include:
    - project: simPod/gitlab-templates
      file: "Php/composer-install.yaml"

default:
    image: registry.gitlab.cdn77.eu/purple-team/ci-cd/images/php-node


.job:
    variables:
        GIT_CLONE_PATH: $CI_BUILDS_DIR/$CI_CONCURRENT_ID/$CI_PROJECT_PATH

.composerJob:
    extends:
        - .job
        - .composer-install
    before_script:
        - !reference [.composer-install-before-script, before_script]
    variables:
        PHP_EXTENSIONS: bcmath curl dom ds intl json mbstring pcov simplexml xml xmlwriter

Coding Standard:
    extends: .composerJob
    script: vendor/bin/phpcs

Composer Normalize:
    extends: .composerJob
    script: composer normalize --dry-run

Infection:
    extends: .composerJob
    script: vendor/bin/infection

Static Analysis (PHPStan):
    extends: .composerJob
    script: vendor/bin/phpstan

Composer Dependency Analyser:
    extends: .composerJob
    script: vendor/bin/composer-dependency-analyser

Tests:
    extends: .composerJob
    script: vendor/bin/phpunit
