<?php declare(strict_types=1);

namespace Generated\Sailor\FlopApi\Types;

class CrossConnectConnectionState
{
    public const Active = 'Active';
    public const CablingDone = 'CablingDone';
    public const DeinstallPending = 'DeinstallPending';
    public const Free = 'Free';
    public const Ordered = 'Ordered';
    public const Precabled = 'Precabled';

    public static function endpoint(): string
    {
        return 'flop';
    }

    public static function config(): string
    {
        return \Safe\realpath(__DIR__ . '/../../../../../sailor.php');
    }
}
