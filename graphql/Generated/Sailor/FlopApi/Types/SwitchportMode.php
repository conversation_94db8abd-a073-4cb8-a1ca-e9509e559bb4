<?php declare(strict_types=1);

namespace Generated\Sailor\FlopApi\Types;

class SwitchportMode
{
    public const Access = 'Access';
    public const Dot1QTunnel = 'Dot1QTunnel';
    public const Down = 'Down';
    public const DynamicAuto = 'DynamicAuto';
    public const Trunk = 'Trunk';
    public const Tunnel = 'Tunnel';

    public static function endpoint(): string
    {
        return 'flop';
    }

    public static function config(): string
    {
        return \Safe\realpath(__DIR__ . '/../../../../../sailor.php');
    }
}
