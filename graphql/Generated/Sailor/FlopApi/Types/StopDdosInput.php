<?php declare(strict_types=1);

namespace Generated\Sailor\FlopApi\Types;

/**
 * @property float|int $bps
 * @property mixed $endAt
 * @property int|string $hurricaneId
 * @property string $hurricaneStatus
 * @property float|int $pps
 */
class StopDdosInput extends \Spawnia\Sailor\ObjectLike
{
    /**
     * @param float|int $bps
     * @param mixed $endAt
     * @param int|string $hurricaneId
     * @param string $hurricaneStatus
     * @param float|int $pps
     */
    public static function make($bps, $endAt, $hurricaneId, $hurricaneStatus, $pps): self
    {
        $instance = new self;

        if ($bps !== self::UNDEFINED) {
            $instance->bps = $bps;
        }
        if ($endAt !== self::UNDEFINED) {
            $instance->endAt = $endAt;
        }
        if ($hurricaneId !== self::UNDEFINED) {
            $instance->hurricaneId = $hurricaneId;
        }
        if ($hurricaneStatus !== self::UNDEFINED) {
            $instance->hurricaneStatus = $hurricaneStatus;
        }
        if ($pps !== self::UNDEFINED) {
            $instance->pps = $pps;
        }

        return $instance;
    }

    protected function converters(): array
    {
        static $converters;

        return $converters ??= [
            'bps' => new \Spawnia\Sailor\Convert\NonNullConverter(new \Spawnia\Sailor\Convert\FloatConverter),
            'endAt' => new \Spawnia\Sailor\Convert\NonNullConverter(new \Spawnia\Sailor\Convert\ScalarConverter),
            'hurricaneId' => new \Spawnia\Sailor\Convert\NonNullConverter(new \Spawnia\Sailor\Convert\IDConverter),
            'hurricaneStatus' => new \Spawnia\Sailor\Convert\NonNullConverter(new \Spawnia\Sailor\Convert\EnumConverter),
            'pps' => new \Spawnia\Sailor\Convert\NonNullConverter(new \Spawnia\Sailor\Convert\FloatConverter),
        ];
    }

    public static function endpoint(): string
    {
        return 'flop';
    }

    public static function config(): string
    {
        return \Safe\realpath(__DIR__ . '/../../../../../sailor.php');
    }
}
