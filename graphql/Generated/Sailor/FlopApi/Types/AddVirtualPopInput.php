<?php declare(strict_types=1);

namespace Generated\Sailor\FlopApi\Types;

/**
 * @property array<int|string|null> $dataCenters
 * @property string $name
 * @property array<string|null> $sourceBgpCommunitiesOriginated
 * @property array<string|null> $sourceBgpCommunitiesExternal
 * @property string|null $color
 * @property string|null $preferredSnmpProxy
 */
class AddVirtualPopInput extends \Spawnia\Sailor\ObjectLike
{
    /**
     * @param array<int|string|null> $dataCenters
     * @param string $name
     * @param array<string|null> $sourceBgpCommunitiesOriginated
     * @param array<string|null> $sourceBgpCommunitiesExternal
     * @param string|null $color
     * @param string|null $preferredSnmpProxy
     */
    public static function make(
        $dataCenters,
        $name,
        $sourceBgpCommunitiesOriginated,
        $sourceBgpCommunitiesExternal,
        $color = 'Special default value that allows <PERSON> to differentiate between explicitly passing null and not passing a value at all.',
        $preferredSnmpProxy = 'Special default value that allows <PERSON> to differentiate between explicitly passing null and not passing a value at all.',
    ): self {
        $instance = new self;

        if ($dataCenters !== self::UNDEFINED) {
            $instance->dataCenters = $dataCenters;
        }
        if ($name !== self::UNDEFINED) {
            $instance->name = $name;
        }
        if ($sourceBgpCommunitiesOriginated !== self::UNDEFINED) {
            $instance->sourceBgpCommunitiesOriginated = $sourceBgpCommunitiesOriginated;
        }
        if ($sourceBgpCommunitiesExternal !== self::UNDEFINED) {
            $instance->sourceBgpCommunitiesExternal = $sourceBgpCommunitiesExternal;
        }
        if ($color !== self::UNDEFINED) {
            $instance->color = $color;
        }
        if ($preferredSnmpProxy !== self::UNDEFINED) {
            $instance->preferredSnmpProxy = $preferredSnmpProxy;
        }

        return $instance;
    }

    protected function converters(): array
    {
        static $converters;

        return $converters ??= [
            'dataCenters' => new \Spawnia\Sailor\Convert\NonNullConverter(new \Spawnia\Sailor\Convert\ListConverter(new \Spawnia\Sailor\Convert\NullConverter(new \Spawnia\Sailor\Convert\IDConverter))),
            'name' => new \Spawnia\Sailor\Convert\NonNullConverter(new \Spawnia\Sailor\Convert\StringConverter),
            'sourceBgpCommunitiesOriginated' => new \Spawnia\Sailor\Convert\NonNullConverter(new \Spawnia\Sailor\Convert\ListConverter(new \Spawnia\Sailor\Convert\NullConverter(new \Spawnia\Sailor\Convert\StringConverter))),
            'sourceBgpCommunitiesExternal' => new \Spawnia\Sailor\Convert\NonNullConverter(new \Spawnia\Sailor\Convert\ListConverter(new \Spawnia\Sailor\Convert\NullConverter(new \Spawnia\Sailor\Convert\StringConverter))),
            'color' => new \Spawnia\Sailor\Convert\NullConverter(new \Spawnia\Sailor\Convert\StringConverter),
            'preferredSnmpProxy' => new \Spawnia\Sailor\Convert\NullConverter(new \Spawnia\Sailor\Convert\EnumConverter),
        ];
    }

    public static function endpoint(): string
    {
        return 'flop';
    }

    public static function config(): string
    {
        return \Safe\realpath(__DIR__ . '/../../../../../sailor.php');
    }
}
