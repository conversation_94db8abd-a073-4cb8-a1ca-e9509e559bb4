<?php declare(strict_types=1);

namespace Generated\Sailor\FlopApi\Types;

/**
 * @property bool|null $active
 */
class ExternalMaintenanceFilterInput extends \Spawnia\Sailor\ObjectLike
{
    /**
     * @param bool|null $active
     */
    public static function make(
        $active = 'Special default value that allows <PERSON> to differentiate between explicitly passing null and not passing a value at all.',
    ): self {
        $instance = new self;

        if ($active !== self::UNDEFINED) {
            $instance->active = $active;
        }

        return $instance;
    }

    protected function converters(): array
    {
        static $converters;

        return $converters ??= [
            'active' => new \Spawnia\Sailor\Convert\NullConverter(new \Spawnia\Sailor\Convert\BooleanConverter),
        ];
    }

    public static function endpoint(): string
    {
        return 'flop';
    }

    public static function config(): string
    {
        return \Safe\realpath(__DIR__ . '/../../../../../sailor.php');
    }
}
