<?php declare(strict_types=1);

namespace Generated\Sailor\FlopApi\Types;

class BirdPrefixApprovalStatus
{
    public const ApprovedByNetworkAdmin = 'ApprovedByNetworkAdmin';
    public const LoaValidated = 'LoaValidated';
    public const NotApproved = 'NotApproved';
    public const ValidatingLoa = 'ValidatingLoa';

    public static function endpoint(): string
    {
        return 'flop';
    }

    public static function config(): string
    {
        return \Safe\realpath(__DIR__ . '/../../../../../sailor.php');
    }
}
