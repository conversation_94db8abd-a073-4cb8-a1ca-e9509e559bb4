<?php declare(strict_types=1);

namespace Generated\Sailor\FlopApi\Types;

class PortLldpStatusInput
{
    public const Disabled = 'Disabled';
    public const DisabledOrPartiallyDisabled = 'DisabledOrPartiallyDisabled';
    public const Enabled = 'Enabled';
    public const RxOnly = 'RxOnly';
    public const TxOnly = 'TxOnly';
    public const TxOrRx = 'TxOrRx';

    public static function endpoint(): string
    {
        return 'flop';
    }

    public static function config(): string
    {
        return \Safe\realpath(__DIR__ . '/../../../../../sailor.php');
    }
}
