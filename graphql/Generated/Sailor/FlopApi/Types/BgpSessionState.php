<?php declare(strict_types=1);

namespace Generated\Sailor\FlopApi\Types;

class BgpSessionState
{
    public const Active = 'Active';
    public const AdministrativelyShutDown = 'AdministrativelyShutDown';
    public const Connect = 'Connect';
    public const Established = 'Established';
    public const Idle = 'Idle';
    public const OpenConfirm = 'OpenConfirm';
    public const OpenSent = 'OpenSent';

    public static function endpoint(): string
    {
        return 'flop';
    }

    public static function config(): string
    {
        return \Safe\realpath(__DIR__ . '/../../../../../sailor.php');
    }
}
