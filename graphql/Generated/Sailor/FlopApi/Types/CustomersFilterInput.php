<?php declare(strict_types=1);

namespace Generated\Sailor\FlopApi\Types;

/**
 * @property string|null $name
 * @property array<int|string>|null $ids
 * @property bool|null $isOwner
 * @property string|null $origin
 */
class CustomersFilterInput extends \Spawnia\Sailor\ObjectLike
{
    /**
     * @param string|null $name
     * @param array<int|string>|null $ids
     * @param bool|null $isOwner
     * @param string|null $origin
     */
    public static function make(
        $name = 'Special default value that allows <PERSON> to differentiate between explicitly passing null and not passing a value at all.',
        $ids = 'Special default value that allows <PERSON> to differentiate between explicitly passing null and not passing a value at all.',
        $isOwner = 'Special default value that allows <PERSON> to differentiate between explicitly passing null and not passing a value at all.',
        $origin = 'Special default value that allows <PERSON> to differentiate between explicitly passing null and not passing a value at all.',
    ): self {
        $instance = new self;

        if ($name !== self::UNDEFINED) {
            $instance->name = $name;
        }
        if ($ids !== self::UNDEFINED) {
            $instance->ids = $ids;
        }
        if ($isOwner !== self::UNDEFINED) {
            $instance->isOwner = $isOwner;
        }
        if ($origin !== self::UNDEFINED) {
            $instance->origin = $origin;
        }

        return $instance;
    }

    protected function converters(): array
    {
        static $converters;

        return $converters ??= [
            'name' => new \Spawnia\Sailor\Convert\NullConverter(new \Spawnia\Sailor\Convert\StringConverter),
            'ids' => new \Spawnia\Sailor\Convert\NullConverter(new \Spawnia\Sailor\Convert\ListConverter(new \Spawnia\Sailor\Convert\NonNullConverter(new \Spawnia\Sailor\Convert\IDConverter))),
            'isOwner' => new \Spawnia\Sailor\Convert\NullConverter(new \Spawnia\Sailor\Convert\BooleanConverter),
            'origin' => new \Spawnia\Sailor\Convert\NullConverter(new \Spawnia\Sailor\Convert\EnumConverter),
        ];
    }

    public static function endpoint(): string
    {
        return 'flop';
    }

    public static function config(): string
    {
        return \Safe\realpath(__DIR__ . '/../../../../../sailor.php');
    }
}
