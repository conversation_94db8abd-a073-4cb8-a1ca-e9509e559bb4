<?php declare(strict_types=1);

namespace Generated\Sailor\FlopApi\Types;

class HurricaneDdosStatus
{
    public const Error = 'Error';
    public const NotResolved = 'NotResolved';
    public const ResolvedAdvanced = 'ResolvedAdvanced';
    public const ResolvedManual = 'ResolvedManual';
    public const ResolvedSimple = 'ResolvedSimple';

    public static function endpoint(): string
    {
        return 'flop';
    }

    public static function config(): string
    {
        return \Safe\realpath(__DIR__ . '/../../../../../sailor.php');
    }
}
