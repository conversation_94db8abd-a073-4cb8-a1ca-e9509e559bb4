<?php declare(strict_types=1);

namespace Generated\Sailor\FlopApi\Types;

/**
 * @property int|string $routerId
 * @property mixed $ipV4
 * @property mixed $ipV6
 */
class AddBirdRouterBgpSettingInput extends \Spawnia\Sailor\ObjectLike
{
    /**
     * @param int|string $routerId
     * @param mixed $ipV4
     * @param mixed $ipV6
     */
    public static function make($routerId, $ipV4, $ipV6): self
    {
        $instance = new self;

        if ($routerId !== self::UNDEFINED) {
            $instance->routerId = $routerId;
        }
        if ($ipV4 !== self::UNDEFINED) {
            $instance->ipV4 = $ipV4;
        }
        if ($ipV6 !== self::UNDEFINED) {
            $instance->ipV6 = $ipV6;
        }

        return $instance;
    }

    protected function converters(): array
    {
        static $converters;

        return $converters ??= [
            'routerId' => new \Spawnia\Sailor\Convert\NonNullConverter(new \Spawnia\Sailor\Convert\IDConverter),
            'ipV4' => new \Spawnia\Sailor\Convert\NonNullConverter(new \Spawnia\Sailor\Convert\ScalarConverter),
            'ipV6' => new \Spawnia\Sailor\Convert\NonNullConverter(new \Spawnia\Sailor\Convert\ScalarConverter),
        ];
    }

    public static function endpoint(): string
    {
        return 'flop';
    }

    public static function config(): string
    {
        return \Safe\realpath(__DIR__ . '/../../../../../sailor.php');
    }
}
