<?php declare(strict_types=1);

namespace Generated\Sailor\FlopApi\Types;

/**
 * @property array<int|string>|null $continents
 * @property array<int|string>|null $customers
 * @property array<int|string>|null $dataCenters
 * @property array<int|string>|null $gelIds
 * @property array<int|string>|null $ids
 * @property string|null $name
 * @property array<mixed>|null $macAddresses
 */
class ServersFilterInput extends \Spawnia\Sailor\ObjectLike
{
    /**
     * @param array<int|string>|null $continents
     * @param array<int|string>|null $customers
     * @param array<int|string>|null $dataCenters
     * @param array<int|string>|null $gelIds
     * @param array<int|string>|null $ids
     * @param string|null $name
     * @param array<mixed>|null $macAddresses
     */
    public static function make(
        $continents = 'Special default value that allows <PERSON> to differentiate between explicitly passing null and not passing a value at all.',
        $customers = 'Special default value that allows <PERSON> to differentiate between explicitly passing null and not passing a value at all.',
        $dataCenters = 'Special default value that allows <PERSON> to differentiate between explicitly passing null and not passing a value at all.',
        $gelIds = 'Special default value that allows <PERSON> to differentiate between explicitly passing null and not passing a value at all.',
        $ids = 'Special default value that allows Sailor to differentiate between explicitly passing null and not passing a value at all.',
        $name = 'Special default value that allows Sailor to differentiate between explicitly passing null and not passing a value at all.',
        $macAddresses = 'Special default value that allows Sailor to differentiate between explicitly passing null and not passing a value at all.',
    ): self {
        $instance = new self;

        if ($continents !== self::UNDEFINED) {
            $instance->continents = $continents;
        }
        if ($customers !== self::UNDEFINED) {
            $instance->customers = $customers;
        }
        if ($dataCenters !== self::UNDEFINED) {
            $instance->dataCenters = $dataCenters;
        }
        if ($gelIds !== self::UNDEFINED) {
            $instance->gelIds = $gelIds;
        }
        if ($ids !== self::UNDEFINED) {
            $instance->ids = $ids;
        }
        if ($name !== self::UNDEFINED) {
            $instance->name = $name;
        }
        if ($macAddresses !== self::UNDEFINED) {
            $instance->macAddresses = $macAddresses;
        }

        return $instance;
    }

    protected function converters(): array
    {
        static $converters;

        return $converters ??= [
            'continents' => new \Spawnia\Sailor\Convert\NullConverter(new \Spawnia\Sailor\Convert\ListConverter(new \Spawnia\Sailor\Convert\NonNullConverter(new \Spawnia\Sailor\Convert\IDConverter))),
            'customers' => new \Spawnia\Sailor\Convert\NullConverter(new \Spawnia\Sailor\Convert\ListConverter(new \Spawnia\Sailor\Convert\NonNullConverter(new \Spawnia\Sailor\Convert\IDConverter))),
            'dataCenters' => new \Spawnia\Sailor\Convert\NullConverter(new \Spawnia\Sailor\Convert\ListConverter(new \Spawnia\Sailor\Convert\NonNullConverter(new \Spawnia\Sailor\Convert\IDConverter))),
            'gelIds' => new \Spawnia\Sailor\Convert\NullConverter(new \Spawnia\Sailor\Convert\ListConverter(new \Spawnia\Sailor\Convert\NonNullConverter(new \Spawnia\Sailor\Convert\IDConverter))),
            'ids' => new \Spawnia\Sailor\Convert\NullConverter(new \Spawnia\Sailor\Convert\ListConverter(new \Spawnia\Sailor\Convert\NonNullConverter(new \Spawnia\Sailor\Convert\IDConverter))),
            'name' => new \Spawnia\Sailor\Convert\NullConverter(new \Spawnia\Sailor\Convert\StringConverter),
            'macAddresses' => new \Spawnia\Sailor\Convert\NullConverter(new \Spawnia\Sailor\Convert\ListConverter(new \Spawnia\Sailor\Convert\NonNullConverter(new \Spawnia\Sailor\Convert\ScalarConverter))),
        ];
    }

    public static function endpoint(): string
    {
        return 'flop';
    }

    public static function config(): string
    {
        return \Safe\realpath(__DIR__ . '/../../../../../sailor.php');
    }
}
