<?php declare(strict_types=1);

namespace Generated\Sailor\FlopApi\Types;

/**
 * @property int|null $accessModeVlan
 * @property string|null $administrativeStatus
 * @property array<string>|null $connectedIps
 * @property string|null $customerName
 * @property string|null $description
 * @property int|string|null $deviceId
 * @property string|null $deviceIp
 * @property string|null $deviceName
 * @property array<int|string>|null $ids
 * @property int|null $index
 * @property array<int>|null $indices
 * @property string|null $lldpStatus
 * @property string|null $name
 * @property string|null $operationalStatus
 * @property string|null $serviceName
 * @property string|null $speed
 * @property string|null $topologyType
 */
class PortsFilterInput extends \Spawnia\Sailor\ObjectLike
{
    /**
     * @param int|null $accessModeVlan
     * @param string|null $administrativeStatus
     * @param array<string>|null $connectedIps
     * @param string|null $customerName
     * @param string|null $description
     * @param int|string|null $deviceId
     * @param string|null $deviceIp
     * @param string|null $deviceName
     * @param array<int|string>|null $ids
     * @param int|null $index
     * @param array<int>|null $indices
     * @param string|null $lldpStatus
     * @param string|null $name
     * @param string|null $operationalStatus
     * @param string|null $serviceName
     * @param string|null $speed
     * @param string|null $topologyType
     */
    public static function make(
        $accessModeVlan = 'Special default value that allows Sailor to differentiate between explicitly passing null and not passing a value at all.',
        $administrativeStatus = 'Special default value that allows Sailor to differentiate between explicitly passing null and not passing a value at all.',
        $connectedIps = 'Special default value that allows Sailor to differentiate between explicitly passing null and not passing a value at all.',
        $customerName = 'Special default value that allows Sailor to differentiate between explicitly passing null and not passing a value at all.',
        $description = 'Special default value that allows Sailor to differentiate between explicitly passing null and not passing a value at all.',
        $deviceId = 'Special default value that allows Sailor to differentiate between explicitly passing null and not passing a value at all.',
        $deviceIp = 'Special default value that allows Sailor to differentiate between explicitly passing null and not passing a value at all.',
        $deviceName = 'Special default value that allows Sailor to differentiate between explicitly passing null and not passing a value at all.',
        $ids = 'Special default value that allows Sailor to differentiate between explicitly passing null and not passing a value at all.',
        $index = 'Special default value that allows Sailor to differentiate between explicitly passing null and not passing a value at all.',
        $indices = 'Special default value that allows Sailor to differentiate between explicitly passing null and not passing a value at all.',
        $lldpStatus = 'Special default value that allows Sailor to differentiate between explicitly passing null and not passing a value at all.',
        $name = 'Special default value that allows Sailor to differentiate between explicitly passing null and not passing a value at all.',
        $operationalStatus = 'Special default value that allows Sailor to differentiate between explicitly passing null and not passing a value at all.',
        $serviceName = 'Special default value that allows Sailor to differentiate between explicitly passing null and not passing a value at all.',
        $speed = 'Special default value that allows Sailor to differentiate between explicitly passing null and not passing a value at all.',
        $topologyType = 'Special default value that allows Sailor to differentiate between explicitly passing null and not passing a value at all.',
    ): self {
        $instance = new self;

        if ($accessModeVlan !== self::UNDEFINED) {
            $instance->accessModeVlan = $accessModeVlan;
        }
        if ($administrativeStatus !== self::UNDEFINED) {
            $instance->administrativeStatus = $administrativeStatus;
        }
        if ($connectedIps !== self::UNDEFINED) {
            $instance->connectedIps = $connectedIps;
        }
        if ($customerName !== self::UNDEFINED) {
            $instance->customerName = $customerName;
        }
        if ($description !== self::UNDEFINED) {
            $instance->description = $description;
        }
        if ($deviceId !== self::UNDEFINED) {
            $instance->deviceId = $deviceId;
        }
        if ($deviceIp !== self::UNDEFINED) {
            $instance->deviceIp = $deviceIp;
        }
        if ($deviceName !== self::UNDEFINED) {
            $instance->deviceName = $deviceName;
        }
        if ($ids !== self::UNDEFINED) {
            $instance->ids = $ids;
        }
        if ($index !== self::UNDEFINED) {
            $instance->index = $index;
        }
        if ($indices !== self::UNDEFINED) {
            $instance->indices = $indices;
        }
        if ($lldpStatus !== self::UNDEFINED) {
            $instance->lldpStatus = $lldpStatus;
        }
        if ($name !== self::UNDEFINED) {
            $instance->name = $name;
        }
        if ($operationalStatus !== self::UNDEFINED) {
            $instance->operationalStatus = $operationalStatus;
        }
        if ($serviceName !== self::UNDEFINED) {
            $instance->serviceName = $serviceName;
        }
        if ($speed !== self::UNDEFINED) {
            $instance->speed = $speed;
        }
        if ($topologyType !== self::UNDEFINED) {
            $instance->topologyType = $topologyType;
        }

        return $instance;
    }

    protected function converters(): array
    {
        static $converters;

        return $converters ??= [
            'accessModeVlan' => new \Spawnia\Sailor\Convert\NullConverter(new \Spawnia\Sailor\Convert\IntConverter),
            'administrativeStatus' => new \Spawnia\Sailor\Convert\NullConverter(new \Spawnia\Sailor\Convert\EnumConverter),
            'connectedIps' => new \Spawnia\Sailor\Convert\NullConverter(new \Spawnia\Sailor\Convert\ListConverter(new \Spawnia\Sailor\Convert\NonNullConverter(new \Spawnia\Sailor\Convert\StringConverter))),
            'customerName' => new \Spawnia\Sailor\Convert\NullConverter(new \Spawnia\Sailor\Convert\StringConverter),
            'description' => new \Spawnia\Sailor\Convert\NullConverter(new \Spawnia\Sailor\Convert\StringConverter),
            'deviceId' => new \Spawnia\Sailor\Convert\NullConverter(new \Spawnia\Sailor\Convert\IDConverter),
            'deviceIp' => new \Spawnia\Sailor\Convert\NullConverter(new \Spawnia\Sailor\Convert\StringConverter),
            'deviceName' => new \Spawnia\Sailor\Convert\NullConverter(new \Spawnia\Sailor\Convert\StringConverter),
            'ids' => new \Spawnia\Sailor\Convert\NullConverter(new \Spawnia\Sailor\Convert\ListConverter(new \Spawnia\Sailor\Convert\NonNullConverter(new \Spawnia\Sailor\Convert\IDConverter))),
            'index' => new \Spawnia\Sailor\Convert\NullConverter(new \Spawnia\Sailor\Convert\IntConverter),
            'indices' => new \Spawnia\Sailor\Convert\NullConverter(new \Spawnia\Sailor\Convert\ListConverter(new \Spawnia\Sailor\Convert\NonNullConverter(new \Spawnia\Sailor\Convert\IntConverter))),
            'lldpStatus' => new \Spawnia\Sailor\Convert\NullConverter(new \Spawnia\Sailor\Convert\EnumConverter),
            'name' => new \Spawnia\Sailor\Convert\NullConverter(new \Spawnia\Sailor\Convert\StringConverter),
            'operationalStatus' => new \Spawnia\Sailor\Convert\NullConverter(new \Spawnia\Sailor\Convert\EnumConverter),
            'serviceName' => new \Spawnia\Sailor\Convert\NullConverter(new \Spawnia\Sailor\Convert\StringConverter),
            'speed' => new \Spawnia\Sailor\Convert\NullConverter(new \Spawnia\Sailor\Convert\StringConverter),
            'topologyType' => new \Spawnia\Sailor\Convert\NullConverter(new \Spawnia\Sailor\Convert\EnumConverter),
        ];
    }

    public static function endpoint(): string
    {
        return 'flop';
    }

    public static function config(): string
    {
        return \Safe\realpath(__DIR__ . '/../../../../../sailor.php');
    }
}
