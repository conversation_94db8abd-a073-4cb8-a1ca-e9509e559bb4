<?php declare(strict_types=1);

namespace Generated\Sailor\FlopApi\Types;

class ServerIdType
{
    public const FLOP = 'FLOP';
    public const Flop = 'Flop';
    public const GEL = 'GEL';
    public const Gel = 'Gel';

    public static function endpoint(): string
    {
        return 'flop';
    }

    public static function config(): string
    {
        return \Safe\realpath(__DIR__ . '/../../../../../sailor.php');
    }
}
