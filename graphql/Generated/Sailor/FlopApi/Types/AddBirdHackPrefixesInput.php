<?php declare(strict_types=1);

namespace Generated\Sailor\FlopApi\Types;

/**
 * @property array<string> $additionalBgpCommunities
 * @property string $approvalStatus
 * @property mixed $nextHop
 * @property int|string $prefixListId
 * @property array<mixed> $prefixes
 */
class AddBirdHackPrefixesInput extends \Spawnia\Sailor\ObjectLike
{
    /**
     * @param array<string> $additionalBgpCommunities
     * @param string $approvalStatus
     * @param mixed $nextHop
     * @param int|string $prefixListId
     * @param array<mixed> $prefixes
     */
    public static function make($additionalBgpCommunities, $approvalStatus, $nextHop, $prefixListId, $prefixes): self
    {
        $instance = new self;

        if ($additionalBgpCommunities !== self::UNDEFINED) {
            $instance->additionalBgpCommunities = $additionalBgpCommunities;
        }
        if ($approvalStatus !== self::UNDEFINED) {
            $instance->approvalStatus = $approvalStatus;
        }
        if ($nextHop !== self::UNDEFINED) {
            $instance->nextHop = $nextHop;
        }
        if ($prefixListId !== self::UNDEFINED) {
            $instance->prefixListId = $prefixListId;
        }
        if ($prefixes !== self::UNDEFINED) {
            $instance->prefixes = $prefixes;
        }

        return $instance;
    }

    protected function converters(): array
    {
        static $converters;

        return $converters ??= [
            'additionalBgpCommunities' => new \Spawnia\Sailor\Convert\NonNullConverter(new \Spawnia\Sailor\Convert\ListConverter(new \Spawnia\Sailor\Convert\NonNullConverter(new \Spawnia\Sailor\Convert\StringConverter))),
            'approvalStatus' => new \Spawnia\Sailor\Convert\NonNullConverter(new \Spawnia\Sailor\Convert\EnumConverter),
            'nextHop' => new \Spawnia\Sailor\Convert\NonNullConverter(new \Spawnia\Sailor\Convert\ScalarConverter),
            'prefixListId' => new \Spawnia\Sailor\Convert\NonNullConverter(new \Spawnia\Sailor\Convert\IDConverter),
            'prefixes' => new \Spawnia\Sailor\Convert\NonNullConverter(new \Spawnia\Sailor\Convert\ListConverter(new \Spawnia\Sailor\Convert\NonNullConverter(new \Spawnia\Sailor\Convert\ScalarConverter))),
        ];
    }

    public static function endpoint(): string
    {
        return 'flop';
    }

    public static function config(): string
    {
        return \Safe\realpath(__DIR__ . '/../../../../../sailor.php');
    }
}
