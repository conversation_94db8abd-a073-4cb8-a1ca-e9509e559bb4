<?php declare(strict_types=1);

namespace Generated\Sailor\FlopApi\Types;

class SflowAggregationResultSort
{
    public const Avg = 'Avg';
    public const LatencyAvg = 'LatencyAvg';
    public const LossAvg = 'LossAvg';
    public const Max = 'Max';
    public const Min = 'Min';
    public const Pctl95 = 'Pctl95';
    public const Pctl99 = 'Pctl99';
    public const Sum = 'Sum';

    public static function endpoint(): string
    {
        return 'flop';
    }

    public static function config(): string
    {
        return \Safe\realpath(__DIR__ . '/../../../../../sailor.php');
    }
}
