<?php declare(strict_types=1);

namespace Generated\Sailor\FlopApi\Types;

/**
 * @property array<string> $additionalBgpCommunities
 * @property int|string $asn
 * @property int|string $customerId
 * @property array<int|string> $dataCenterIds
 * @property string $name
 * @property string|null $note
 */
class EditBirdHackPrefixListInput extends \Spawnia\Sailor\ObjectLike
{
    /**
     * @param array<string> $additionalBgpCommunities
     * @param int|string $asn
     * @param int|string $customerId
     * @param array<int|string> $dataCenterIds
     * @param string $name
     * @param string|null $note
     */
    public static function make(
        $additionalBgpCommunities,
        $asn,
        $customerId,
        $dataCenterIds,
        $name,
        $note = 'Special default value that allows <PERSON> to differentiate between explicitly passing null and not passing a value at all.',
    ): self {
        $instance = new self;

        if ($additionalBgpCommunities !== self::UNDEFINED) {
            $instance->additionalBgpCommunities = $additionalBgpCommunities;
        }
        if ($asn !== self::UNDEFINED) {
            $instance->asn = $asn;
        }
        if ($customerId !== self::UNDEFINED) {
            $instance->customerId = $customerId;
        }
        if ($dataCenterIds !== self::UNDEFINED) {
            $instance->dataCenterIds = $dataCenterIds;
        }
        if ($name !== self::UNDEFINED) {
            $instance->name = $name;
        }
        if ($note !== self::UNDEFINED) {
            $instance->note = $note;
        }

        return $instance;
    }

    protected function converters(): array
    {
        static $converters;

        return $converters ??= [
            'additionalBgpCommunities' => new \Spawnia\Sailor\Convert\NonNullConverter(new \Spawnia\Sailor\Convert\ListConverter(new \Spawnia\Sailor\Convert\NonNullConverter(new \Spawnia\Sailor\Convert\StringConverter))),
            'asn' => new \Spawnia\Sailor\Convert\NonNullConverter(new \Spawnia\Sailor\Convert\IDConverter),
            'customerId' => new \Spawnia\Sailor\Convert\NonNullConverter(new \Spawnia\Sailor\Convert\IDConverter),
            'dataCenterIds' => new \Spawnia\Sailor\Convert\NonNullConverter(new \Spawnia\Sailor\Convert\ListConverter(new \Spawnia\Sailor\Convert\NonNullConverter(new \Spawnia\Sailor\Convert\IDConverter))),
            'name' => new \Spawnia\Sailor\Convert\NonNullConverter(new \Spawnia\Sailor\Convert\StringConverter),
            'note' => new \Spawnia\Sailor\Convert\NullConverter(new \Spawnia\Sailor\Convert\StringConverter),
        ];
    }

    public static function endpoint(): string
    {
        return 'flop';
    }

    public static function config(): string
    {
        return \Safe\realpath(__DIR__ . '/../../../../../sailor.php');
    }
}
