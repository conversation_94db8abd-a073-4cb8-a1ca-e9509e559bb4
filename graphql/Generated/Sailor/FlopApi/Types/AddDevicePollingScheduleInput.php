<?php declare(strict_types=1);

namespace Generated\Sailor\FlopApi\Types;

/**
 * @property string $cronExpression
 * @property string $dataType
 */
class AddDevicePollingScheduleInput extends \Spawnia\Sailor\ObjectLike
{
    /**
     * @param string $cronExpression
     * @param string $dataType
     */
    public static function make($cronExpression, $dataType): self
    {
        $instance = new self;

        if ($cronExpression !== self::UNDEFINED) {
            $instance->cronExpression = $cronExpression;
        }
        if ($dataType !== self::UNDEFINED) {
            $instance->dataType = $dataType;
        }

        return $instance;
    }

    protected function converters(): array
    {
        static $converters;

        return $converters ??= [
            'cronExpression' => new \Spawnia\Sailor\Convert\NonNullConverter(new \Spawnia\Sailor\Convert\StringConverter),
            'dataType' => new \Spawnia\Sailor\Convert\NonNullConverter(new \Spawnia\Sailor\Convert\StringConverter),
        ];
    }

    public static function endpoint(): string
    {
        return 'flop';
    }

    public static function config(): string
    {
        return \Safe\realpath(__DIR__ . '/../../../../../sailor.php');
    }
}
