<?php declare(strict_types=1);

namespace Generated\Sailor\FlopApi\Types;

class ContractType
{
    public const CDN77 = 'CDN77';
    public const Cache = 'Cache';
    public const IX = 'IX';
    public const PNI = 'PNI';
    public const Transit = 'Transit';

    public static function endpoint(): string
    {
        return 'flop';
    }

    public static function config(): string
    {
        return \Safe\realpath(__DIR__ . '/../../../../../sailor.php');
    }
}
