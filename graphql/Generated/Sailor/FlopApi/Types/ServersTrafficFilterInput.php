<?php declare(strict_types=1);

namespace Generated\Sailor\FlopApi\Types;

/**
 * @property mixed $from
 * @property int $period
 * @property array<\Generated\Sailor\FlopApi\Types\ServerIdFilterInput> $serverIds
 * @property mixed $to
 * @property \Generated\Sailor\FlopApi\Types\CustomerIdFilterInput|null $customerId
 */
class ServersTrafficFilterInput extends \Spawnia\Sailor\ObjectLike
{
    /**
     * @param mixed $from
     * @param int $period
     * @param array<\Generated\Sailor\FlopApi\Types\ServerIdFilterInput> $serverIds
     * @param mixed $to
     * @param \Generated\Sailor\FlopApi\Types\CustomerIdFilterInput|null $customerId
     */
    public static function make(
        $from,
        $period,
        $serverIds,
        $to,
        $customerId = 'Special default value that allows <PERSON> to differentiate between explicitly passing null and not passing a value at all.',
    ): self {
        $instance = new self;

        if ($from !== self::UNDEFINED) {
            $instance->from = $from;
        }
        if ($period !== self::UNDEFINED) {
            $instance->period = $period;
        }
        if ($serverIds !== self::UNDEFINED) {
            $instance->serverIds = $serverIds;
        }
        if ($to !== self::UNDEFINED) {
            $instance->to = $to;
        }
        if ($customerId !== self::UNDEFINED) {
            $instance->customerId = $customerId;
        }

        return $instance;
    }

    protected function converters(): array
    {
        static $converters;

        return $converters ??= [
            'from' => new \Spawnia\Sailor\Convert\NonNullConverter(new \Spawnia\Sailor\Convert\ScalarConverter),
            'period' => new \Spawnia\Sailor\Convert\NonNullConverter(new \Spawnia\Sailor\Convert\IntConverter),
            'serverIds' => new \Spawnia\Sailor\Convert\NonNullConverter(new \Spawnia\Sailor\Convert\ListConverter(new \Spawnia\Sailor\Convert\NonNullConverter(new \Generated\Sailor\FlopApi\Types\ServerIdFilterInput))),
            'to' => new \Spawnia\Sailor\Convert\NonNullConverter(new \Spawnia\Sailor\Convert\ScalarConverter),
            'customerId' => new \Spawnia\Sailor\Convert\NullConverter(new \Generated\Sailor\FlopApi\Types\CustomerIdFilterInput),
        ];
    }

    public static function endpoint(): string
    {
        return 'flop';
    }

    public static function config(): string
    {
        return \Safe\realpath(__DIR__ . '/../../../../../sailor.php');
    }
}
