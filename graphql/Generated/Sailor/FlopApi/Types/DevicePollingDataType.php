<?php declare(strict_types=1);

namespace Generated\Sailor\FlopApi\Types;

class DevicePollingDataType
{
    public const Arp = 'Arp';
    public const BgpSessions = 'BgpSessions';
    public const DeviceVirtualIps = 'DeviceVirtualIps';
    public const FdbLldpRecords = 'FdbLldpRecords';
    public const HardwareOnlyLag = 'HardwareOnlyLag';
    public const Inventory = 'Inventory';
    public const LldpPortStatuses = 'LldpPortStatuses';
    public const LoadBalancerContext = 'LoadBalancerContext';
    public const LoadBalancerCounters = 'LoadBalancerCounters';
    public const MacAddressTableAgingTime = 'MacAddressTableAgingTime';
    public const Mlag = 'Mlag';
    public const ModelName = 'ModelName';
    public const Name = 'Name';
    public const OperatingSystem = 'OperatingSystem';
    public const PortIps = 'PortIps';
    public const Ports = 'Ports';
    public const Sensor = 'Sensor';
    public const SensorPhysical = 'SensorPhysical';
    public const SerialNumber = 'SerialNumber';
    public const StaticRoutes = 'StaticRoutes';
    public const SystemCoolingStatus = 'SystemCoolingStatus';
    public const SystemDate = 'SystemDate';
    public const SystemTemperatureStatus = 'SystemTemperatureStatus';
    public const Uptime = 'Uptime';
    public const Version = 'Version';
    public const Vlans = 'Vlans';
    public const VxlanConfigSanity = 'VxlanConfigSanity';

    public static function endpoint(): string
    {
        return 'flop';
    }

    public static function config(): string
    {
        return \Safe\realpath(__DIR__ . '/../../../../../sailor.php');
    }
}
