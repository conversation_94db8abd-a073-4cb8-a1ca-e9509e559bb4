<?php declare(strict_types=1);

namespace Generated\Sailor\FlopApi\Types;

/**
 * @property array<string> $contactEmails
 * @property string $name
 */
class EditCrossConnectContactInput extends \Spawnia\Sailor\ObjectLike
{
    /**
     * @param array<string> $contactEmails
     * @param string $name
     */
    public static function make($contactEmails, $name): self
    {
        $instance = new self;

        if ($contactEmails !== self::UNDEFINED) {
            $instance->contactEmails = $contactEmails;
        }
        if ($name !== self::UNDEFINED) {
            $instance->name = $name;
        }

        return $instance;
    }

    protected function converters(): array
    {
        static $converters;

        return $converters ??= [
            'contactEmails' => new \Spawnia\Sailor\Convert\NonNullConverter(new \Spawnia\Sailor\Convert\ListConverter(new \Spawnia\Sailor\Convert\NonNullConverter(new \Spawnia\Sailor\Convert\StringConverter))),
            'name' => new \Spawnia\Sailor\Convert\NonNullConverter(new \Spawnia\Sailor\Convert\StringConverter),
        ];
    }

    public static function endpoint(): string
    {
        return 'flop';
    }

    public static function config(): string
    {
        return \Safe\realpath(__DIR__ . '/../../../../../sailor.php');
    }
}
