<?php declare(strict_types=1);

namespace Generated\Sailor\FlopApi\Types;

/**
 * @property bool $applyToLastCommitment
 * @property string $bandwidth95PercentileMetric
 * @property bool $billable
 * @property mixed $cdr
 * @property int|string $customer
 * @property string $name
 * @property float|int $priceAmount
 * @property string $priceCurrency
 * @property string $seriesColor
 * @property mixed|null $expiresAt
 */
class EditContractInput extends \Spawnia\Sailor\ObjectLike
{
    /**
     * @param bool $applyToLastCommitment
     * @param string $bandwidth95PercentileMetric
     * @param bool $billable
     * @param mixed $cdr
     * @param int|string $customer
     * @param string $name
     * @param float|int $priceAmount
     * @param string $priceCurrency
     * @param string $seriesColor
     * @param mixed|null $expiresAt
     */
    public static function make(
        $applyToLastCommitment,
        $bandwidth95PercentileMetric,
        $billable,
        $cdr,
        $customer,
        $name,
        $priceAmount,
        $priceCurrency,
        $seriesColor,
        $expiresAt = 'Special default value that allows <PERSON> to differentiate between explicitly passing null and not passing a value at all.',
    ): self {
        $instance = new self;

        if ($applyToLastCommitment !== self::UNDEFINED) {
            $instance->applyToLastCommitment = $applyToLastCommitment;
        }
        if ($bandwidth95PercentileMetric !== self::UNDEFINED) {
            $instance->bandwidth95PercentileMetric = $bandwidth95PercentileMetric;
        }
        if ($billable !== self::UNDEFINED) {
            $instance->billable = $billable;
        }
        if ($cdr !== self::UNDEFINED) {
            $instance->cdr = $cdr;
        }
        if ($customer !== self::UNDEFINED) {
            $instance->customer = $customer;
        }
        if ($name !== self::UNDEFINED) {
            $instance->name = $name;
        }
        if ($priceAmount !== self::UNDEFINED) {
            $instance->priceAmount = $priceAmount;
        }
        if ($priceCurrency !== self::UNDEFINED) {
            $instance->priceCurrency = $priceCurrency;
        }
        if ($seriesColor !== self::UNDEFINED) {
            $instance->seriesColor = $seriesColor;
        }
        if ($expiresAt !== self::UNDEFINED) {
            $instance->expiresAt = $expiresAt;
        }

        return $instance;
    }

    protected function converters(): array
    {
        static $converters;

        return $converters ??= [
            'applyToLastCommitment' => new \Spawnia\Sailor\Convert\NonNullConverter(new \Spawnia\Sailor\Convert\BooleanConverter),
            'bandwidth95PercentileMetric' => new \Spawnia\Sailor\Convert\NonNullConverter(new \Spawnia\Sailor\Convert\EnumConverter),
            'billable' => new \Spawnia\Sailor\Convert\NonNullConverter(new \Spawnia\Sailor\Convert\BooleanConverter),
            'cdr' => new \Spawnia\Sailor\Convert\NonNullConverter(new \Spawnia\Sailor\Convert\ScalarConverter),
            'customer' => new \Spawnia\Sailor\Convert\NonNullConverter(new \Spawnia\Sailor\Convert\IDConverter),
            'name' => new \Spawnia\Sailor\Convert\NonNullConverter(new \Spawnia\Sailor\Convert\StringConverter),
            'priceAmount' => new \Spawnia\Sailor\Convert\NonNullConverter(new \Spawnia\Sailor\Convert\FloatConverter),
            'priceCurrency' => new \Spawnia\Sailor\Convert\NonNullConverter(new \Spawnia\Sailor\Convert\StringConverter),
            'seriesColor' => new \Spawnia\Sailor\Convert\NonNullConverter(new \Spawnia\Sailor\Convert\StringConverter),
            'expiresAt' => new \Spawnia\Sailor\Convert\NullConverter(new \Spawnia\Sailor\Convert\ScalarConverter),
        ];
    }

    public static function endpoint(): string
    {
        return 'flop';
    }

    public static function config(): string
    {
        return \Safe\realpath(__DIR__ . '/../../../../../sailor.php');
    }
}
