<?php declare(strict_types=1);

namespace Generated\Sailor\FlopApi\Types;

/**
 * @property int|string|null $aggregateId
 * @property string|null $context
 * @property int|string|null $entityId
 * @property string|null $eventName
 * @property string|null $originator
 * @property string|null $originatorType
 * @property string|null $payload
 * @property mixed|null $recordedAfter
 */
class EventLogFilterInput extends \Spawnia\Sailor\ObjectLike
{
    /**
     * @param int|string|null $aggregateId
     * @param string|null $context
     * @param int|string|null $entityId
     * @param string|null $eventName
     * @param string|null $originator
     * @param string|null $originatorType
     * @param string|null $payload
     * @param mixed|null $recordedAfter
     */
    public static function make(
        $aggregateId = 'Special default value that allows <PERSON> to differentiate between explicitly passing null and not passing a value at all.',
        $context = 'Special default value that allows <PERSON> to differentiate between explicitly passing null and not passing a value at all.',
        $entityId = 'Special default value that allows <PERSON> to differentiate between explicitly passing null and not passing a value at all.',
        $eventName = 'Special default value that allows <PERSON> to differentiate between explicitly passing null and not passing a value at all.',
        $originator = 'Special default value that allows <PERSON> to differentiate between explicitly passing null and not passing a value at all.',
        $originatorType = 'Special default value that allows Sailor to differentiate between explicitly passing null and not passing a value at all.',
        $payload = 'Special default value that allows Sailor to differentiate between explicitly passing null and not passing a value at all.',
        $recordedAfter = 'Special default value that allows Sailor to differentiate between explicitly passing null and not passing a value at all.',
    ): self {
        $instance = new self;

        if ($aggregateId !== self::UNDEFINED) {
            $instance->aggregateId = $aggregateId;
        }
        if ($context !== self::UNDEFINED) {
            $instance->context = $context;
        }
        if ($entityId !== self::UNDEFINED) {
            $instance->entityId = $entityId;
        }
        if ($eventName !== self::UNDEFINED) {
            $instance->eventName = $eventName;
        }
        if ($originator !== self::UNDEFINED) {
            $instance->originator = $originator;
        }
        if ($originatorType !== self::UNDEFINED) {
            $instance->originatorType = $originatorType;
        }
        if ($payload !== self::UNDEFINED) {
            $instance->payload = $payload;
        }
        if ($recordedAfter !== self::UNDEFINED) {
            $instance->recordedAfter = $recordedAfter;
        }

        return $instance;
    }

    protected function converters(): array
    {
        static $converters;

        return $converters ??= [
            'aggregateId' => new \Spawnia\Sailor\Convert\NullConverter(new \Spawnia\Sailor\Convert\IDConverter),
            'context' => new \Spawnia\Sailor\Convert\NullConverter(new \Spawnia\Sailor\Convert\StringConverter),
            'entityId' => new \Spawnia\Sailor\Convert\NullConverter(new \Spawnia\Sailor\Convert\IDConverter),
            'eventName' => new \Spawnia\Sailor\Convert\NullConverter(new \Spawnia\Sailor\Convert\StringConverter),
            'originator' => new \Spawnia\Sailor\Convert\NullConverter(new \Spawnia\Sailor\Convert\StringConverter),
            'originatorType' => new \Spawnia\Sailor\Convert\NullConverter(new \Spawnia\Sailor\Convert\EnumConverter),
            'payload' => new \Spawnia\Sailor\Convert\NullConverter(new \Spawnia\Sailor\Convert\StringConverter),
            'recordedAfter' => new \Spawnia\Sailor\Convert\NullConverter(new \Spawnia\Sailor\Convert\ScalarConverter),
        ];
    }

    public static function endpoint(): string
    {
        return 'flop';
    }

    public static function config(): string
    {
        return \Safe\realpath(__DIR__ . '/../../../../../sailor.php');
    }
}
