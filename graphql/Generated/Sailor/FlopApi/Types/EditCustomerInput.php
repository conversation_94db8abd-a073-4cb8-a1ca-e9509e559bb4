<?php declare(strict_types=1);

namespace Generated\Sailor\FlopApi\Types;

/**
 * @property string|null $bgpCommunity
 */
class EditCustomerInput extends \Spawnia\Sailor\ObjectLike
{
    /**
     * @param string|null $bgpCommunity
     */
    public static function make(
        $bgpCommunity = 'Special default value that allows <PERSON> to differentiate between explicitly passing null and not passing a value at all.',
    ): self {
        $instance = new self;

        if ($bgpCommunity !== self::UNDEFINED) {
            $instance->bgpCommunity = $bgpCommunity;
        }

        return $instance;
    }

    protected function converters(): array
    {
        static $converters;

        return $converters ??= [
            'bgpCommunity' => new \Spawnia\Sailor\Convert\NullConverter(new \Spawnia\Sailor\Convert\StringConverter),
        ];
    }

    public static function endpoint(): string
    {
        return 'flop';
    }

    public static function config(): string
    {
        return \Safe\realpath(__DIR__ . '/../../../../../sailor.php');
    }
}
