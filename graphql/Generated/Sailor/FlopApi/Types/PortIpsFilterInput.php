<?php declare(strict_types=1);

namespace Generated\Sailor\FlopApi\Types;

/**
 * @property \Generated\Sailor\FlopApi\Types\CustomerIdFilterInput|null $customer
 * @property int|string|null $device
 * @property array<string>|null $ips
 * @property string|null $ipFamily
 */
class PortIpsFilterInput extends \Spawnia\Sailor\ObjectLike
{
    /**
     * @param \Generated\Sailor\FlopApi\Types\CustomerIdFilterInput|null $customer
     * @param int|string|null $device
     * @param array<string>|null $ips
     * @param string|null $ipFamily
     */
    public static function make(
        $customer = 'Special default value that allows <PERSON> to differentiate between explicitly passing null and not passing a value at all.',
        $device = 'Special default value that allows <PERSON> to differentiate between explicitly passing null and not passing a value at all.',
        $ips = 'Special default value that allows <PERSON> to differentiate between explicitly passing null and not passing a value at all.',
        $ipFamily = 'Special default value that allows <PERSON> to differentiate between explicitly passing null and not passing a value at all.',
    ): self {
        $instance = new self;

        if ($customer !== self::UNDEFINED) {
            $instance->customer = $customer;
        }
        if ($device !== self::UNDEFINED) {
            $instance->device = $device;
        }
        if ($ips !== self::UNDEFINED) {
            $instance->ips = $ips;
        }
        if ($ipFamily !== self::UNDEFINED) {
            $instance->ipFamily = $ipFamily;
        }

        return $instance;
    }

    protected function converters(): array
    {
        static $converters;

        return $converters ??= [
            'customer' => new \Spawnia\Sailor\Convert\NullConverter(new \Generated\Sailor\FlopApi\Types\CustomerIdFilterInput),
            'device' => new \Spawnia\Sailor\Convert\NullConverter(new \Spawnia\Sailor\Convert\IDConverter),
            'ips' => new \Spawnia\Sailor\Convert\NullConverter(new \Spawnia\Sailor\Convert\ListConverter(new \Spawnia\Sailor\Convert\NonNullConverter(new \Spawnia\Sailor\Convert\StringConverter))),
            'ipFamily' => new \Spawnia\Sailor\Convert\NullConverter(new \Spawnia\Sailor\Convert\EnumConverter),
        ];
    }

    public static function endpoint(): string
    {
        return 'flop';
    }

    public static function config(): string
    {
        return \Safe\realpath(__DIR__ . '/../../../../../sailor.php');
    }
}
