<?php declare(strict_types=1);

namespace Generated\Sailor\FlopApi\Types;

/**
 * @property int|string $odfId
 */
class CrossConnectOdfPortFilterInput extends \Spawnia\Sailor\ObjectLike
{
    /**
     * @param int|string $odfId
     */
    public static function make($odfId): self
    {
        $instance = new self;

        if ($odfId !== self::UNDEFINED) {
            $instance->odfId = $odfId;
        }

        return $instance;
    }

    protected function converters(): array
    {
        static $converters;

        return $converters ??= [
            'odfId' => new \Spawnia\Sailor\Convert\NonNullConverter(new \Spawnia\Sailor\Convert\IDConverter),
        ];
    }

    public static function endpoint(): string
    {
        return 'flop';
    }

    public static function config(): string
    {
        return \Safe\realpath(__DIR__ . '/../../../../../sailor.php');
    }
}
