<?php declare(strict_types=1);

namespace Generated\Sailor\FlopApi\Types;

/**
 * @property int|string|null $deviceId
 */
class DevicePollingSchedulesFilterInput extends \Spawnia\Sailor\ObjectLike
{
    /**
     * @param int|string|null $deviceId
     */
    public static function make(
        $deviceId = 'Special default value that allows <PERSON> to differentiate between explicitly passing null and not passing a value at all.',
    ): self {
        $instance = new self;

        if ($deviceId !== self::UNDEFINED) {
            $instance->deviceId = $deviceId;
        }

        return $instance;
    }

    protected function converters(): array
    {
        static $converters;

        return $converters ??= [
            'deviceId' => new \Spawnia\Sailor\Convert\NullConverter(new \Spawnia\Sailor\Convert\IDConverter),
        ];
    }

    public static function endpoint(): string
    {
        return 'flop';
    }

    public static function config(): string
    {
        return \Safe\realpath(__DIR__ . '/../../../../../sailor.php');
    }
}
