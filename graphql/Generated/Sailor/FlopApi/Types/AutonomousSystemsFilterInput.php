<?php declare(strict_types=1);

namespace Generated\Sailor\FlopApi\Types;

/**
 * @property string|null $label
 * @property string|null $name
 * @property array<mixed>|null $numbers
 */
class AutonomousSystemsFilterInput extends \Spawnia\Sailor\ObjectLike
{
    /**
     * @param string|null $label
     * @param string|null $name
     * @param array<mixed>|null $numbers
     */
    public static function make(
        $label = 'Special default value that allows <PERSON> to differentiate between explicitly passing null and not passing a value at all.',
        $name = 'Special default value that allows <PERSON> to differentiate between explicitly passing null and not passing a value at all.',
        $numbers = 'Special default value that allows <PERSON> to differentiate between explicitly passing null and not passing a value at all.',
    ): self {
        $instance = new self;

        if ($label !== self::UNDEFINED) {
            $instance->label = $label;
        }
        if ($name !== self::UNDEFINED) {
            $instance->name = $name;
        }
        if ($numbers !== self::UNDEFINED) {
            $instance->numbers = $numbers;
        }

        return $instance;
    }

    protected function converters(): array
    {
        static $converters;

        return $converters ??= [
            'label' => new \Spawnia\Sailor\Convert\NullConverter(new \Spawnia\Sailor\Convert\StringConverter),
            'name' => new \Spawnia\Sailor\Convert\NullConverter(new \Spawnia\Sailor\Convert\StringConverter),
            'numbers' => new \Spawnia\Sailor\Convert\NullConverter(new \Spawnia\Sailor\Convert\ListConverter(new \Spawnia\Sailor\Convert\NonNullConverter(new \Spawnia\Sailor\Convert\ScalarConverter))),
        ];
    }

    public static function endpoint(): string
    {
        return 'flop';
    }

    public static function config(): string
    {
        return \Safe\realpath(__DIR__ . '/../../../../../sailor.php');
    }
}
