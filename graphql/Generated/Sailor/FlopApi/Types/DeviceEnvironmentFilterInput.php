<?php declare(strict_types=1);

namespace Generated\Sailor\FlopApi\Types;

/**
 * @property int|string $deviceId
 */
class DeviceEnvironmentFilterInput extends \Spawnia\Sailor\ObjectLike
{
    /**
     * @param int|string $deviceId
     */
    public static function make($deviceId): self
    {
        $instance = new self;

        if ($deviceId !== self::UNDEFINED) {
            $instance->deviceId = $deviceId;
        }

        return $instance;
    }

    protected function converters(): array
    {
        static $converters;

        return $converters ??= [
            'deviceId' => new \Spawnia\Sailor\Convert\NonNullConverter(new \Spawnia\Sailor\Convert\IDConverter),
        ];
    }

    public static function endpoint(): string
    {
        return 'flop';
    }

    public static function config(): string
    {
        return \Safe\realpath(__DIR__ . '/../../../../../sailor.php');
    }
}
