<?php declare(strict_types=1);

namespace Generated\Sailor\FlopApi\Types;

class MaintenanceImpactType
{
    public const Interruption = 'Interruption';
    public const None = 'None';
    public const Other = 'Other';
    public const Risk = 'Risk';

    public static function endpoint(): string
    {
        return 'flop';
    }

    public static function config(): string
    {
        return \Safe\realpath(__DIR__ . '/../../../../../sailor.php');
    }
}
