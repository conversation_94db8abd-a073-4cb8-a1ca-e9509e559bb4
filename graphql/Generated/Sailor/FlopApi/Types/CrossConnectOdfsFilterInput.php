<?php declare(strict_types=1);

namespace Generated\Sailor\FlopApi\Types;

/**
 * @property int|string $dataCenter
 */
class CrossConnectOdfsFilterInput extends \Spawnia\Sailor\ObjectLike
{
    /**
     * @param int|string $dataCenter
     */
    public static function make($dataCenter): self
    {
        $instance = new self;

        if ($dataCenter !== self::UNDEFINED) {
            $instance->dataCenter = $dataCenter;
        }

        return $instance;
    }

    protected function converters(): array
    {
        static $converters;

        return $converters ??= [
            'dataCenter' => new \Spawnia\Sailor\Convert\NonNullConverter(new \Spawnia\Sailor\Convert\IDConverter),
        ];
    }

    public static function endpoint(): string
    {
        return 'flop';
    }

    public static function config(): string
    {
        return \Safe\realpath(__DIR__ . '/../../../../../sailor.php');
    }
}
