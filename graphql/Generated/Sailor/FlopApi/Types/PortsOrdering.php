<?php declare(strict_types=1);

namespace Generated\Sailor\FlopApi\Types;

/**
 * @property string $direction
 * @property string $sort
 */
class PortsOrdering extends \Spawnia\Sailor\ObjectLike
{
    /**
     * @param string $direction
     * @param string $sort
     */
    public static function make($direction, $sort): self
    {
        $instance = new self;

        if ($direction !== self::UNDEFINED) {
            $instance->direction = $direction;
        }
        if ($sort !== self::UNDEFINED) {
            $instance->sort = $sort;
        }

        return $instance;
    }

    protected function converters(): array
    {
        static $converters;

        return $converters ??= [
            'direction' => new \Spawnia\Sailor\Convert\NonNullConverter(new \Spawnia\Sailor\Convert\EnumConverter),
            'sort' => new \Spawnia\Sailor\Convert\NonNullConverter(new \Spawnia\Sailor\Convert\EnumConverter),
        ];
    }

    public static function endpoint(): string
    {
        return 'flop';
    }

    public static function config(): string
    {
        return \Safe\realpath(__DIR__ . '/../../../../../sailor.php');
    }
}
