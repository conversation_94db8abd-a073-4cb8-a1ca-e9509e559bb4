<?php declare(strict_types=1);

namespace Generated\Sailor\FlopApi\Types;

/**
 * @property int|string $dataPacketId
 * @property string $name
 */
class AddDataPacketCustomerInput extends \Spawnia\Sailor\ObjectLike
{
    /**
     * @param int|string $dataPacketId
     * @param string $name
     */
    public static function make($dataPacketId, $name): self
    {
        $instance = new self;

        if ($dataPacketId !== self::UNDEFINED) {
            $instance->dataPacketId = $dataPacketId;
        }
        if ($name !== self::UNDEFINED) {
            $instance->name = $name;
        }

        return $instance;
    }

    protected function converters(): array
    {
        static $converters;

        return $converters ??= [
            'dataPacketId' => new \Spawnia\Sailor\Convert\NonNullConverter(new \Spawnia\Sailor\Convert\IDConverter),
            'name' => new \Spawnia\Sailor\Convert\NonNullConverter(new \Spawnia\Sailor\Convert\StringConverter),
        ];
    }

    public static function endpoint(): string
    {
        return 'flop';
    }

    public static function config(): string
    {
        return \Safe\realpath(__DIR__ . '/../../../../../sailor.php');
    }
}
