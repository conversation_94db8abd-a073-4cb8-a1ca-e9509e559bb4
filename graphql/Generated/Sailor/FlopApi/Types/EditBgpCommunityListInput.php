<?php declare(strict_types=1);

namespace Generated\Sailor\FlopApi\Types;

/**
 * @property string $name
 * @property array<string> $communities
 * @property string|null $asPathPattern
 */
class EditBgpCommunityListInput extends \Spawnia\Sailor\ObjectLike
{
    /**
     * @param string $name
     * @param array<string> $communities
     * @param string|null $asPathPattern
     */
    public static function make(
        $name,
        $communities,
        $asPathPattern = 'Special default value that allows <PERSON> to differentiate between explicitly passing null and not passing a value at all.',
    ): self {
        $instance = new self;

        if ($name !== self::UNDEFINED) {
            $instance->name = $name;
        }
        if ($communities !== self::UNDEFINED) {
            $instance->communities = $communities;
        }
        if ($asPathPattern !== self::UNDEFINED) {
            $instance->asPathPattern = $asPathPattern;
        }

        return $instance;
    }

    protected function converters(): array
    {
        static $converters;

        return $converters ??= [
            'name' => new \Spawnia\Sailor\Convert\NonNullConverter(new \Spawnia\Sailor\Convert\StringConverter),
            'communities' => new \Spawnia\Sailor\Convert\NonNullConverter(new \Spawnia\Sailor\Convert\ListConverter(new \Spawnia\Sailor\Convert\NonNullConverter(new \Spawnia\Sailor\Convert\StringConverter))),
            'asPathPattern' => new \Spawnia\Sailor\Convert\NullConverter(new \Spawnia\Sailor\Convert\StringConverter),
        ];
    }

    public static function endpoint(): string
    {
        return 'flop';
    }

    public static function config(): string
    {
        return \Safe\realpath(__DIR__ . '/../../../../../sailor.php');
    }
}
