<?php declare(strict_types=1);

namespace Generated\Sailor\FlopApi\Types;

/**
 * @property int|string $asn
 * @property int|string $customerId
 * @property string $name
 * @property string|null $note
 */
class EditBirdPrefixListInput extends \Spawnia\Sailor\ObjectLike
{
    /**
     * @param int|string $asn
     * @param int|string $customerId
     * @param string $name
     * @param string|null $note
     */
    public static function make(
        $asn,
        $customerId,
        $name,
        $note = 'Special default value that allows <PERSON> to differentiate between explicitly passing null and not passing a value at all.',
    ): self {
        $instance = new self;

        if ($asn !== self::UNDEFINED) {
            $instance->asn = $asn;
        }
        if ($customerId !== self::UNDEFINED) {
            $instance->customerId = $customerId;
        }
        if ($name !== self::UNDEFINED) {
            $instance->name = $name;
        }
        if ($note !== self::UNDEFINED) {
            $instance->note = $note;
        }

        return $instance;
    }

    protected function converters(): array
    {
        static $converters;

        return $converters ??= [
            'asn' => new \Spawnia\Sailor\Convert\NonNullConverter(new \Spawnia\Sailor\Convert\IDConverter),
            'customerId' => new \Spawnia\Sailor\Convert\NonNullConverter(new \Spawnia\Sailor\Convert\IDConverter),
            'name' => new \Spawnia\Sailor\Convert\NonNullConverter(new \Spawnia\Sailor\Convert\StringConverter),
            'note' => new \Spawnia\Sailor\Convert\NullConverter(new \Spawnia\Sailor\Convert\StringConverter),
        ];
    }

    public static function endpoint(): string
    {
        return 'flop';
    }

    public static function config(): string
    {
        return \Safe\realpath(__DIR__ . '/../../../../../sailor.php');
    }
}
