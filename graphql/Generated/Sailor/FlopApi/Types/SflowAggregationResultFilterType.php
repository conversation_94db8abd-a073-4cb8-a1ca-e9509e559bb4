<?php declare(strict_types=1);

namespace Generated\Sailor\FlopApi\Types;

class SflowAggregationResultFilterType
{
    public const CountLte = 'CountLte';
    public const MaxBpsGte = 'MaxBpsGte';
    public const MaxBpsLte = 'MaxBpsLte';
    public const Pctl95BpsGte = 'Pctl95BpsGte';
    public const Pctl95BpsLte = 'Pctl95BpsLte';

    public static function endpoint(): string
    {
        return 'flop';
    }

    public static function config(): string
    {
        return \Safe\realpath(__DIR__ . '/../../../../../sailor.php');
    }
}
