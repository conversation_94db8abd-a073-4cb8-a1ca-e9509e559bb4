<?php declare(strict_types=1);

namespace Generated\Sailor\FlopApi\Types;

class OperatingSystemName
{
    public const Dcp = 'Dcp';
    public const Eos = 'Eos';
    public const Ios = 'Ios';
    public const Linux = 'Linux';
    public const Nos = 'Nos';
    public const Unknown = 'Unknown';

    public static function endpoint(): string
    {
        return 'flop';
    }

    public static function config(): string
    {
        return \Safe\realpath(__DIR__ . '/../../../../../sailor.php');
    }
}
