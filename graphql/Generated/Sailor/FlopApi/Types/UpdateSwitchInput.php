<?php declare(strict_types=1);

namespace Generated\Sailor\FlopApi\Types;

/**
 * @property bool $arpEnabled
 * @property bool $bgpEnabled
 * @property int|string $cabinet
 * @property bool $pollingEnabled
 * @property int|string $owner
 * @property string $primaryIp
 * @property bool $sflowToFlop
 * @property string|null $comment
 * @property string|null $distributor
 * @property string|null $invoiceUrl
 * @property string|null $secondaryIp
 * @property mixed|null $serviceEndDate
 */
class UpdateSwitchInput extends \Spawnia\Sailor\ObjectLike
{
    /**
     * @param bool $arpEnabled
     * @param bool $bgpEnabled
     * @param int|string $cabinet
     * @param bool $pollingEnabled
     * @param int|string $owner
     * @param string $primaryIp
     * @param bool $sflowToFlop
     * @param string|null $comment
     * @param string|null $distributor
     * @param string|null $invoiceUrl
     * @param string|null $secondaryIp
     * @param mixed|null $serviceEndDate
     */
    public static function make(
        $arpEnabled,
        $bgpEnabled,
        $cabinet,
        $pollingEnabled,
        $owner,
        $primaryIp,
        $sflowToFlop,
        $comment = 'Special default value that allows <PERSON> to differentiate between explicitly passing null and not passing a value at all.',
        $distributor = 'Special default value that allows Sailor to differentiate between explicitly passing null and not passing a value at all.',
        $invoiceUrl = 'Special default value that allows Sailor to differentiate between explicitly passing null and not passing a value at all.',
        $secondaryIp = 'Special default value that allows Sailor to differentiate between explicitly passing null and not passing a value at all.',
        $serviceEndDate = 'Special default value that allows Sailor to differentiate between explicitly passing null and not passing a value at all.',
    ): self {
        $instance = new self;

        if ($arpEnabled !== self::UNDEFINED) {
            $instance->arpEnabled = $arpEnabled;
        }
        if ($bgpEnabled !== self::UNDEFINED) {
            $instance->bgpEnabled = $bgpEnabled;
        }
        if ($cabinet !== self::UNDEFINED) {
            $instance->cabinet = $cabinet;
        }
        if ($pollingEnabled !== self::UNDEFINED) {
            $instance->pollingEnabled = $pollingEnabled;
        }
        if ($owner !== self::UNDEFINED) {
            $instance->owner = $owner;
        }
        if ($primaryIp !== self::UNDEFINED) {
            $instance->primaryIp = $primaryIp;
        }
        if ($sflowToFlop !== self::UNDEFINED) {
            $instance->sflowToFlop = $sflowToFlop;
        }
        if ($comment !== self::UNDEFINED) {
            $instance->comment = $comment;
        }
        if ($distributor !== self::UNDEFINED) {
            $instance->distributor = $distributor;
        }
        if ($invoiceUrl !== self::UNDEFINED) {
            $instance->invoiceUrl = $invoiceUrl;
        }
        if ($secondaryIp !== self::UNDEFINED) {
            $instance->secondaryIp = $secondaryIp;
        }
        if ($serviceEndDate !== self::UNDEFINED) {
            $instance->serviceEndDate = $serviceEndDate;
        }

        return $instance;
    }

    protected function converters(): array
    {
        static $converters;

        return $converters ??= [
            'arpEnabled' => new \Spawnia\Sailor\Convert\NonNullConverter(new \Spawnia\Sailor\Convert\BooleanConverter),
            'bgpEnabled' => new \Spawnia\Sailor\Convert\NonNullConverter(new \Spawnia\Sailor\Convert\BooleanConverter),
            'cabinet' => new \Spawnia\Sailor\Convert\NonNullConverter(new \Spawnia\Sailor\Convert\IDConverter),
            'pollingEnabled' => new \Spawnia\Sailor\Convert\NonNullConverter(new \Spawnia\Sailor\Convert\BooleanConverter),
            'owner' => new \Spawnia\Sailor\Convert\NonNullConverter(new \Spawnia\Sailor\Convert\IDConverter),
            'primaryIp' => new \Spawnia\Sailor\Convert\NonNullConverter(new \Spawnia\Sailor\Convert\StringConverter),
            'sflowToFlop' => new \Spawnia\Sailor\Convert\NonNullConverter(new \Spawnia\Sailor\Convert\BooleanConverter),
            'comment' => new \Spawnia\Sailor\Convert\NullConverter(new \Spawnia\Sailor\Convert\StringConverter),
            'distributor' => new \Spawnia\Sailor\Convert\NullConverter(new \Spawnia\Sailor\Convert\StringConverter),
            'invoiceUrl' => new \Spawnia\Sailor\Convert\NullConverter(new \Spawnia\Sailor\Convert\StringConverter),
            'secondaryIp' => new \Spawnia\Sailor\Convert\NullConverter(new \Spawnia\Sailor\Convert\StringConverter),
            'serviceEndDate' => new \Spawnia\Sailor\Convert\NullConverter(new \Spawnia\Sailor\Convert\ScalarConverter),
        ];
    }

    public static function endpoint(): string
    {
        return 'flop';
    }

    public static function config(): string
    {
        return \Safe\realpath(__DIR__ . '/../../../../../sailor.php');
    }
}
