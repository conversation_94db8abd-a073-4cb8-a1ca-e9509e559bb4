<?php declare(strict_types=1);

namespace Generated\Sailor\FlopApi\Types;

/**
 * @property mixed|null $endAfter
 * @property mixed|null $endBefore
 */
class MaintenancesFilterInput extends \Spawnia\Sailor\ObjectLike
{
    /**
     * @param mixed|null $endAfter
     * @param mixed|null $endBefore
     */
    public static function make(
        $endAfter = 'Special default value that allows <PERSON> to differentiate between explicitly passing null and not passing a value at all.',
        $endBefore = 'Special default value that allows <PERSON> to differentiate between explicitly passing null and not passing a value at all.',
    ): self {
        $instance = new self;

        if ($endAfter !== self::UNDEFINED) {
            $instance->endAfter = $endAfter;
        }
        if ($endBefore !== self::UNDEFINED) {
            $instance->endBefore = $endBefore;
        }

        return $instance;
    }

    protected function converters(): array
    {
        static $converters;

        return $converters ??= [
            'endAfter' => new \Spawnia\Sailor\Convert\NullConverter(new \Spawnia\Sailor\Convert\ScalarConverter),
            'endBefore' => new \Spawnia\Sailor\Convert\NullConverter(new \Spawnia\Sailor\Convert\ScalarConverter),
        ];
    }

    public static function endpoint(): string
    {
        return 'flop';
    }

    public static function config(): string
    {
        return \Safe\realpath(__DIR__ . '/../../../../../sailor.php');
    }
}
