<?php declare(strict_types=1);

namespace Generated\Sailor\FlopApi\Types;

/**
 * @property string $connector
 * @property string $type
 */
class EditCrossConnectOdfPortInput extends \Spawnia\Sailor\ObjectLike
{
    /**
     * @param string $connector
     * @param string $type
     */
    public static function make($connector, $type): self
    {
        $instance = new self;

        if ($connector !== self::UNDEFINED) {
            $instance->connector = $connector;
        }
        if ($type !== self::UNDEFINED) {
            $instance->type = $type;
        }

        return $instance;
    }

    protected function converters(): array
    {
        static $converters;

        return $converters ??= [
            'connector' => new \Spawnia\Sailor\Convert\NonNullConverter(new \Spawnia\Sailor\Convert\EnumConverter),
            'type' => new \Spawnia\Sailor\Convert\NonNullConverter(new \Spawnia\Sailor\Convert\EnumConverter),
        ];
    }

    public static function endpoint(): string
    {
        return 'flop';
    }

    public static function config(): string
    {
        return \Safe\realpath(__DIR__ . '/../../../../../sailor.php');
    }
}
