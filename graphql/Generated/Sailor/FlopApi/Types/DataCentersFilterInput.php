<?php declare(strict_types=1);

namespace Generated\Sailor\FlopApi\Types;

/**
 * @property array<int|string>|null $continents
 * @property bool|null $hasBillableBandwidth
 * @property bool|null $containsOdf
 * @property array<int|string>|null $presentCustomers
 * @property string|null $siteId
 */
class DataCentersFilterInput extends \Spawnia\Sailor\ObjectLike
{
    /**
     * @param array<int|string>|null $continents
     * @param bool|null $hasBillableBandwidth
     * @param bool|null $containsOdf
     * @param array<int|string>|null $presentCustomers
     * @param string|null $siteId
     */
    public static function make(
        $continents = 'Special default value that allows <PERSON> to differentiate between explicitly passing null and not passing a value at all.',
        $hasBillableBandwidth = 'Special default value that allows <PERSON> to differentiate between explicitly passing null and not passing a value at all.',
        $containsOdf = 'Special default value that allows <PERSON> to differentiate between explicitly passing null and not passing a value at all.',
        $presentCustomers = 'Special default value that allows <PERSON> to differentiate between explicitly passing null and not passing a value at all.',
        $siteId = 'Special default value that allows <PERSON> to differentiate between explicitly passing null and not passing a value at all.',
    ): self {
        $instance = new self;

        if ($continents !== self::UNDEFINED) {
            $instance->continents = $continents;
        }
        if ($hasBillableBandwidth !== self::UNDEFINED) {
            $instance->hasBillableBandwidth = $hasBillableBandwidth;
        }
        if ($containsOdf !== self::UNDEFINED) {
            $instance->containsOdf = $containsOdf;
        }
        if ($presentCustomers !== self::UNDEFINED) {
            $instance->presentCustomers = $presentCustomers;
        }
        if ($siteId !== self::UNDEFINED) {
            $instance->siteId = $siteId;
        }

        return $instance;
    }

    protected function converters(): array
    {
        static $converters;

        return $converters ??= [
            'continents' => new \Spawnia\Sailor\Convert\NullConverter(new \Spawnia\Sailor\Convert\ListConverter(new \Spawnia\Sailor\Convert\NonNullConverter(new \Spawnia\Sailor\Convert\IDConverter))),
            'hasBillableBandwidth' => new \Spawnia\Sailor\Convert\NullConverter(new \Spawnia\Sailor\Convert\BooleanConverter),
            'containsOdf' => new \Spawnia\Sailor\Convert\NullConverter(new \Spawnia\Sailor\Convert\BooleanConverter),
            'presentCustomers' => new \Spawnia\Sailor\Convert\NullConverter(new \Spawnia\Sailor\Convert\ListConverter(new \Spawnia\Sailor\Convert\NonNullConverter(new \Spawnia\Sailor\Convert\IDConverter))),
            'siteId' => new \Spawnia\Sailor\Convert\NullConverter(new \Spawnia\Sailor\Convert\StringConverter),
        ];
    }

    public static function endpoint(): string
    {
        return 'flop';
    }

    public static function config(): string
    {
        return \Safe\realpath(__DIR__ . '/../../../../../sailor.php');
    }
}
