<?php declare(strict_types=1);

namespace Generated\Sailor\FlopApi\Types;

/**
 * @property string $linkType
 * @property int|string $pduId
 * @property int|string $portId
 */
class LinkPduToPortInput extends \Spawnia\Sailor\ObjectLike
{
    /**
     * @param string $linkType
     * @param int|string $pduId
     * @param int|string $portId
     */
    public static function make($linkType, $pduId, $portId): self
    {
        $instance = new self;

        if ($linkType !== self::UNDEFINED) {
            $instance->linkType = $linkType;
        }
        if ($pduId !== self::UNDEFINED) {
            $instance->pduId = $pduId;
        }
        if ($portId !== self::UNDEFINED) {
            $instance->portId = $portId;
        }

        return $instance;
    }

    protected function converters(): array
    {
        static $converters;

        return $converters ??= [
            'linkType' => new \Spawnia\Sailor\Convert\NonNullConverter(new \Spawnia\Sailor\Convert\EnumConverter),
            'pduId' => new \Spawnia\Sailor\Convert\NonNullConverter(new \Spawnia\Sailor\Convert\IDConverter),
            'portId' => new \Spawnia\Sailor\Convert\NonNullConverter(new \Spawnia\Sailor\Convert\IDConverter),
        ];
    }

    public static function endpoint(): string
    {
        return 'flop';
    }

    public static function config(): string
    {
        return \Safe\realpath(__DIR__ . '/../../../../../sailor.php');
    }
}
