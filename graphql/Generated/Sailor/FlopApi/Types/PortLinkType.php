<?php declare(strict_types=1);

namespace Generated\Sailor\FlopApi\Types;

class PortLinkType
{
    public const Internal = 'Internal';
    public const Ipmi = 'Ipmi';
    public const Provisioning = 'Provisioning';
    public const Uplink = 'Uplink';

    public static function endpoint(): string
    {
        return 'flop';
    }

    public static function config(): string
    {
        return \Safe\realpath(__DIR__ . '/../../../../../sailor.php');
    }
}
