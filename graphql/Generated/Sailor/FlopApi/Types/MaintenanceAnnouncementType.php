<?php declare(strict_types=1);

namespace Generated\Sailor\FlopApi\Types;

class MaintenanceAnnouncementType
{
    public const Cancelled = 'Cancelled';
    public const Finished = 'Finished';
    public const Other = 'Other';
    public const Outage = 'Outage';
    public const Planned = 'Planned';
    public const Update = 'Update';

    public static function endpoint(): string
    {
        return 'flop';
    }

    public static function config(): string
    {
        return \Safe\realpath(__DIR__ . '/../../../../../sailor.php');
    }
}
