<?php declare(strict_types=1);

namespace Generated\Sailor\FlopApi\Types;

/**
 * @property array<string|null>|null $ips
 * @property array<string|null>|null $portIds
 */
class IpsFilterInput extends \Spawnia\Sailor\ObjectLike
{
    /**
     * @param array<string|null>|null $ips
     * @param array<string|null>|null $portIds
     */
    public static function make(
        $ips = 'Special default value that allows <PERSON> to differentiate between explicitly passing null and not passing a value at all.',
        $portIds = 'Special default value that allows <PERSON> to differentiate between explicitly passing null and not passing a value at all.',
    ): self {
        $instance = new self;

        if ($ips !== self::UNDEFINED) {
            $instance->ips = $ips;
        }
        if ($portIds !== self::UNDEFINED) {
            $instance->portIds = $portIds;
        }

        return $instance;
    }

    protected function converters(): array
    {
        static $converters;

        return $converters ??= [
            'ips' => new \Spawnia\Sailor\Convert\NullConverter(new \Spawnia\Sailor\Convert\ListConverter(new \Spawnia\Sailor\Convert\NullConverter(new \Spawnia\Sailor\Convert\StringConverter))),
            'portIds' => new \Spawnia\Sailor\Convert\NullConverter(new \Spawnia\Sailor\Convert\ListConverter(new \Spawnia\Sailor\Convert\NullConverter(new \Spawnia\Sailor\Convert\StringConverter))),
        ];
    }

    public static function endpoint(): string
    {
        return 'flop';
    }

    public static function config(): string
    {
        return \Safe\realpath(__DIR__ . '/../../../../../sailor.php');
    }
}
