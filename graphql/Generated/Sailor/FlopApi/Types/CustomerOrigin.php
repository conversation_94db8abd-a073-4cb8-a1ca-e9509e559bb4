<?php declare(strict_types=1);

namespace Generated\Sailor\FlopApi\Types;

class CustomerOrigin
{
    public const DataPacket = 'DataPacket';
    public const Gel = 'Gel';
    public const PeeringCz = 'PeeringCz';

    public static function endpoint(): string
    {
        return 'flop';
    }

    public static function config(): string
    {
        return \Safe\realpath(__DIR__ . '/../../../../../sailor.php');
    }
}
