<?php declare(strict_types=1);

namespace Generated\Sailor\FlopApi\Types;

/**
 * @property int|string $cabinetId
 * @property string $name
 * @property string $portConnector
 * @property int $portCount
 * @property string $portNamePrefix
 * @property string $portNumberingStrategy
 * @property string $portType
 */
class AddCrossConnectOdfInput extends \Spawnia\Sailor\ObjectLike
{
    /**
     * @param int|string $cabinetId
     * @param string $name
     * @param string $portConnector
     * @param int $portCount
     * @param string $portNamePrefix
     * @param string $portNumberingStrategy
     * @param string $portType
     */
    public static function make(
        $cabinetId,
        $name,
        $portConnector,
        $portCount,
        $portNamePrefix,
        $portNumberingStrategy,
        $portType,
    ): self {
        $instance = new self;

        if ($cabinetId !== self::UNDEFINED) {
            $instance->cabinetId = $cabinetId;
        }
        if ($name !== self::UNDEFINED) {
            $instance->name = $name;
        }
        if ($portConnector !== self::UNDEFINED) {
            $instance->portConnector = $portConnector;
        }
        if ($portCount !== self::UNDEFINED) {
            $instance->portCount = $portCount;
        }
        if ($portNamePrefix !== self::UNDEFINED) {
            $instance->portNamePrefix = $portNamePrefix;
        }
        if ($portNumberingStrategy !== self::UNDEFINED) {
            $instance->portNumberingStrategy = $portNumberingStrategy;
        }
        if ($portType !== self::UNDEFINED) {
            $instance->portType = $portType;
        }

        return $instance;
    }

    protected function converters(): array
    {
        static $converters;

        return $converters ??= [
            'cabinetId' => new \Spawnia\Sailor\Convert\NonNullConverter(new \Spawnia\Sailor\Convert\IDConverter),
            'name' => new \Spawnia\Sailor\Convert\NonNullConverter(new \Spawnia\Sailor\Convert\StringConverter),
            'portConnector' => new \Spawnia\Sailor\Convert\NonNullConverter(new \Spawnia\Sailor\Convert\EnumConverter),
            'portCount' => new \Spawnia\Sailor\Convert\NonNullConverter(new \Spawnia\Sailor\Convert\IntConverter),
            'portNamePrefix' => new \Spawnia\Sailor\Convert\NonNullConverter(new \Spawnia\Sailor\Convert\StringConverter),
            'portNumberingStrategy' => new \Spawnia\Sailor\Convert\NonNullConverter(new \Spawnia\Sailor\Convert\EnumConverter),
            'portType' => new \Spawnia\Sailor\Convert\NonNullConverter(new \Spawnia\Sailor\Convert\EnumConverter),
        ];
    }

    public static function endpoint(): string
    {
        return 'flop';
    }

    public static function config(): string
    {
        return \Safe\realpath(__DIR__ . '/../../../../../sailor.php');
    }
}
