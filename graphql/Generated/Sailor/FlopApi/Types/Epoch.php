<?php declare(strict_types=1);

namespace Generated\Sailor\FlopApi\Types;

/**
 * @property int $bucketSize
 * @property mixed $from
 * @property mixed $to
 */
class Epoch extends \Spawnia\Sailor\ObjectLike
{
    /**
     * @param int $bucketSize
     * @param mixed $from
     * @param mixed $to
     */
    public static function make($bucketSize, $from, $to): self
    {
        $instance = new self;

        if ($bucketSize !== self::UNDEFINED) {
            $instance->bucketSize = $bucketSize;
        }
        if ($from !== self::UNDEFINED) {
            $instance->from = $from;
        }
        if ($to !== self::UNDEFINED) {
            $instance->to = $to;
        }

        return $instance;
    }

    protected function converters(): array
    {
        static $converters;

        return $converters ??= [
            'bucketSize' => new \Spawnia\Sailor\Convert\NonNullConverter(new \Spawnia\Sailor\Convert\IntConverter),
            'from' => new \Spawnia\Sailor\Convert\NonNullConverter(new \Spawnia\Sailor\Convert\ScalarConverter),
            'to' => new \Spawnia\Sailor\Convert\NonNullConverter(new \Spawnia\Sailor\Convert\ScalarConverter),
        ];
    }

    public static function endpoint(): string
    {
        return 'flop';
    }

    public static function config(): string
    {
        return \Safe\realpath(__DIR__ . '/../../../../../sailor.php');
    }
}
