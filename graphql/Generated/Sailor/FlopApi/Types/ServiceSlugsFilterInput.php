<?php declare(strict_types=1);

namespace Generated\Sailor\FlopApi\Types;

/**
 * @property string $projectSlug
 * @property string $slug
 */
class ServiceSlugsFilterInput extends \Spawnia\Sailor\ObjectLike
{
    /**
     * @param string $projectSlug
     * @param string $slug
     */
    public static function make($projectSlug, $slug): self
    {
        $instance = new self;

        if ($projectSlug !== self::UNDEFINED) {
            $instance->projectSlug = $projectSlug;
        }
        if ($slug !== self::UNDEFINED) {
            $instance->slug = $slug;
        }

        return $instance;
    }

    protected function converters(): array
    {
        static $converters;

        return $converters ??= [
            'projectSlug' => new \Spawnia\Sailor\Convert\NonNullConverter(new \Spawnia\Sailor\Convert\StringConverter),
            'slug' => new \Spawnia\Sailor\Convert\NonNullConverter(new \Spawnia\Sailor\Convert\StringConverter),
        ];
    }

    public static function endpoint(): string
    {
        return 'flop';
    }

    public static function config(): string
    {
        return \Safe\realpath(__DIR__ . '/../../../../../sailor.php');
    }
}
