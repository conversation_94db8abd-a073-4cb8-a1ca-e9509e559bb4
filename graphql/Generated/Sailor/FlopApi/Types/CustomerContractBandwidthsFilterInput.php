<?php declare(strict_types=1);

namespace Generated\Sailor\FlopApi\Types;

/**
 * @property int|string|null $customer
 * @property array<\Generated\Sailor\FlopApi\Types\CustomerIdFilterInput>|null $customers
 * @property \Generated\Sailor\FlopApi\Types\DateTimeRange|null $range
 */
class CustomerContractBandwidthsFilterInput extends \Spawnia\Sailor\ObjectLike
{
    /**
     * @param int|string|null $customer
     * @param array<\Generated\Sailor\FlopApi\Types\CustomerIdFilterInput>|null $customers
     * @param \Generated\Sailor\FlopApi\Types\DateTimeRange|null $range
     */
    public static function make(
        $customer = 'Special default value that allows <PERSON> to differentiate between explicitly passing null and not passing a value at all.',
        $customers = 'Special default value that allows <PERSON> to differentiate between explicitly passing null and not passing a value at all.',
        $range = 'Special default value that allows <PERSON> to differentiate between explicitly passing null and not passing a value at all.',
    ): self {
        $instance = new self;

        if ($customer !== self::UNDEFINED) {
            $instance->customer = $customer;
        }
        if ($customers !== self::UNDEFINED) {
            $instance->customers = $customers;
        }
        if ($range !== self::UNDEFINED) {
            $instance->range = $range;
        }

        return $instance;
    }

    protected function converters(): array
    {
        static $converters;

        return $converters ??= [
            'customer' => new \Spawnia\Sailor\Convert\NullConverter(new \Spawnia\Sailor\Convert\IDConverter),
            'customers' => new \Spawnia\Sailor\Convert\NullConverter(new \Spawnia\Sailor\Convert\ListConverter(new \Spawnia\Sailor\Convert\NonNullConverter(new \Generated\Sailor\FlopApi\Types\CustomerIdFilterInput))),
            'range' => new \Spawnia\Sailor\Convert\NullConverter(new \Generated\Sailor\FlopApi\Types\DateTimeRange),
        ];
    }

    public static function endpoint(): string
    {
        return 'flop';
    }

    public static function config(): string
    {
        return \Safe\realpath(__DIR__ . '/../../../../../sailor.php');
    }
}
