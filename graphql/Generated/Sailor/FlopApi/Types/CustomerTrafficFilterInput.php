<?php declare(strict_types=1);

namespace Generated\Sailor\FlopApi\Types;

/**
 * @property \Generated\Sailor\FlopApi\Types\CustomerIdFilterInput $customerId
 * @property mixed $from
 * @property int $interval
 * @property mixed $to
 */
class CustomerTrafficFilterInput extends \Spawnia\Sailor\ObjectLike
{
    /**
     * @param \Generated\Sailor\FlopApi\Types\CustomerIdFilterInput $customerId
     * @param mixed $from
     * @param int $interval
     * @param mixed $to
     */
    public static function make($customerId, $from, $interval, $to): self
    {
        $instance = new self;

        if ($customerId !== self::UNDEFINED) {
            $instance->customerId = $customerId;
        }
        if ($from !== self::UNDEFINED) {
            $instance->from = $from;
        }
        if ($interval !== self::UNDEFINED) {
            $instance->interval = $interval;
        }
        if ($to !== self::UNDEFINED) {
            $instance->to = $to;
        }

        return $instance;
    }

    protected function converters(): array
    {
        static $converters;

        return $converters ??= [
            'customerId' => new \Spawnia\Sailor\Convert\NonNullConverter(new \Generated\Sailor\FlopApi\Types\CustomerIdFilterInput),
            'from' => new \Spawnia\Sailor\Convert\NonNullConverter(new \Spawnia\Sailor\Convert\ScalarConverter),
            'interval' => new \Spawnia\Sailor\Convert\NonNullConverter(new \Spawnia\Sailor\Convert\IntConverter),
            'to' => new \Spawnia\Sailor\Convert\NonNullConverter(new \Spawnia\Sailor\Convert\ScalarConverter),
        ];
    }

    public static function endpoint(): string
    {
        return 'flop';
    }

    public static function config(): string
    {
        return \Safe\realpath(__DIR__ . '/../../../../../sailor.php');
    }
}
