<?php declare(strict_types=1);

namespace Generated\Sailor\FlopApi\Types;

class DevicePollingResult
{
    public const NetworkError = 'NetworkError';
    public const Ok = 'Ok';
    public const StrategyError = 'StrategyError';
    public const Warning = 'Warning';

    public static function endpoint(): string
    {
        return 'flop';
    }

    public static function config(): string
    {
        return \Safe\realpath(__DIR__ . '/../../../../../sailor.php');
    }
}
