<?php declare(strict_types=1);

namespace Generated\Sailor\FlopApi\Types;

/**
 * @property string $color
 * @property string $community
 * @property string $name
 * @property string|null $defaultFilter
 */
class EditNetworkLabelInput extends \Spawnia\Sailor\ObjectLike
{
    /**
     * @param string $color
     * @param string $community
     * @param string $name
     * @param string|null $defaultFilter
     */
    public static function make(
        $color,
        $community,
        $name,
        $defaultFilter = 'Special default value that allows <PERSON> to differentiate between explicitly passing null and not passing a value at all.',
    ): self {
        $instance = new self;

        if ($color !== self::UNDEFINED) {
            $instance->color = $color;
        }
        if ($community !== self::UNDEFINED) {
            $instance->community = $community;
        }
        if ($name !== self::UNDEFINED) {
            $instance->name = $name;
        }
        if ($defaultFilter !== self::UNDEFINED) {
            $instance->defaultFilter = $defaultFilter;
        }

        return $instance;
    }

    protected function converters(): array
    {
        static $converters;

        return $converters ??= [
            'color' => new \Spawnia\Sailor\Convert\NonNullConverter(new \Spawnia\Sailor\Convert\StringConverter),
            'community' => new \Spawnia\Sailor\Convert\NonNullConverter(new \Spawnia\Sailor\Convert\StringConverter),
            'name' => new \Spawnia\Sailor\Convert\NonNullConverter(new \Spawnia\Sailor\Convert\StringConverter),
            'defaultFilter' => new \Spawnia\Sailor\Convert\NullConverter(new \Spawnia\Sailor\Convert\EnumConverter),
        ];
    }

    public static function endpoint(): string
    {
        return 'flop';
    }

    public static function config(): string
    {
        return \Safe\realpath(__DIR__ . '/../../../../../sailor.php');
    }
}
