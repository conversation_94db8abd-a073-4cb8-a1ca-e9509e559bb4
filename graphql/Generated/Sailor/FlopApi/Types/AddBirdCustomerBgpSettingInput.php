<?php declare(strict_types=1);

namespace Generated\Sailor\FlopApi\Types;

/**
 * @property int|string $dataCenterId
 * @property int|string $prefixListId
 * @property mixed $remoteIp
 * @property int|string $serverId
 */
class AddBirdCustomerBgpSettingInput extends \Spawnia\Sailor\ObjectLike
{
    /**
     * @param int|string $dataCenterId
     * @param int|string $prefixListId
     * @param mixed $remoteIp
     * @param int|string $serverId
     */
    public static function make($dataCenterId, $prefixListId, $remoteIp, $serverId): self
    {
        $instance = new self;

        if ($dataCenterId !== self::UNDEFINED) {
            $instance->dataCenterId = $dataCenterId;
        }
        if ($prefixListId !== self::UNDEFINED) {
            $instance->prefixListId = $prefixListId;
        }
        if ($remoteIp !== self::UNDEFINED) {
            $instance->remoteIp = $remoteIp;
        }
        if ($serverId !== self::UNDEFINED) {
            $instance->serverId = $serverId;
        }

        return $instance;
    }

    protected function converters(): array
    {
        static $converters;

        return $converters ??= [
            'dataCenterId' => new \Spawnia\Sailor\Convert\NonNullConverter(new \Spawnia\Sailor\Convert\IDConverter),
            'prefixListId' => new \Spawnia\Sailor\Convert\NonNullConverter(new \Spawnia\Sailor\Convert\IDConverter),
            'remoteIp' => new \Spawnia\Sailor\Convert\NonNullConverter(new \Spawnia\Sailor\Convert\ScalarConverter),
            'serverId' => new \Spawnia\Sailor\Convert\NonNullConverter(new \Spawnia\Sailor\Convert\IDConverter),
        ];
    }

    public static function endpoint(): string
    {
        return 'flop';
    }

    public static function config(): string
    {
        return \Safe\realpath(__DIR__ . '/../../../../../sailor.php');
    }
}
