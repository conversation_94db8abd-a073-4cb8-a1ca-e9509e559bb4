<?php declare(strict_types=1);

namespace Generated\Sailor\FlopApi\Types;

/**
 * @property mixed|null $ip
 */
class EdgePortsFilterInput extends \Spawnia\Sailor\ObjectLike
{
    /**
     * @param mixed|null $ip
     */
    public static function make(
        $ip = 'Special default value that allows <PERSON> to differentiate between explicitly passing null and not passing a value at all.',
    ): self {
        $instance = new self;

        if ($ip !== self::UNDEFINED) {
            $instance->ip = $ip;
        }

        return $instance;
    }

    protected function converters(): array
    {
        static $converters;

        return $converters ??= [
            'ip' => new \Spawnia\Sailor\Convert\NullConverter(new \Spawnia\Sailor\Convert\ScalarConverter),
        ];
    }

    public static function endpoint(): string
    {
        return 'flop';
    }

    public static function config(): string
    {
        return \Safe\realpath(__DIR__ . '/../../../../../sailor.php');
    }
}
