<?php declare(strict_types=1);

namespace Generated\Sailor\FlopApi\Types;

/**
 * @property mixed $from
 * @property int $period
 * @property array<int|string> $portIds
 * @property mixed $to
 */
class PortsDiscardsFilterInput extends \Spawnia\Sailor\ObjectLike
{
    /**
     * @param mixed $from
     * @param int $period
     * @param array<int|string> $portIds
     * @param mixed $to
     */
    public static function make($from, $period, $portIds, $to): self
    {
        $instance = new self;

        if ($from !== self::UNDEFINED) {
            $instance->from = $from;
        }
        if ($period !== self::UNDEFINED) {
            $instance->period = $period;
        }
        if ($portIds !== self::UNDEFINED) {
            $instance->portIds = $portIds;
        }
        if ($to !== self::UNDEFINED) {
            $instance->to = $to;
        }

        return $instance;
    }

    protected function converters(): array
    {
        static $converters;

        return $converters ??= [
            'from' => new \Spawnia\Sailor\Convert\NonNullConverter(new \Spawnia\Sailor\Convert\ScalarConverter),
            'period' => new \Spawnia\Sailor\Convert\NonNullConverter(new \Spawnia\Sailor\Convert\IntConverter),
            'portIds' => new \Spawnia\Sailor\Convert\NonNullConverter(new \Spawnia\Sailor\Convert\ListConverter(new \Spawnia\Sailor\Convert\NonNullConverter(new \Spawnia\Sailor\Convert\IDConverter))),
            'to' => new \Spawnia\Sailor\Convert\NonNullConverter(new \Spawnia\Sailor\Convert\ScalarConverter),
        ];
    }

    public static function endpoint(): string
    {
        return 'flop';
    }

    public static function config(): string
    {
        return \Safe\realpath(__DIR__ . '/../../../../../sailor.php');
    }
}
