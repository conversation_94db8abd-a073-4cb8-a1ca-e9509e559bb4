<?php declare(strict_types=1);

namespace Generated\Sailor\FlopApi\Types;

/**
 * @property string|null $name
 * @property array<int|string>|null $presentCustomers
 */
class ContinentsFilterInput extends \Spawnia\Sailor\ObjectLike
{
    /**
     * @param string|null $name
     * @param array<int|string>|null $presentCustomers
     */
    public static function make(
        $name = 'Special default value that allows <PERSON> to differentiate between explicitly passing null and not passing a value at all.',
        $presentCustomers = 'Special default value that allows <PERSON> to differentiate between explicitly passing null and not passing a value at all.',
    ): self {
        $instance = new self;

        if ($name !== self::UNDEFINED) {
            $instance->name = $name;
        }
        if ($presentCustomers !== self::UNDEFINED) {
            $instance->presentCustomers = $presentCustomers;
        }

        return $instance;
    }

    protected function converters(): array
    {
        static $converters;

        return $converters ??= [
            'name' => new \Spawnia\Sailor\Convert\NullConverter(new \Spawnia\Sailor\Convert\StringConverter),
            'presentCustomers' => new \Spawnia\Sailor\Convert\NullConverter(new \Spawnia\Sailor\Convert\ListConverter(new \Spawnia\Sailor\Convert\NonNullConverter(new \Spawnia\Sailor\Convert\IDConverter))),
        ];
    }

    public static function endpoint(): string
    {
        return 'flop';
    }

    public static function config(): string
    {
        return \Safe\realpath(__DIR__ . '/../../../../../sailor.php');
    }
}
