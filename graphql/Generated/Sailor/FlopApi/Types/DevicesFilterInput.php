<?php declare(strict_types=1);

namespace Generated\Sailor\FlopApi\Types;

/**
 * @property int|string|null $dataCenter
 * @property bool|null $hasArp
 * @property bool|null $hasBgp
 * @property bool|null $hasSflow
 * @property string|null $ip
 * @property bool|null $isEnabled
 * @property string|null $modelName
 * @property string|null $name
 * @property \Generated\Sailor\FlopApi\Types\OperatingSystemFilterInput|null $operatingSystem
 * @property int|string|null $owner
 * @property string|null $serialNumber
 * @property string|null $type
 */
class DevicesFilterInput extends \Spawnia\Sailor\ObjectLike
{
    /**
     * @param int|string|null $dataCenter
     * @param bool|null $hasArp
     * @param bool|null $hasBgp
     * @param bool|null $hasSflow
     * @param string|null $ip
     * @param bool|null $isEnabled
     * @param string|null $modelName
     * @param string|null $name
     * @param \Generated\Sailor\FlopApi\Types\OperatingSystemFilterInput|null $operatingSystem
     * @param int|string|null $owner
     * @param string|null $serialNumber
     * @param string|null $type
     */
    public static function make(
        $dataCenter = 'Special default value that allows Sailor to differentiate between explicitly passing null and not passing a value at all.',
        $hasArp = 'Special default value that allows Sailor to differentiate between explicitly passing null and not passing a value at all.',
        $hasBgp = 'Special default value that allows Sailor to differentiate between explicitly passing null and not passing a value at all.',
        $hasSflow = 'Special default value that allows Sailor to differentiate between explicitly passing null and not passing a value at all.',
        $ip = 'Special default value that allows Sailor to differentiate between explicitly passing null and not passing a value at all.',
        $isEnabled = 'Special default value that allows Sailor to differentiate between explicitly passing null and not passing a value at all.',
        $modelName = 'Special default value that allows Sailor to differentiate between explicitly passing null and not passing a value at all.',
        $name = 'Special default value that allows Sailor to differentiate between explicitly passing null and not passing a value at all.',
        $operatingSystem = 'Special default value that allows Sailor to differentiate between explicitly passing null and not passing a value at all.',
        $owner = 'Special default value that allows Sailor to differentiate between explicitly passing null and not passing a value at all.',
        $serialNumber = 'Special default value that allows Sailor to differentiate between explicitly passing null and not passing a value at all.',
        $type = 'Special default value that allows Sailor to differentiate between explicitly passing null and not passing a value at all.',
    ): self {
        $instance = new self;

        if ($dataCenter !== self::UNDEFINED) {
            $instance->dataCenter = $dataCenter;
        }
        if ($hasArp !== self::UNDEFINED) {
            $instance->hasArp = $hasArp;
        }
        if ($hasBgp !== self::UNDEFINED) {
            $instance->hasBgp = $hasBgp;
        }
        if ($hasSflow !== self::UNDEFINED) {
            $instance->hasSflow = $hasSflow;
        }
        if ($ip !== self::UNDEFINED) {
            $instance->ip = $ip;
        }
        if ($isEnabled !== self::UNDEFINED) {
            $instance->isEnabled = $isEnabled;
        }
        if ($modelName !== self::UNDEFINED) {
            $instance->modelName = $modelName;
        }
        if ($name !== self::UNDEFINED) {
            $instance->name = $name;
        }
        if ($operatingSystem !== self::UNDEFINED) {
            $instance->operatingSystem = $operatingSystem;
        }
        if ($owner !== self::UNDEFINED) {
            $instance->owner = $owner;
        }
        if ($serialNumber !== self::UNDEFINED) {
            $instance->serialNumber = $serialNumber;
        }
        if ($type !== self::UNDEFINED) {
            $instance->type = $type;
        }

        return $instance;
    }

    protected function converters(): array
    {
        static $converters;

        return $converters ??= [
            'dataCenter' => new \Spawnia\Sailor\Convert\NullConverter(new \Spawnia\Sailor\Convert\IDConverter),
            'hasArp' => new \Spawnia\Sailor\Convert\NullConverter(new \Spawnia\Sailor\Convert\BooleanConverter),
            'hasBgp' => new \Spawnia\Sailor\Convert\NullConverter(new \Spawnia\Sailor\Convert\BooleanConverter),
            'hasSflow' => new \Spawnia\Sailor\Convert\NullConverter(new \Spawnia\Sailor\Convert\BooleanConverter),
            'ip' => new \Spawnia\Sailor\Convert\NullConverter(new \Spawnia\Sailor\Convert\StringConverter),
            'isEnabled' => new \Spawnia\Sailor\Convert\NullConverter(new \Spawnia\Sailor\Convert\BooleanConverter),
            'modelName' => new \Spawnia\Sailor\Convert\NullConverter(new \Spawnia\Sailor\Convert\StringConverter),
            'name' => new \Spawnia\Sailor\Convert\NullConverter(new \Spawnia\Sailor\Convert\StringConverter),
            'operatingSystem' => new \Spawnia\Sailor\Convert\NullConverter(new \Generated\Sailor\FlopApi\Types\OperatingSystemFilterInput),
            'owner' => new \Spawnia\Sailor\Convert\NullConverter(new \Spawnia\Sailor\Convert\IDConverter),
            'serialNumber' => new \Spawnia\Sailor\Convert\NullConverter(new \Spawnia\Sailor\Convert\StringConverter),
            'type' => new \Spawnia\Sailor\Convert\NullConverter(new \Spawnia\Sailor\Convert\EnumConverter),
        ];
    }

    public static function endpoint(): string
    {
        return 'flop';
    }

    public static function config(): string
    {
        return \Safe\realpath(__DIR__ . '/../../../../../sailor.php');
    }
}
