<?php declare(strict_types=1);

namespace Generated\Sailor\FlopApi\Types;

class LoadBalancerMatchRuleStatus
{
    public const InHardware = 'InHardware';
    public const NotHandled = 'NotHandled';
    public const PartiallyInHardware = 'PartiallyInHardware';

    public static function endpoint(): string
    {
        return 'flop';
    }

    public static function config(): string
    {
        return \Safe\realpath(__DIR__ . '/../../../../../sailor.php');
    }
}
