<?php declare(strict_types=1);

namespace Generated\Sailor\FlopApi\Types;

/**
 * @property string|null $deviceName
 * @property string|null $modelName
 * @property string|null $serialNumber
 * @property string|null $type
 */
class DeviceComponentsFilterInput extends \Spawnia\Sailor\ObjectLike
{
    /**
     * @param string|null $deviceName
     * @param string|null $modelName
     * @param string|null $serialNumber
     * @param string|null $type
     */
    public static function make(
        $deviceName = 'Special default value that allows <PERSON> to differentiate between explicitly passing null and not passing a value at all.',
        $modelName = 'Special default value that allows <PERSON> to differentiate between explicitly passing null and not passing a value at all.',
        $serialNumber = 'Special default value that allows <PERSON> to differentiate between explicitly passing null and not passing a value at all.',
        $type = 'Special default value that allows <PERSON> to differentiate between explicitly passing null and not passing a value at all.',
    ): self {
        $instance = new self;

        if ($deviceName !== self::UNDEFINED) {
            $instance->deviceName = $deviceName;
        }
        if ($modelName !== self::UNDEFINED) {
            $instance->modelName = $modelName;
        }
        if ($serialNumber !== self::UNDEFINED) {
            $instance->serialNumber = $serialNumber;
        }
        if ($type !== self::UNDEFINED) {
            $instance->type = $type;
        }

        return $instance;
    }

    protected function converters(): array
    {
        static $converters;

        return $converters ??= [
            'deviceName' => new \Spawnia\Sailor\Convert\NullConverter(new \Spawnia\Sailor\Convert\StringConverter),
            'modelName' => new \Spawnia\Sailor\Convert\NullConverter(new \Spawnia\Sailor\Convert\StringConverter),
            'serialNumber' => new \Spawnia\Sailor\Convert\NullConverter(new \Spawnia\Sailor\Convert\StringConverter),
            'type' => new \Spawnia\Sailor\Convert\NullConverter(new \Spawnia\Sailor\Convert\StringConverter),
        ];
    }

    public static function endpoint(): string
    {
        return 'flop';
    }

    public static function config(): string
    {
        return \Safe\realpath(__DIR__ . '/../../../../../sailor.php');
    }
}
