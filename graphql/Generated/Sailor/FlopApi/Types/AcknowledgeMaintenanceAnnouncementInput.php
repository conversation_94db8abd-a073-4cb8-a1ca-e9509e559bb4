<?php declare(strict_types=1);

namespace Generated\Sailor\FlopApi\Types;

/**
 * @property string|null $comment
 */
class AcknowledgeMaintenanceAnnouncementInput extends \Spawnia\Sailor\ObjectLike
{
    /**
     * @param string|null $comment
     */
    public static function make(
        $comment = 'Special default value that allows <PERSON> to differentiate between explicitly passing null and not passing a value at all.',
    ): self {
        $instance = new self;

        if ($comment !== self::UNDEFINED) {
            $instance->comment = $comment;
        }

        return $instance;
    }

    protected function converters(): array
    {
        static $converters;

        return $converters ??= [
            'comment' => new \Spawnia\Sailor\Convert\NullConverter(new \Spawnia\Sailor\Convert\StringConverter),
        ];
    }

    public static function endpoint(): string
    {
        return 'flop';
    }

    public static function config(): string
    {
        return \Safe\realpath(__DIR__ . '/../../../../../sailor.php');
    }
}
