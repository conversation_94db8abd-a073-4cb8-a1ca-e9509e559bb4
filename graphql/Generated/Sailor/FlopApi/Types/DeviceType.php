<?php declare(strict_types=1);

namespace Generated\Sailor\FlopApi\Types;

class DeviceType
{
    public const Dwdm = 'Dwdm';
    public const LoadBalancer = 'LoadBalancer';
    public const Router = 'Router';
    public const Server = 'Server';
    public const Switch = 'Switch';
    public const Unmanaged = 'Unmanaged';

    public static function endpoint(): string
    {
        return 'flop';
    }

    public static function config(): string
    {
        return \Safe\realpath(__DIR__ . '/../../../../../sailor.php');
    }
}
