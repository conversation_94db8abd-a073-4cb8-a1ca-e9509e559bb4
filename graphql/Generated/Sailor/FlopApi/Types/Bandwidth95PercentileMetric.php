<?php declare(strict_types=1);

namespace Generated\Sailor\FlopApi\Types;

class Bandwidth95PercentileMetric
{
    public const Default = 'Default';
    public const ShiftToLocalTime = 'ShiftToLocalTime';
    public const SumOverPorts = 'SumOverPorts';

    public static function endpoint(): string
    {
        return 'flop';
    }

    public static function config(): string
    {
        return \Safe\realpath(__DIR__ . '/../../../../../sailor.php');
    }
}
