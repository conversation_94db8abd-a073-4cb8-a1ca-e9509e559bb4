<?php declare(strict_types=1);

namespace Generated\Sailor\FlopApi\Types;

class SflowAttribute
{
    public const AsPath = 'AsPath';
    public const AsPathNextAs = 'AsPathNextAs';
    public const BgpCommunities = 'BgpCommunities';
    public const BgpNextHopIp = 'BgpNextHopIp';
    public const DataCenter = 'DataCenter';
    public const Device = 'Device';
    public const DstAs = 'DstAs';
    public const DstContinent = 'DstContinent';
    public const DstCountry = 'DstCountry';
    public const DstCustomerOverIp = 'DstCustomerOverIp';
    public const DstCustomerOverMac = 'DstCustomerOverMac';
    public const DstIp = 'DstIp';
    public const DstMac = 'DstMac';
    public const DstPort = 'DstPort';
    public const DstPrefix = 'DstPrefix';
    public const DstPrefixMatchIp = 'DstPrefixMatchIp';
    public const DstProject = 'DstProject';
    public const DstServerOverIp = 'DstServerOverIp';
    public const DstService = 'DstService';
    public const DstServiceGroup = 'DstServiceGroup';
    public const DstSmallestPrefix = 'DstSmallestPrefix';
    public const DstVirtualPop = 'DstVirtualPop';
    public const DstVlan = 'DstVlan';
    public const InContract = 'InContract';
    public const InIf = 'InIf';
    public const InIfId = 'InIfId';
    public const InNetworkPath = 'InNetworkPath';
    public const InNetworkPathPricingModel = 'InNetworkPathPricingModel';
    public const InNetworkPathType = 'InNetworkPathType';
    public const OutContract = 'OutContract';
    public const OutIf = 'OutIf';
    public const OutIfId = 'OutIfId';
    public const OutNetworkPath = 'OutNetworkPath';
    public const OutNetworkPathPricingModel = 'OutNetworkPathPricingModel';
    public const OutNetworkPathType = 'OutNetworkPathType';
    public const Protocol = 'Protocol';
    public const ProvinceCode = 'ProvinceCode';
    public const SrcAs = 'SrcAs';
    public const SrcContinent = 'SrcContinent';
    public const SrcCountry = 'SrcCountry';
    public const SrcCustomerOverIp = 'SrcCustomerOverIp';
    public const SrcCustomerOverMac = 'SrcCustomerOverMac';
    public const SrcIp = 'SrcIp';
    public const SrcMac = 'SrcMac';
    public const SrcPort = 'SrcPort';
    public const SrcPrefix = 'SrcPrefix';
    public const SrcPrefixMatchIp = 'SrcPrefixMatchIp';
    public const SrcProject = 'SrcProject';
    public const SrcServerOverIp = 'SrcServerOverIp';
    public const SrcService = 'SrcService';
    public const SrcServiceGroup = 'SrcServiceGroup';
    public const SrcSmallestPrefix = 'SrcSmallestPrefix';
    public const SrcVirtualPop = 'SrcVirtualPop';
    public const SrcVlan = 'SrcVlan';
    public const Tags = 'Tags';
    public const VirtualPop = 'VirtualPop';

    public static function endpoint(): string
    {
        return 'flop';
    }

    public static function config(): string
    {
        return \Safe\realpath(__DIR__ . '/../../../../../sailor.php');
    }
}
