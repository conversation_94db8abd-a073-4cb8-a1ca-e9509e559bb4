<?php declare(strict_types=1);

namespace Generated\Sailor\FlopApi\Types;

/**
 * @property string $approvalStatus
 * @property int|string $prefixListId
 * @property array<string> $prefixes
 * @property string|null $kayakoTicketId
 */
class AddBirdPrefixInput extends \Spawnia\Sailor\ObjectLike
{
    /**
     * @param string $approvalStatus
     * @param int|string $prefixListId
     * @param array<string> $prefixes
     * @param string|null $kayakoTicketId
     */
    public static function make(
        $approvalStatus,
        $prefixListId,
        $prefixes,
        $kayakoTicketId = 'Special default value that allows <PERSON> to differentiate between explicitly passing null and not passing a value at all.',
    ): self {
        $instance = new self;

        if ($approvalStatus !== self::UNDEFINED) {
            $instance->approvalStatus = $approvalStatus;
        }
        if ($prefixListId !== self::UNDEFINED) {
            $instance->prefixListId = $prefixListId;
        }
        if ($prefixes !== self::UNDEFINED) {
            $instance->prefixes = $prefixes;
        }
        if ($kayakoTicketId !== self::UNDEFINED) {
            $instance->kayakoTicketId = $kayakoTicketId;
        }

        return $instance;
    }

    protected function converters(): array
    {
        static $converters;

        return $converters ??= [
            'approvalStatus' => new \Spawnia\Sailor\Convert\NonNullConverter(new \Spawnia\Sailor\Convert\EnumConverter),
            'prefixListId' => new \Spawnia\Sailor\Convert\NonNullConverter(new \Spawnia\Sailor\Convert\IDConverter),
            'prefixes' => new \Spawnia\Sailor\Convert\NonNullConverter(new \Spawnia\Sailor\Convert\ListConverter(new \Spawnia\Sailor\Convert\NonNullConverter(new \Spawnia\Sailor\Convert\StringConverter))),
            'kayakoTicketId' => new \Spawnia\Sailor\Convert\NullConverter(new \Spawnia\Sailor\Convert\StringConverter),
        ];
    }

    public static function endpoint(): string
    {
        return 'flop';
    }

    public static function config(): string
    {
        return \Safe\realpath(__DIR__ . '/../../../../../sailor.php');
    }
}
