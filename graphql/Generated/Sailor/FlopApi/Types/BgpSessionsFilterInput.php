<?php declare(strict_types=1);

namespace Generated\Sailor\FlopApi\Types;

/**
 * @property string|null $deviceName
 * @property string|null $description
 * @property int|string|null $localAs
 * @property string|null $localIp
 * @property int|null $prefixesAccepted
 * @property int|null $prefixesReceived
 * @property int|null $prefixesSent
 * @property int|string|null $remoteAs
 * @property string|null $remoteIp
 * @property array<mixed>|null $remoteIps
 * @property string|null $state
 * @property array<string>|null $states
 */
class BgpSessionsFilterInput extends \Spawnia\Sailor\ObjectLike
{
    /**
     * @param string|null $deviceName
     * @param string|null $description
     * @param int|string|null $localAs
     * @param string|null $localIp
     * @param int|null $prefixesAccepted
     * @param int|null $prefixesReceived
     * @param int|null $prefixesSent
     * @param int|string|null $remoteAs
     * @param string|null $remoteIp
     * @param array<mixed>|null $remoteIps
     * @param string|null $state
     * @param array<string>|null $states
     */
    public static function make(
        $deviceName = 'Special default value that allows <PERSON> to differentiate between explicitly passing null and not passing a value at all.',
        $description = 'Special default value that allows Sailor to differentiate between explicitly passing null and not passing a value at all.',
        $localAs = 'Special default value that allows Sailor to differentiate between explicitly passing null and not passing a value at all.',
        $localIp = 'Special default value that allows Sailor to differentiate between explicitly passing null and not passing a value at all.',
        $prefixesAccepted = 'Special default value that allows Sailor to differentiate between explicitly passing null and not passing a value at all.',
        $prefixesReceived = 'Special default value that allows Sailor to differentiate between explicitly passing null and not passing a value at all.',
        $prefixesSent = 'Special default value that allows Sailor to differentiate between explicitly passing null and not passing a value at all.',
        $remoteAs = 'Special default value that allows Sailor to differentiate between explicitly passing null and not passing a value at all.',
        $remoteIp = 'Special default value that allows Sailor to differentiate between explicitly passing null and not passing a value at all.',
        $remoteIps = 'Special default value that allows Sailor to differentiate between explicitly passing null and not passing a value at all.',
        $state = 'Special default value that allows Sailor to differentiate between explicitly passing null and not passing a value at all.',
        $states = 'Special default value that allows Sailor to differentiate between explicitly passing null and not passing a value at all.',
    ): self {
        $instance = new self;

        if ($deviceName !== self::UNDEFINED) {
            $instance->deviceName = $deviceName;
        }
        if ($description !== self::UNDEFINED) {
            $instance->description = $description;
        }
        if ($localAs !== self::UNDEFINED) {
            $instance->localAs = $localAs;
        }
        if ($localIp !== self::UNDEFINED) {
            $instance->localIp = $localIp;
        }
        if ($prefixesAccepted !== self::UNDEFINED) {
            $instance->prefixesAccepted = $prefixesAccepted;
        }
        if ($prefixesReceived !== self::UNDEFINED) {
            $instance->prefixesReceived = $prefixesReceived;
        }
        if ($prefixesSent !== self::UNDEFINED) {
            $instance->prefixesSent = $prefixesSent;
        }
        if ($remoteAs !== self::UNDEFINED) {
            $instance->remoteAs = $remoteAs;
        }
        if ($remoteIp !== self::UNDEFINED) {
            $instance->remoteIp = $remoteIp;
        }
        if ($remoteIps !== self::UNDEFINED) {
            $instance->remoteIps = $remoteIps;
        }
        if ($state !== self::UNDEFINED) {
            $instance->state = $state;
        }
        if ($states !== self::UNDEFINED) {
            $instance->states = $states;
        }

        return $instance;
    }

    protected function converters(): array
    {
        static $converters;

        return $converters ??= [
            'deviceName' => new \Spawnia\Sailor\Convert\NullConverter(new \Spawnia\Sailor\Convert\StringConverter),
            'description' => new \Spawnia\Sailor\Convert\NullConverter(new \Spawnia\Sailor\Convert\StringConverter),
            'localAs' => new \Spawnia\Sailor\Convert\NullConverter(new \Spawnia\Sailor\Convert\IDConverter),
            'localIp' => new \Spawnia\Sailor\Convert\NullConverter(new \Spawnia\Sailor\Convert\StringConverter),
            'prefixesAccepted' => new \Spawnia\Sailor\Convert\NullConverter(new \Spawnia\Sailor\Convert\IntConverter),
            'prefixesReceived' => new \Spawnia\Sailor\Convert\NullConverter(new \Spawnia\Sailor\Convert\IntConverter),
            'prefixesSent' => new \Spawnia\Sailor\Convert\NullConverter(new \Spawnia\Sailor\Convert\IntConverter),
            'remoteAs' => new \Spawnia\Sailor\Convert\NullConverter(new \Spawnia\Sailor\Convert\IDConverter),
            'remoteIp' => new \Spawnia\Sailor\Convert\NullConverter(new \Spawnia\Sailor\Convert\StringConverter),
            'remoteIps' => new \Spawnia\Sailor\Convert\NullConverter(new \Spawnia\Sailor\Convert\ListConverter(new \Spawnia\Sailor\Convert\NonNullConverter(new \Spawnia\Sailor\Convert\ScalarConverter))),
            'state' => new \Spawnia\Sailor\Convert\NullConverter(new \Spawnia\Sailor\Convert\EnumConverter),
            'states' => new \Spawnia\Sailor\Convert\NullConverter(new \Spawnia\Sailor\Convert\ListConverter(new \Spawnia\Sailor\Convert\NonNullConverter(new \Spawnia\Sailor\Convert\EnumConverter))),
        ];
    }

    public static function endpoint(): string
    {
        return 'flop';
    }

    public static function config(): string
    {
        return \Safe\realpath(__DIR__ . '/../../../../../sailor.php');
    }
}
