<?php declare(strict_types=1);

namespace Generated\Sailor\FlopApi\Types;

/**
 * @property string $name
 * @property array<\Generated\Sailor\FlopApi\Types\MetricAttributeFilter>|null $filter
 */
class MetricFilter extends \Spawnia\Sailor\ObjectLike
{
    /**
     * @param string $name
     * @param array<\Generated\Sailor\FlopApi\Types\MetricAttributeFilter>|null $filter
     */
    public static function make(
        $name,
        $filter = 'Special default value that allows <PERSON> to differentiate between explicitly passing null and not passing a value at all.',
    ): self {
        $instance = new self;

        if ($name !== self::UNDEFINED) {
            $instance->name = $name;
        }
        if ($filter !== self::UNDEFINED) {
            $instance->filter = $filter;
        }

        return $instance;
    }

    protected function converters(): array
    {
        static $converters;

        return $converters ??= [
            'name' => new \Spawnia\Sailor\Convert\NonNullConverter(new \Spawnia\Sailor\Convert\StringConverter),
            'filter' => new \Spawnia\Sailor\Convert\NullConverter(new \Spawnia\Sailor\Convert\ListConverter(new \Spawnia\Sailor\Convert\NonNullConverter(new \Generated\Sailor\FlopApi\Types\MetricAttributeFilter))),
        ];
    }

    public static function endpoint(): string
    {
        return 'flop';
    }

    public static function config(): string
    {
        return \Safe\realpath(__DIR__ . '/../../../../../sailor.php');
    }
}
