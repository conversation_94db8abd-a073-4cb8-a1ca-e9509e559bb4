<?php declare(strict_types=1);

namespace Generated\Sailor\FlopApi\Types;

/**
 * @property int|string $customer
 * @property mixed $month
 */
class TrafficStatisticsFilterInput extends \Spawnia\Sailor\ObjectLike
{
    /**
     * @param int|string $customer
     * @param mixed $month
     */
    public static function make($customer, $month): self
    {
        $instance = new self;

        if ($customer !== self::UNDEFINED) {
            $instance->customer = $customer;
        }
        if ($month !== self::UNDEFINED) {
            $instance->month = $month;
        }

        return $instance;
    }

    protected function converters(): array
    {
        static $converters;

        return $converters ??= [
            'customer' => new \Spawnia\Sailor\Convert\NonNullConverter(new \Spawnia\Sailor\Convert\IDConverter),
            'month' => new \Spawnia\Sailor\Convert\NonNullConverter(new \Spawnia\Sailor\Convert\ScalarConverter),
        ];
    }

    public static function endpoint(): string
    {
        return 'flop';
    }

    public static function config(): string
    {
        return \Safe\realpath(__DIR__ . '/../../../../../sailor.php');
    }
}
