<?php declare(strict_types=1);

namespace Generated\Sailor\FlopApi\Types;

class BgpSessionPriority
{
    public const High = 'High';
    public const Low = 'Low';
    public const Mid = 'Mid';
    public const None = 'None';

    public static function endpoint(): string
    {
        return 'flop';
    }

    public static function config(): string
    {
        return \Safe\realpath(__DIR__ . '/../../../../../sailor.php');
    }
}
