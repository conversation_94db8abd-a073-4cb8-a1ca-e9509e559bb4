<?php declare(strict_types=1);

namespace Generated\Sailor\FlopApi\Types;

/**
 * @property string $deviceName
 */
class LldpRecordsFilterInput extends \Spawnia\Sailor\ObjectLike
{
    /**
     * @param string $deviceName
     */
    public static function make($deviceName): self
    {
        $instance = new self;

        if ($deviceName !== self::UNDEFINED) {
            $instance->deviceName = $deviceName;
        }

        return $instance;
    }

    protected function converters(): array
    {
        static $converters;

        return $converters ??= [
            'deviceName' => new \Spawnia\Sailor\Convert\NonNullConverter(new \Spawnia\Sailor\Convert\StringConverter),
        ];
    }

    public static function endpoint(): string
    {
        return 'flop';
    }

    public static function config(): string
    {
        return \Safe\realpath(__DIR__ . '/../../../../../sailor.php');
    }
}
