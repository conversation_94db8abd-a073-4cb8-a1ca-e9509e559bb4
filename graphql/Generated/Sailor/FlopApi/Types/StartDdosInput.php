<?php declare(strict_types=1);

namespace Generated\Sailor\FlopApi\Types;

/**
 * @property float|int $bps
 * @property int|string $hurricaneId
 * @property string $hurricaneStatus
 * @property float|int $pps
 * @property string $source
 * @property mixed $startAt
 * @property string $target
 * @property string $type
 */
class StartDdosInput extends \Spawnia\Sailor\ObjectLike
{
    /**
     * @param float|int $bps
     * @param int|string $hurricaneId
     * @param string $hurricaneStatus
     * @param float|int $pps
     * @param string $source
     * @param mixed $startAt
     * @param string $target
     * @param string $type
     */
    public static function make($bps, $hurricaneId, $hurricaneStatus, $pps, $source, $startAt, $target, $type): self
    {
        $instance = new self;

        if ($bps !== self::UNDEFINED) {
            $instance->bps = $bps;
        }
        if ($hurricaneId !== self::UNDEFINED) {
            $instance->hurricaneId = $hurricaneId;
        }
        if ($hurricaneStatus !== self::UNDEFINED) {
            $instance->hurricaneStatus = $hurricaneStatus;
        }
        if ($pps !== self::UNDEFINED) {
            $instance->pps = $pps;
        }
        if ($source !== self::UNDEFINED) {
            $instance->source = $source;
        }
        if ($startAt !== self::UNDEFINED) {
            $instance->startAt = $startAt;
        }
        if ($target !== self::UNDEFINED) {
            $instance->target = $target;
        }
        if ($type !== self::UNDEFINED) {
            $instance->type = $type;
        }

        return $instance;
    }

    protected function converters(): array
    {
        static $converters;

        return $converters ??= [
            'bps' => new \Spawnia\Sailor\Convert\NonNullConverter(new \Spawnia\Sailor\Convert\FloatConverter),
            'hurricaneId' => new \Spawnia\Sailor\Convert\NonNullConverter(new \Spawnia\Sailor\Convert\IDConverter),
            'hurricaneStatus' => new \Spawnia\Sailor\Convert\NonNullConverter(new \Spawnia\Sailor\Convert\EnumConverter),
            'pps' => new \Spawnia\Sailor\Convert\NonNullConverter(new \Spawnia\Sailor\Convert\FloatConverter),
            'source' => new \Spawnia\Sailor\Convert\NonNullConverter(new \Spawnia\Sailor\Convert\StringConverter),
            'startAt' => new \Spawnia\Sailor\Convert\NonNullConverter(new \Spawnia\Sailor\Convert\ScalarConverter),
            'target' => new \Spawnia\Sailor\Convert\NonNullConverter(new \Spawnia\Sailor\Convert\StringConverter),
            'type' => new \Spawnia\Sailor\Convert\NonNullConverter(new \Spawnia\Sailor\Convert\StringConverter),
        ];
    }

    public static function endpoint(): string
    {
        return 'flop';
    }

    public static function config(): string
    {
        return \Safe\realpath(__DIR__ . '/../../../../../sailor.php');
    }
}
