<?php declare(strict_types=1);

namespace Generated\Sailor\FlopApi\Types;

class CustomerIdType
{
    public const DATA_PACKET = 'DATA_PACKET';
    public const DataPacket = 'DataPacket';
    public const FLOP = 'FLOP';
    public const Flop = 'Flop';
    public const GEL = 'GEL';
    public const Gel = 'Gel';
    public const PEERING_CZ = 'PEERING_CZ';
    public const PeeringCz = 'PeeringCz';

    public static function endpoint(): string
    {
        return 'flop';
    }

    public static function config(): string
    {
        return \Safe\realpath(__DIR__ . '/../../../../../sailor.php');
    }
}
