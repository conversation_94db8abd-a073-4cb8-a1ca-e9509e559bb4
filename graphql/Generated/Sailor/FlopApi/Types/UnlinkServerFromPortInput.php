<?php declare(strict_types=1);

namespace Generated\Sailor\FlopApi\Types;

/**
 * @property int|string $portId
 * @property int|string $serverId
 */
class UnlinkServerFromPortInput extends \Spawnia\Sailor\ObjectLike
{
    /**
     * @param int|string $portId
     * @param int|string $serverId
     */
    public static function make($portId, $serverId): self
    {
        $instance = new self;

        if ($portId !== self::UNDEFINED) {
            $instance->portId = $portId;
        }
        if ($serverId !== self::UNDEFINED) {
            $instance->serverId = $serverId;
        }

        return $instance;
    }

    protected function converters(): array
    {
        static $converters;

        return $converters ??= [
            'portId' => new \Spawnia\Sailor\Convert\NonNullConverter(new \Spawnia\Sailor\Convert\IDConverter),
            'serverId' => new \Spawnia\Sailor\Convert\NonNullConverter(new \Spawnia\Sailor\Convert\IDConverter),
        ];
    }

    public static function endpoint(): string
    {
        return 'flop';
    }

    public static function config(): string
    {
        return \Safe\realpath(__DIR__ . '/../../../../../sailor.php');
    }
}
