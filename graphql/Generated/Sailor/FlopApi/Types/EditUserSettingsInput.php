<?php declare(strict_types=1);

namespace Generated\Sailor\FlopApi\Types;

/**
 * @property bool $hideBackboneOutages
 * @property bool $hidePreviousWeek
 * @property string|null $macFormatPreference
 */
class EditUserSettingsInput extends \Spawnia\Sailor\ObjectLike
{
    /**
     * @param bool $hideBackboneOutages
     * @param bool $hidePreviousWeek
     * @param string|null $macFormatPreference
     */
    public static function make(
        $hideBackboneOutages,
        $hidePreviousWeek,
        $macFormatPreference = 'Special default value that allows <PERSON> to differentiate between explicitly passing null and not passing a value at all.',
    ): self {
        $instance = new self;

        if ($hideBackboneOutages !== self::UNDEFINED) {
            $instance->hideBackboneOutages = $hideBackboneOutages;
        }
        if ($hidePreviousWeek !== self::UNDEFINED) {
            $instance->hidePreviousWeek = $hidePreviousWeek;
        }
        if ($macFormatPreference !== self::UNDEFINED) {
            $instance->macFormatPreference = $macFormatPreference;
        }

        return $instance;
    }

    protected function converters(): array
    {
        static $converters;

        return $converters ??= [
            'hideBackboneOutages' => new \Spawnia\Sailor\Convert\NonNullConverter(new \Spawnia\Sailor\Convert\BooleanConverter),
            'hidePreviousWeek' => new \Spawnia\Sailor\Convert\NonNullConverter(new \Spawnia\Sailor\Convert\BooleanConverter),
            'macFormatPreference' => new \Spawnia\Sailor\Convert\NullConverter(new \Spawnia\Sailor\Convert\EnumConverter),
        ];
    }

    public static function endpoint(): string
    {
        return 'flop';
    }

    public static function config(): string
    {
        return \Safe\realpath(__DIR__ . '/../../../../../sailor.php');
    }
}
