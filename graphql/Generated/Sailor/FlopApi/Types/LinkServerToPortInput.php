<?php declare(strict_types=1);

namespace Generated\Sailor\FlopApi\Types;

/**
 * @property string $linkType
 * @property int|string $portId
 * @property int|string $serverId
 */
class LinkServerToPortInput extends \Spawnia\Sailor\ObjectLike
{
    /**
     * @param string $linkType
     * @param int|string $portId
     * @param int|string $serverId
     */
    public static function make($linkType, $portId, $serverId): self
    {
        $instance = new self;

        if ($linkType !== self::UNDEFINED) {
            $instance->linkType = $linkType;
        }
        if ($portId !== self::UNDEFINED) {
            $instance->portId = $portId;
        }
        if ($serverId !== self::UNDEFINED) {
            $instance->serverId = $serverId;
        }

        return $instance;
    }

    protected function converters(): array
    {
        static $converters;

        return $converters ??= [
            'linkType' => new \Spawnia\Sailor\Convert\NonNullConverter(new \Spawnia\Sailor\Convert\EnumConverter),
            'portId' => new \Spawnia\Sailor\Convert\NonNullConverter(new \Spawnia\Sailor\Convert\IDConverter),
            'serverId' => new \Spawnia\Sailor\Convert\NonNullConverter(new \Spawnia\Sailor\Convert\IDConverter),
        ];
    }

    public static function endpoint(): string
    {
        return 'flop';
    }

    public static function config(): string
    {
        return \Safe\realpath(__DIR__ . '/../../../../../sailor.php');
    }
}
