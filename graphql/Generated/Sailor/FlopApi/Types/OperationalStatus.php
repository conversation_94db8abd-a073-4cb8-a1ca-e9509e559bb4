<?php declare(strict_types=1);

namespace Generated\Sailor\FlopApi\Types;

class OperationalStatus
{
    public const Dormant = 'Dormant';
    public const Down = 'Down';
    public const LowerLayerDown = 'LowerLayerDown';
    public const NotPresent = 'NotPresent';
    public const Testing = 'Testing';
    public const Unknown = 'Unknown';
    public const Up = 'Up';

    public static function endpoint(): string
    {
        return 'flop';
    }

    public static function config(): string
    {
        return \Safe\realpath(__DIR__ . '/../../../../../sailor.php');
    }
}
