<?php declare(strict_types=1);

namespace Generated\Sailor\FlopApi\Operations\AllAutonomousSystems\AutonomousSystems\Edges;

/**
 * @property \Generated\Sailor\FlopApi\Operations\AllAutonomousSystems\AutonomousSystems\Edges\Node\AutonomousSystem $node
 * @property string $__typename
 */
class AutonomousSystemEdge extends \Spawnia\Sailor\ObjectLike
{
    /**
     * @param \Generated\Sailor\FlopApi\Operations\AllAutonomousSystems\AutonomousSystems\Edges\Node\AutonomousSystem $node
     */
    public static function make($node): self
    {
        $instance = new self;

        if ($node !== self::UNDEFINED) {
            $instance->node = $node;
        }
        $instance->__typename = 'AutonomousSystemEdge';

        return $instance;
    }

    protected function converters(): array
    {
        static $converters;

        return $converters ??= [
            'node' => new \Spawnia\Sailor\Convert\NonNullConverter(new \Generated\Sailor\FlopApi\Operations\AllAutonomousSystems\AutonomousSystems\Edges\Node\AutonomousSystem),
            '__typename' => new \Spawnia\Sailor\Convert\NonNullConverter(new \Spawnia\Sailor\Convert\StringConverter),
        ];
    }

    public static function endpoint(): string
    {
        return 'flop';
    }

    public static function config(): string
    {
        return \Safe\realpath(__DIR__ . '/../../../../../../../../sailor.php');
    }
}
