<?php declare(strict_types=1);

namespace Generated\Sailor\FlopApi\Operations\AllAutonomousSystems\AutonomousSystems\Edges\Node;

/**
 * @property string $label
 * @property string $number
 * @property string $__typename
 */
class AutonomousSystem extends \Spawnia\Sailor\ObjectLike
{
    /**
     * @param string $label
     * @param string $number
     */
    public static function make($label, $number): self
    {
        $instance = new self;

        if ($label !== self::UNDEFINED) {
            $instance->label = $label;
        }
        if ($number !== self::UNDEFINED) {
            $instance->number = $number;
        }
        $instance->__typename = 'AutonomousSystem';

        return $instance;
    }

    protected function converters(): array
    {
        static $converters;

        return $converters ??= [
            'label' => new \Spawnia\Sailor\Convert\NonNullConverter(new \Spawnia\Sailor\Convert\StringConverter),
            'number' => new \Spawnia\Sailor\Convert\NonNullConverter(new \Spawnia\Sailor\Convert\IDConverter),
            '__typename' => new \Spawnia\Sailor\Convert\NonNullConverter(new \Spawnia\Sailor\Convert\StringConverter),
        ];
    }

    public static function endpoint(): string
    {
        return 'flop';
    }

    public static function config(): string
    {
        return \Safe\realpath(__DIR__ . '/../../../../../../../../../sailor.php');
    }
}
