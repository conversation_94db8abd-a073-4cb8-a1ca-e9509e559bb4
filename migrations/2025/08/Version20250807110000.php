<?php

declare(strict_types=1);

namespace Cdn77\Api\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20250807110000 extends AbstractMigration
{
    public function up(Schema $schema): void
    {
        $this->addSql(
            <<<'PSQL'
CREATE TABLE revoked_tokens (
    id uuid NOT NULL PRIMARY KEY,
    token character varying(256) NOT NULL,
    created_at timestamp NOT NULL,
    expires_at timestamp NOT NULL,
    product_id character varying(256) NOT NULL,
    customer_id uuid NOT NULL
);
PSQL,
        );

        $this->addSql(
            <<<'PSQL'
            ALTER TABLE revoked_tokens
                ADD CONSTRAINT revoked_tokens_product_id_token_unique UNIQUE (product_id, token);
            PSQL,
        );

        $this->addSql(
            <<<'PSQL'
            CREATE INDEX idx_revoked_tokens_created_at ON revoked_tokens USING btree (created_at);
            PSQL,
        );

        $this->addSql(
            <<<'PSQL'
            CREATE INDEX idx_revoked_tokens_expires_at ON revoked_tokens USING btree (expires_at);
            PSQL,
        );
    }

    public function down(Schema $schema): void
    {
        $this->throwIrreversibleMigrationException();
    }
}
