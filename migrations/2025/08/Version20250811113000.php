<?php

declare(strict_types=1);

namespace Cdn77\Api\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20250811113000 extends AbstractMigration
{
    public function up(Schema $schema): void
    {
        $this->addSql(
            <<<'PSQL'
            ALTER TABLE account_payment_settings
            ADD COLUMN credit_payment_enabled BOOLEAN NOT NULL DEFAULT TRUE,
            ADD COLUMN plan_payment_enabled BOOLEAN NOT NULL DEFAULT TRUE;
            PSQL,
        );
    }

    public function down(Schema $schema): void
    {
        $this->throwIrreversibleMigrationException();
    }
}
