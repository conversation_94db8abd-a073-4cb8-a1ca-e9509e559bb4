<?php

declare(strict_types=1);

namespace Cdn77\Api\Tests\Authentication\Domain\Command;

use Cdn77\Api\Authentication\Domain\Command\VerifyCredentials;
use Cdn77\Api\Authentication\Domain\Command\VerifyCredentialsHandler;
use Cdn77\Api\Authentication\Domain\Dto\AuthCredentials;
use Cdn77\Api\Authentication\Domain\Exception\InvalidCredentials;
use Cdn77\Api\Authentication\Domain\Exception\InvalidOtpProvided;
use Cdn77\Api\Authentication\Domain\Exception\MissingOtp;
use Cdn77\Api\Authentication\Domain\Factory\OneTimePasswordFactory;
use Cdn77\Api\Authentication\Domain\Repository\LoginLogRepository;
use Cdn77\Api\Authentication\Domain\TwoFactorAuthenticator;
use Cdn77\Api\Authentication\Domain\Value\AuthenticationCode;
use Cdn77\Api\Core\Domain\Entity\Customer\Credentials;
use Cdn77\Api\Core\Domain\Entity\Customer\Customer;
use Cdn77\Api\Core\Domain\Entity\Customer\CustomerId;
use Cdn77\Api\Core\Domain\Entity\Customer\CustomerUuid;
use Cdn77\Api\Core\Domain\Exception\ClapDomainException;
use Cdn77\Api\Core\Domain\Exception\Customer\CustomerIsSuspended;
use Cdn77\Api\Core\Domain\JWT\SignInTokenGenerator;
use Cdn77\Api\Core\Domain\Repository\CustomerRepository;
use Cdn77\Api\Core\Domain\Repository\SessionRepository;
use Cdn77\Api\Core\Domain\Session\SessionFactory;
use Cdn77\Api\Core\Domain\Value\Customer\EmailAddress;
use Cdn77\Api\Core\Domain\Value\Customer\PlainPassword;
use Cdn77\Api\Core\Domain\Value\Customer\SuspensionReason;
use Cdn77\Api\Core\Domain\Value\Fingerprint;
use Cdn77\Api\Core\Domain\Value\IpAddress;
use Cdn77\Api\CoreLibrary\GeoIp\GeoIpDatabaseReader;
use Cdn77\Api\Customer\Domain\ApiPasswordEncryptor;
use Cdn77\Api\Customer\Domain\PasswordHasher;
use Cdn77\Api\Tests\TestCase;
use Cdn77\TestUtils\Stub;
use DateTimeImmutable;
use Generator;
use Lcobucci\Clock\FrozenClock;
use Mockery;
use PHPUnit\Framework\Attributes\CoversClass;
use PHPUnit\Framework\Attributes\DataProvider;
use PHPUnit\Framework\Attributes\Group;
use Psr\Log\NullLogger;
use Symfony\Component\HttpFoundation\Request;

#[Group('unit')]
#[CoversClass(VerifyCredentialsHandler::class)]
#[CoversClass(VerifyCredentials::class)]
final class VerifyCredentialsHandlerTest extends TestCase
{
    private const string Secret = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';

    /** @param class-string<ClapDomainException>|null $expectedException */
    #[DataProvider('customerVerificationProvider')]
    public function testHandle(
        Customer|null $customer,
        AuthCredentials $authCredentials,
        string|null $expectedException,
    ): void {
        $tokenSecret = 'abcdefgh1234ABCDEFGH5678!=aBcDeF';
        $ipAddress = new IpAddress('127.0.0.1');

        $apiPasswordEncryptor = new ApiPasswordEncryptor('encryptionKey');
        $clock = new FrozenClock(new DateTimeImmutable('2022-09-12 00:00:00'));
        $customerRepository = Mockery::mock(CustomerRepository::class);
        $loginLogRepository = Mockery::mock(LoginLogRepository::class);
        $sessionFactory = new SessionFactory(
            $clock,
            new GeoIpDatabaseReader(new NullLogger(), '', 'test'),
            new PasswordHasher(4),
        );
        $sessionRepository = Mockery::mock(SessionRepository::class);
        $signInTokenGenerator = new SignInTokenGenerator('test', $tokenSecret);

        $customerRepository
            ->shouldReceive('findForEmail')
            ->once()
            ->with($authCredentials->emailAddress)
            ->andReturn($customer);

        if ($expectedException === null) {
            $loginLogRepository
                ->expects('add');
            $sessionRepository
                ->expects('add');
        }

        $command = new VerifyCredentials($authCredentials, $ipAddress, Fingerprint::fromRequest(new Request()));
        $handler = new VerifyCredentialsHandler(
            $clock,
            $customerRepository,
            $loginLogRepository,
            new TwoFactorAuthenticator(
                $apiPasswordEncryptor,
                new OneTimePasswordFactory($clock),
            ),
            $signInTokenGenerator,
            $sessionFactory,
            $sessionRepository,
        );

        self::assertFalse($expectedException === null && $customer === null);

        if ($expectedException !== null) {
            $this->expectException($expectedException);
        }

        $verificationResult = $handler->handle($command);

        if ($customer === null) {
            return;
        }

        self::assertSame($customer->getNewId(), $verificationResult->customerId);
    }

    public static function customerVerificationProvider(): Generator
    {
        $emailConfirmedAt = new DateTimeImmutable('2022-01-08 15:30:30');
        $apiPasswordEncryptor = new ApiPasswordEncryptor('encryptionKey');
        $passwordHasher = new PasswordHasher(4);
        $customerId = CustomerId::fromInteger(1);
        $customerUuid = CustomerUuid::fromString('7c2ea3ff-9f50-48c4-9272-efc21f6ae03f');
        $email = EmailAddress::fromString('<EMAIL>');
        $password = new PlainPassword('passwd');

        $credentials = Stub::create(Credentials::class, [
            Credentials::FieldEmail => $email->get(),
            Credentials::FieldPassword => $passwordHasher->hash($password)->toString(),
        ]);

        $customer = Stub::create(Customer::class, [
            Customer::FieldId => $customerId->toInt(),
            Customer::FieldNewId => $customerUuid->toUid(),
            Customer::FieldCredentials => $credentials,
            Customer::FieldTwoFactorAuthSecret => null,
            Customer::FieldSuspendedAt => null,
            Customer::FieldEmailConfirmedAt => $emailConfirmedAt,
        ]);

        $twoFaCustomer = Stub::create(Customer::class, [
            Customer::FieldId => $customerId->toInt(),
            Customer::FieldNewId => $customerUuid->toUid(),
            Customer::FieldCredentials => $credentials,
            Customer::FieldTwoFactorAuthSecret => $apiPasswordEncryptor->encrypt(self::Secret)->toString(),
            Customer::FieldSuspendedAt => null,
            Customer::FieldEmailConfirmedAt => $emailConfirmedAt,
        ]);

        $unsuspendableCustomer = Stub::create(Customer::class, [
            Customer::FieldId => $customerId->toInt(),
            Customer::FieldNewId => $customerUuid->toUid(),
            Customer::FieldCredentials => $credentials,
            Customer::FieldTwoFactorAuthSecret => null,
            Customer::FieldSuspendedAt => new DateTimeImmutable('2022-01-08 15:30:30'),
            Customer::FieldSuspensionReason => SuspensionReason::ClientRequest,
            Customer::FieldEmailConfirmedAt => $emailConfirmedAt,
        ]);

        $debtCustomer = Stub::create(Customer::class, [
            Customer::FieldId => $customerId->toInt(),
            Customer::FieldNewId => $customerUuid->toUid(),
            Customer::FieldCredentials => $credentials,
            Customer::FieldTwoFactorAuthSecret => null,
            Customer::FieldSuspendedAt => new DateTimeImmutable('2022-01-08 15:30:30'),
            Customer::FieldSuspensionReason => SuspensionReason::InDebt,
            Customer::FieldEmailConfirmedAt => $emailConfirmedAt,
        ]);

        $validOtp = AuthenticationCode::fromString('784818');
        $inValidOtp = AuthenticationCode::fromString('123456');

        yield 'Verified Customer' => [
            $customer,
            new AuthCredentials($email, $password, null, false),
            null,
        ];

        yield 'Verified Customer With Two-FA' => [
            $twoFaCustomer,
            new AuthCredentials($email, $password, $validOtp, false),
            null,
        ];

        yield 'Bad Otp For Customer With Two-FA' => [
            $twoFaCustomer,
            new AuthCredentials($email, $password, $inValidOtp, false),
            InvalidOtpProvided::class,
        ];

        yield 'Missing Otp For Customer With Two-FA' => [
            $twoFaCustomer,
            new AuthCredentials($email, $password, null, false),
            MissingOtp::class,
        ];

        yield 'Suspended Customer' => [
            $unsuspendableCustomer,
            new AuthCredentials($email, $password, null, false),
            CustomerIsSuspended::class,
        ];

        yield 'Debt Customer is verified' => [
            $debtCustomer,
            new AuthCredentials($email, $password, null, false),
            null,
        ];

        yield 'Non existing customer' => [
            null,
            new AuthCredentials($email, $password, null, false),
            InvalidCredentials::class,
        ];

        yield 'Invalid password' => [
            $customer,
            new AuthCredentials($email, new PlainPassword('wrongpasswd'), null, false),
            InvalidCredentials::class,
        ];
    }
}
