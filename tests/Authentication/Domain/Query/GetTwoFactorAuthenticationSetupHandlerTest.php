<?php

declare(strict_types=1);

namespace Cdn77\Api\Tests\Authentication\Domain\Query;

use Cdn77\Api\Authentication\Domain\Dto\QRCode;
use Cdn77\Api\Authentication\Domain\Factory\OneTimePasswordFactory;
use Cdn77\Api\Authentication\Domain\Factory\QRCodeFactory;
use Cdn77\Api\Authentication\Domain\Query\GetTwoFactorAuthenticationSetup;
use Cdn77\Api\Authentication\Domain\Query\GetTwoFactorAuthenticationSetupHandler;
use Cdn77\Api\Core\Domain\Entity\Customer\Credentials;
use Cdn77\Api\Core\Domain\Entity\Customer\Customer;
use Cdn77\Api\Core\Domain\Entity\Customer\CustomerUuid;
use Cdn77\Api\Core\Domain\Repository\CustomerRepository;
use Cdn77\Api\Tests\TestCase;
use Cdn77\TestUtils\Stub;
use DateTimeImmutable;
use <PERSON><PERSON>bucci\Clock\FrozenClock;
use Mockery;
use PHPUnit\Framework\Attributes\CoversClass;
use PHPUnit\Framework\Attributes\Group;

#[Group('unit')]
#[CoversClass(GetTwoFactorAuthenticationSetupHandler::class)]
#[CoversClass(GetTwoFactorAuthenticationSetup::class)]
final class GetTwoFactorAuthenticationSetupHandlerTest extends TestCase
{
    public function testHandleGetSetup(): void
    {
        $customerId = CustomerUuid::fromString('4d9fd3cc-bb1b-4c52-a3cf-076f410dce03');
        $customer = Stub::create(Customer::class, [
            Customer::FieldCredentials => Stub::create(
                Credentials::class,
                [Credentials::FieldEmail => '<EMAIL>'],
            ),
        ]);
        $qrCode = new QRCode('data:image/svg+xml;base64,PD94bWwg');

        $customerRepository = Mockery::mock(CustomerRepository::class);
        $qrCodeFactory = Mockery::mock(QRCodeFactory::class);

        $customerRepository
            ->shouldReceive('getForId')
            ->with($customerId)
            ->andReturn($customer);

        $qrCodeFactory
            ->shouldReceive('createSvg')
            ->andReturn($qrCode);

        $query = new GetTwoFactorAuthenticationSetup($customerId);
        $handler = new GetTwoFactorAuthenticationSetupHandler(
            $customerRepository,
            new OneTimePasswordFactory(new FrozenClock(new DateTimeImmutable('2025-04-01 13:21:50'))),
            $qrCodeFactory,
        );

        $twoFactorAuthenticationSetup = $handler->handle($query);
        $secret = $twoFactorAuthenticationSetup->secret;

        self::assertSame($qrCode, $twoFactorAuthenticationSetup->qrCode);
        self::assertMatchesRegularExpression('~[A-Z1-9]{103}~', $secret->value);
    }
}
