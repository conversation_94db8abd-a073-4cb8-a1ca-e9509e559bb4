<?php

declare(strict_types=1);

namespace Cdn77\Api\Tests\ObjectStorage\Domain\Dto;

use Cdn77\Api\Core\Domain\Entity\Origin\Origin;
use Cdn77\Api\Core\Domain\Entity\Origin\OriginId;
use Cdn77\Api\ObjectStorage\Domain\Dto\BucketsByHost;
use Cdn77\Api\Tests\StubFactory\OriginFactory;
use Cdn77\Api\Tests\TestCase;
use DateTimeImmutable;
use Generator;
use PHPUnit\Framework\Attributes\CoversClass;
use PHPUnit\Framework\Attributes\DataProvider;
use PHPUnit\Framework\Attributes\Group;

use function count;

#[CoversClass(BucketsByHost::class)]
#[Group('unit')]
final class BucketsByHostTest extends TestCase
{
    /**
     * @param iterable<Origin> $origins
     * @param array<string, list<string>> $expectedBucketsByHost
     */
    #[DataProvider('fromOriginsProvider')]
    public function testFromOrigins(
        iterable $origins,
        array $expectedBucketsByHost,
    ): void {
        $now = new DateTimeImmutable('2025-05-27 10:00:00');
        $bucketsByHost = BucketsByHost::fromOrigins($origins, $now)->map;

        self::assertCount(count($expectedBucketsByHost), $bucketsByHost);

        foreach ($bucketsByHost as $host => $buckets) {
            self::assertArrayHasKey($host->value, $expectedBucketsByHost);
            $expectedBuckets = $expectedBucketsByHost[$host->value];
            self::assertCount(count($expectedBuckets), $buckets);

            foreach ($buckets as $bucketScope) {
                self::assertContains($bucketScope->bucketName->value, $expectedBuckets);
            }
        }
    }

    public static function fromOriginsProvider(): Generator
    {
        yield 'empty origins' => [
            [],
            [],
        ];

        yield 'one host one bucket' => [
            [
                OriginFactory::create(
                    OriginId::new(),
                    'eu.test-storage.com',
                    'bucket-name',
                    new DateTimeImmutable('2025-05-27 10:00:00'),
                ),
            ],
            ['eu.test-storage.com' => ['bucket-name']],
        ];

        yield 'same bucket in two clusters' => [
            [
                OriginFactory::create(
                    OriginId::new(),
                    'eu.test-storage.com',
                    'bucket-name',
                    new DateTimeImmutable('2025-05-27 10:00:00'),
                ),
                OriginFactory::create(
                    OriginId::new(),
                    'us.test-storage.com',
                    'bucket-name',
                    new DateTimeImmutable('2025-05-27 10:00:00'),
                ),
            ],
            [
                'eu.test-storage.com' => ['bucket-name'],
                'us.test-storage.com' => ['bucket-name'],
            ],
        ];

        yield 'more buckets in two clusters' => [
            [
                OriginFactory::create(
                    OriginId::new(),
                    'eu.test-storage.com',
                    'bucket-name-1',
                    new DateTimeImmutable('2025-05-27 10:00:00'),
                ),
                OriginFactory::create(
                    OriginId::new(),
                    'eu.test-storage.com',
                    'bucket-name-2',
                    new DateTimeImmutable('2025-05-27 11:00:00'),
                ),
                OriginFactory::create(
                    OriginId::new(),
                    'eu.test-storage.com',
                    'bucket-name-4',
                    new DateTimeImmutable('2025-05-27 11:30:00'),
                ),
                OriginFactory::create(
                    OriginId::new(),
                    'us.test-storage.com',
                    'bucket-name-1',
                    new DateTimeImmutable('2025-05-20 09:00:00'),
                ),
                OriginFactory::create(
                    OriginId::new(),
                    'us.test-storage.com',
                    'bucket-name-2',
                    new DateTimeImmutable('2025-05-20 20:00:00'),
                ),
                OriginFactory::create(
                    OriginId::new(),
                    'us.test-storage.com',
                    'bucket-name-3',
                    new DateTimeImmutable('2025-05-20 21:00:00'),
                ),
            ],
            [
                'eu.test-storage.com' => ['bucket-name-1', 'bucket-name-2', 'bucket-name-4'],
                'us.test-storage.com' => ['bucket-name-1', 'bucket-name-2', 'bucket-name-3'],
            ],
        ];
    }
}
