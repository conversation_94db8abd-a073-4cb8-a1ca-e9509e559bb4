<?php

declare(strict_types=1);

namespace Cdn77\Api\Tests\ObjectStorage\Domain\Query;

use Cdn77\Api\Core\Domain\Ceph\AdminApi;
use Cdn77\Api\Core\Domain\Ceph\ClientApi;
use Cdn77\Api\Core\Domain\Entity\Customer\Customer;
use Cdn77\Api\Core\Domain\Entity\Customer\CustomerUuid;
use Cdn77\Api\Core\Domain\Entity\ObjectStorage\AccessKey;
use Cdn77\Api\Core\Domain\Entity\ObjectStorage\RgwCluster;
use Cdn77\Api\Core\Domain\Entity\ObjectStorage\RgwClusterId;
use Cdn77\Api\Core\Domain\Entity\Origin\AdminCredentials;
use Cdn77\Api\Core\Domain\Entity\Origin\Origin;
use Cdn77\Api\Core\Domain\Repository\CustomerRepository;
use Cdn77\Api\Core\Domain\Repository\ObjectStorage\RgwClusterRepository;
use Cdn77\Api\Core\Domain\Resolver\AffectedCustomerResolver;
use Cdn77\Api\Core\Domain\Resolver\RgwClusterResolver;
use Cdn77\Api\Core\Domain\Validation\TeamMemberAccessValidator;
use Cdn77\Api\Core\Domain\Value\ObjectStorage\ObjectStorageCredentials;
use Cdn77\Api\Core\Domain\Value\ObjectStorage\ObjectStorageUserName;
use Cdn77\Api\ObjectStorage\Domain\Query\FindAccessKeys;
use Cdn77\Api\ObjectStorage\Domain\Query\FindAccessKeysHandler;
use Cdn77\Api\ObjectStorage\Domain\Repository\AccessKeysRepository;
use Cdn77\Api\ObjectStorage\Domain\Repository\ObjectStorageOriginRepository;
use Cdn77\Api\ObjectStorage\Domain\Validation\ObjectStorageAccessValidator;
use Cdn77\Api\ObjectStorage\Domain\Value\AccessType;
use Cdn77\Api\ObjectStorage\Domain\Value\Policy;
use Cdn77\Api\ObjectStorage\Domain\Value\PolicyEffect;
use Cdn77\Api\Origin\Domain\Validation\OriginAccessValidator;
use Cdn77\Api\Origin\Domain\Value\S3AccessKey;
use Cdn77\Api\Origin\Domain\Value\S3Secret;
use Cdn77\Api\Tests\TestCase;
use Cdn77\TestUtils\Stub;
use DateTimeImmutable;
use Ds\Set;
use Mockery;
use OutOfBoundsException;
use PHPUnit\Framework\Attributes\CoversClass;
use PHPUnit\Framework\Attributes\Group;
use Psr\Log\NullLogger;
use Symfony\Component\Uid\Uuid;
use Throwable;

use function iterator_to_array;

#[Group('unit')]
#[CoversClass(FindAccessKeysHandler::class)]
#[CoversClass(FindAccessKeys::class)]
final class FindAccessKeysHandlerTest extends TestCase
{
    /**
     * @throws OutOfBoundsException
     * @throws Throwable
     */
    public function testFind(): void
    {
        $customerId = CustomerUuid::fromString('60050ccf-8f0d-45a7-bc79-fe79c15d40b7');
        $clusterHost = 'cluster.host.com';
        $bucketAName = 'bucket-a';
        $bucketBName = 'bucket-b';

        $customer = Stub::create(Customer::class, [Customer::FieldNewId => $customerId->toUid()]);
        $adminCredentials = Stub::create(AdminCredentials::class);
        $clusterId = RgwClusterId::fromString('b79b8df7-bddf-4093-8315-c89528090ed0');
        $cluster = Stub::create(RgwCluster::class, [
            RgwCluster::FieldId => $clusterId->toUid(),
            RgwCluster::FieldHost => $clusterHost,
            RgwCluster::FieldAdminCredentials => $adminCredentials,
        ]);
        $userCredentials = new ObjectStorageCredentials(
            ObjectStorageUserName::fromString('test'),
            new S3AccessKey('abcd'),
            new S3Secret('absc'),
            $clusterHost,
        );
        $originA = Stub::create(Origin::class, [
            Origin::FieldS3BucketName => $bucketAName,
            Origin::FieldHost => $clusterHost,
            Origin::FieldCustomerId => $customerId->toUid(),
        ]);
        $originB = Stub::create(Origin::class, [
            Origin::FieldS3BucketName => $bucketBName,
            Origin::FieldHost => $clusterHost,
            Origin::FieldCustomerId => $customerId->toUid(),
        ]);
        $bucketAPolicies = new Set([
            new Policy(
                PolicyEffect::Allow,
                AccessType::Read,
                'some-user',
                ObjectStorageUserName::fromString('7cfd9c16-ce57-4505-bf6b-7f6981d1282a'),
            ),
        ]);
        $bucketBPolicies = new Set([]);
        $user = Stub::create(AccessKey::class, [
            AccessKey::FieldAccessKeyId => 'AMAI12312452356',
            AccessKey::FieldId => Uuid::fromString('7cfd9c16-ce57-4505-bf6b-7f6981d1282a'),
            AccessKey::FieldLabel => 'My S3 Key',
            AccessKey::FieldClusterId => $clusterId->toUid(),
            AccessKey::FieldCreatedAt => new DateTimeImmutable('2023-05-05 05:55:50'),
            AccessKey::FieldCreatedBy => $customerId->toUid(),
            AccessKey::FieldCustomerId => $customerId->toUid(),
            AccessKey::FieldNote => null,
        ]);

        $accessKeysRepository = Mockery::mock(AccessKeysRepository::class);
        $adminApi = Mockery::mock(AdminApi::class);
        $clientApi = Mockery::mock(ClientApi::class);
        $customerRepository = Mockery::mock(CustomerRepository::class);
        $teamMemberAccessValidator = Mockery::mock(TeamMemberAccessValidator::class);
        $objectStorageAccessValidator = new ObjectStorageAccessValidator(
            $customerRepository,
            $teamMemberAccessValidator,
        );
        $originAccessValidator = new OriginAccessValidator($customerRepository, $teamMemberAccessValidator);
        $objectStorageOriginRepository = Mockery::mock(ObjectStorageOriginRepository::class);
        $rgwClusterRepository = Mockery::mock(RgwClusterRepository::class);
        $rgwClusterResolver = new RgwClusterResolver($rgwClusterRepository);
        $affectedCustomerResolver = new AffectedCustomerResolver($customerRepository);

        $customerRepository
            ->expects('getForId')
            ->twice()
            ->with($customerId)
            ->andReturn($customer);
        $objectStorageOriginRepository
            ->expects('findForCustomer')
            ->with($customerId)
            ->andYield($originA, $originB);
        $rgwClusterRepository
            ->expects('findForCustomer')
            ->andReturn([]);
        $rgwClusterRepository
            ->expects('findPublicPublished')
            ->andReturn([$cluster]);
        $adminApi
            ->expects('getUserCredentials')
            ->with($cluster, ObjectStorageUserName::fromCustomerId($customerId))
            ->andReturn($userCredentials);
        $clientApi
            ->expects('getBucketPolicy')
            ->with($userCredentials, $bucketAName)
            ->andReturn($bucketAPolicies);
        $clientApi
            ->expects('getBucketPolicy')
            ->with($userCredentials, $bucketBName)
            ->andReturn($bucketBPolicies);
        $accessKeysRepository
            ->expects('findAllForCustomer')
            ->with($customerId)
            ->andYield($user);

        $handler = new FindAccessKeysHandler(
            $accessKeysRepository,
            $adminApi,
            $affectedCustomerResolver,
            $clientApi,
            $customerRepository,
            $objectStorageAccessValidator,
            $originAccessValidator,
            $objectStorageOriginRepository,
            $rgwClusterResolver,
            new NullLogger(),
        );
        $query = new FindAccessKeys($customerId);

        $result = iterator_to_array($handler->handle($query));

        self::assertCount(1, $result);
        self::assertCount(1, $result[0]->policies);
        self::assertEquals($bucketAPolicies->toArray(), $result[0]->policies->toArray());
    }
}
