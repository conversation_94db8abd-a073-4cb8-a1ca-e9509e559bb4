<?php

declare(strict_types=1);

namespace Cdn77\Api\Tests\Utils;

use Symfony\Component\PropertyAccess\Exception\NoSuchPropertyException;
use Symfony\Component\PropertyAccess\PropertyAccessorInterface;
use Symfony\Component\PropertyAccess\PropertyPathInterface;

final class NoopPropertyAccessor implements PropertyAccessorInterface
{
    public function setValue(
        object|array &$objectOrArray,
        string|PropertyPathInterface $propertyPath,
        mixed $value,
    ): void {
        throw new NoSuchPropertyException();
    }

    public function getValue(object|array $objectOrArray, string|PropertyPathInterface $propertyPath): bool
    {
        throw new NoSuchPropertyException();
    }

    public function isWritable(object|array $objectOrArray, string|PropertyPathInterface $propertyPath): bool
    {
        throw new NoSuchPropertyException();
    }

    public function isReadable(object|array $objectOrArray, string|PropertyPathInterface $propertyPath): bool
    {
        throw new NoSuchPropertyException();
    }
}
