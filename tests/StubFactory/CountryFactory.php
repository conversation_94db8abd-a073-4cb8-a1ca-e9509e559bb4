<?php

declare(strict_types=1);

namespace Cdn77\Api\Tests\StubFactory;

use Cdn77\Api\Core\Domain\Continent\Value\ContinentCode;
use Cdn77\Api\Core\Domain\Entity\Invoice\Country;
use Cdn77\Api\Invoice\Domain\Value\CountryIsoCode;
use Cdn77\TestUtils\Stub;

final readonly class CountryFactory
{
    public static function create(
        CountryIsoCode $countryIsoCode,
        string $countryShortName,
        ContinentCode $continentCode,
    ): Country {
        return Stub::create(Country::class, [
            Country::FieldIso => $countryIsoCode->get(),
            Country::FieldShortName => $countryShortName,
            Country::FieldContinentCode => $continentCode,
        ]);
    }
}
