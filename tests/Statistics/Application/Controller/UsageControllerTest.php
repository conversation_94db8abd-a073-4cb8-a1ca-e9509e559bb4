<?php

declare(strict_types=1);

namespace Cdn77\Api\Tests\Statistics\Application\Controller;

use Cdn77\Api\Core\Domain\Entity\Customer\Customer;
use Cdn77\Api\Statistics\Application\Controller\UsageController;
use Cdn77\Api\Tests\TestCase;
use Cdn77\Api\Tests\Utils\TokenGenerator;
use Cdn77\Api\Tests\Utils\WithBrowser;
use PHPUnit\Framework\Attributes\CoversClass;
use PHPUnit\Framework\Attributes\Group;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Webmozart\Assert\Assert;

#[CoversClass(UsageController::class)]
#[Group('integration')]
final class UsageControllerTest extends TestCase
{
    use WithBrowser;

    public function testNonExistentStorage(): void
    {
        ['testing_customer' => $customer] =
            $this->loadFixturesFromFile(__DIR__ . '/fixtures/nonExistentStorage.yaml');

        Assert::isInstanceOf($customer, Customer::class);

        $token = TokenGenerator::generateApplication($customer->getNewId());
        $response = $this->performHttpRequest(
            Request::METHOD_GET,
            '/v3/storage/0003107c-9566-4f13-bc05-96ed371de22a/usage',
            $this->prepareAuthHeaders($token),
        );

        self::assertHttpCode(Response::HTTP_NOT_FOUND, $response);
    }

    public function testNonExistentStorageWithHistory(): void
    {
        ['testing_customer' => $customer] =
            $this->loadFixturesFromFile(__DIR__ . '/fixtures/nonExistentStorage.yaml');
        Assert::isInstanceOf($customer, Customer::class);

        $token = TokenGenerator::generateApplication($customer->getNewId());
        $response = $this->performHttpRequest(
            Request::METHOD_GET,
            '/v3/storage/0003107c-9566-4f13-bc05-96ed371de22a/usage?from=2018-08-01T01:01:01Z&to=2018-08-01T04:01:01Z',
            $this->prepareAuthHeaders($token),
        );

        self::assertHttpCode(Response::HTTP_NOT_FOUND, $response);
    }

    public function testUsageForDeletedStorageNotFound(): void
    {
        ['testing_customer' => $customer] = $this->loadFixtures();

        Assert::isInstanceOf($customer, Customer::class);

        $token = TokenGenerator::generateApplication($customer->getNewId());
        $response = $this->performHttpRequest(
            Request::METHOD_GET,
            '/v3/storage/f5b04cf1-247d-4893-a2af-9911f9a5645e/usage',
            $this->prepareAuthHeaders($token),
        );

        self::assertHttpCode(Response::HTTP_NOT_FOUND, $response);
    }

    public function testUsageHistoryForDeletedStorageNotFound(): void
    {
        ['testing_customer' => $customer] = $this->loadFixtures();

        Assert::isInstanceOf($customer, Customer::class);

        $token = TokenGenerator::generateApplication($customer->getNewId());
        $response = $this->performHttpRequest(
            Request::METHOD_GET,
            '/v3/storage/f5b04cf1-247d-4893-a2af-9911f9a5645e/usage?from=2018-08-01T01:01:01Z&to=2018-08-01T04:01:01Z',
            $this->prepareAuthHeaders($token),
        );

        self::assertHttpCode(Response::HTTP_NOT_FOUND, $response);
    }

    public function testUsageForStorageNotFound(): void
    {
        ['testing_customer' => $customer] = $this->loadFixtures();

        Assert::isInstanceOf($customer, Customer::class);

        $token = TokenGenerator::generateApplication($customer->getNewId());
        $response = $this->performHttpRequest(
            Request::METHOD_GET,
            '/v3/storage/9c1df08c-e2bb-4ea9-897a-ee42f17c5507/usage',
            $this->prepareAuthHeaders($token),
        );

        self::assertHttpCode(Response::HTTP_NOT_FOUND, $response);
    }

    public function testUsageHistoryForStorageNotFound(): void
    {
        ['testing_customer' => $customer] = $this->loadFixtures();

        Assert::isInstanceOf($customer, Customer::class);

        $token = TokenGenerator::generateApplication($customer->getNewId());
        $response = $this->performHttpRequest(
            Request::METHOD_GET,
            '/v3/storage/9c1df08c-e2aa-4ea9-897a-ee42f17c5507/usage?from=2014-08-01T01:01:01Z&to=2014-08-01T04:01:01Z',
            $this->prepareAuthHeaders($token),
        );

        self::assertHttpCode(Response::HTTP_NOT_FOUND, $response);
    }

    public function testUsage(): void
    {
        ['testing_customer' => $customer] = $this->loadFixtures();

        Assert::isInstanceOf($customer, Customer::class);

        $token = TokenGenerator::generateApplication($customer->getNewId());
        $response = $this->performHttpRequest(
            Request::METHOD_GET,
            '/v3/storage/9c1df08c-e2bb-4ea9-897a-ee42f17c5507/usage',
            $this->prepareAuthHeaders($token),
        );

        self::assertHttpCode(Response::HTTP_OK, $response);
        self::assertJsonResponse(
            [
                [
                    'space' => 200,
                    'nodes' => 50,
                    'time' => '2018-01-12T00:00:00Z',
                ],
            ],
            $response,
        );
    }

    public function testUsageHistory(): void
    {
        ['testing_customer' => $customer] = $this->loadFixtures();

        Assert::isInstanceOf($customer, Customer::class);

        $token = TokenGenerator::generateApplication($customer->getNewId());
        $response = $this->performHttpRequest(
            Request::METHOD_GET,
            '/v3/storage/9c1df08c-e2bb-4ea9-897a-ee42f17c5507/usage?from=2018-08-01T01:01:01Z&to=2018-08-01T04:01:01Z',
            $this->prepareAuthHeaders($token),
        );

        self::assertHttpCode(Response::HTTP_OK, $response);
        self::assertJsonResponse(
            [
                [
                    'space' => 5494442576788,
                    'nodes' => 983,
                    'time' => '2018-08-01T04:01:01Z',
                ],
                [
                    'space' => 494404952576,
                    'nodes' => 600,
                    'time' => '2018-08-01T03:01:01Z',
                ],
                [
                    'space' => 461404952574,
                    'nodes' => 590,
                    'time' => '2018-08-01T02:01:01Z',
                ],
                [
                    'space' => 2576,
                    'nodes' => 54,
                    'time' => '2018-08-01T01:01:01Z',
                ],
            ],
            $response,
        );
    }
}
