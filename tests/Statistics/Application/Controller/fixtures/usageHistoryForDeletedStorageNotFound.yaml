include:
    - ../../../../Storage/Application/Controller/fixtures/deletedStorageNotFound.yaml

Cdn77\Api\Core\Domain\Entity\Statistics\Usage:
    usage_hour_01:
        __construct: false
        space: 2576
        nodes: 54
    usage_hour_02:
        __construct: false
        space: 461404952574
        nodes: 590
    usage_hour_03:
        __construct: false
        space: 494404952576
        nodes: 600
    usage_hour_04:
        __construct: false
        space: 5494442576788
        nodes: 983

Cdn77\Api\Core\Domain\Entity\Statistics\Statistics:
    statistic_hour_01:
        __construct: false
        id: 1
        storageSecret: 'deletedStorageAbc'
        time: <identity(new DateTimeImmutable('2018-08-01 01:01:01', new DateTimeZone('UTC')))>
        usage: '@usage_hour_01'

    statistic_hour_02:
        __construct: false
        id: 2
        storageSecret: 'deletedStorageAbc'
        time: <identity(new DateTimeImmutable('2018-08-01 02:01:01', new DateTimeZone('UTC')))>
        usage: '@usage_hour_02'
    statistic_hour_03:
        __construct: false
        id: 3
        storageSecret: 'deletedStorageAbc'
        time: <identity(new DateTimeImmutable('2018-08-01 03:01:01', new DateTimeZone('UTC')))>
        usage: '@usage_hour_03'
    statistic_hour_04:
        __construct: false
        id: 4
        storageSecret: 'deletedStorageAbc'
        time: <identity(new DateTimeImmutable('2018-08-01 04:01:01', new DateTimeZone('UTC')))>
        usage: '@usage_hour_04'
