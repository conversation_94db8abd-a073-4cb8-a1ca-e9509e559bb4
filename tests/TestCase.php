<?php

declare(strict_types=1);

namespace Cdn77\Api\Tests;

use Brick\Math\Exception\MathException;
use Brick\Money\AbstractMoney;
use Brick\Money\Exception\MoneyException;
use Cdn77\Api\Tests\ORMFixtures\EntityFixtureFactoryRegistry;
use Cdn77\Api\Tests\Utils\TraitsSetupAndTearDown;
use DateTimeInterface;
use Mockery\Adapter\Phpunit\MockeryPHPUnitIntegration;
use PHPUnit\Framework\TestCase as BaseTestCase;

use function sprintf;

abstract class TestCase extends BaseTestCase
{
    use MockeryPHPUnitIntegration;
    use TraitsSetupAndTearDown;

    final public static function assertDateEquals(DateTimeInterface $expectedDate, DateTimeInterface $actualDate): void
    {
        self::assertSame($expectedDate->getTimestamp(), $actualDate->getTimestamp());
    }

    final public static function assertMoneyEquals(AbstractMoney $expectedMoney, AbstractMoney $actualMoney): void
    {
        try {
            $equals = $expectedMoney->isEqualTo($actualMoney);
        } catch (MathException | MoneyException $e) {
            $equals = false;
            $exceptionMessage = $e->getMessage();
        }

        self::assertTrue(
            $equals,
            sprintf(
                "Failed asserting that '%s' is identical to '%s'.%s",
                $expectedMoney->__toString(),
                $actualMoney->__toString(),
                ' ' . ($exceptionMessage ?? ''),
            ),
        );
    }

    protected function setUp(): void
    {
        $this->setUpTraits();

        parent::setUp();
    }

    protected function tearDown(): void
    {
        $this->tearDownTraits();
        EntityFixtureFactoryRegistry::reset();

        parent::tearDown();
    }
}
