<?php

declare(strict_types=1);

namespace Cdn77\Api\Tests\CoreLibrary\Doctrine\Dbal\Middleware\Configuration\Value;

use Cdn77\Api\CoreLibrary\Doctrine\Dbal\Middleware\Configuration\Value\ApplicationName;
use Cdn77\Api\Tests\TestCase;
use Generator;
use PHPUnit\Framework\Attributes\CoversClass;
use PHPUnit\Framework\Attributes\DataProvider;
use PHPUnit\Framework\Attributes\Group;

#[Group('unit')]
#[CoversClass(ApplicationName::class)]
final class ApplicationNameTest extends TestCase
{
    #[DataProvider('provideInputs')]
    public function test(string $input, string $expected): void
    {
        $applicationName = new ApplicationName($input);

        self::assertSame($expected, $applicationName->value);
    }

    /** @return Generator<string, array{string, string}> */
    public static function provideInputs(): Generator
    {
        yield 'command name' => [
            'ns:someCommand-name',
            'ns:someCommand-name',
        ];

        yield 'uri path' => [
            '/some/endpoint-path',
            '/some/endpoint-path',
        ];

        yield 'uri path and query' => [
            '/endpoint?some=parameter1&other[]=parameter2',
            '/endpoint_some_parameter1_other___parameter2',
        ];

        yield 'truncate length' => [
            'this exceeds maximal length of the application name and will be truncated accordingly',
            'this_exceeds_maximal_length_of_the_application_name_and_will_be_',
        ];

        yield 'multi line' => [
            "firstline\nsecondline\nthirdline",
            'firstline_secondline_thirdline',
        ];

        yield 'flatten quotes and backslashes' => [
            '\\\'"`',
            '____',
        ];

        yield 'utf8 mess' => [
            'ěešsčcřržzýyáaíiée',
            '_e_s_c_r_z_y_a_i_e',
        ];
    }
}
