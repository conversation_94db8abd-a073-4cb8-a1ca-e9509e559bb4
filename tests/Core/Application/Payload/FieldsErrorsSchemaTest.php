<?php

declare(strict_types=1);

namespace Cdn77\Api\Tests\Core\Application\Payload;

use Cdn77\Api\Cdn\Domain\Exception\InstantSslToggleFailed;
use Cdn77\Api\Core\Application\Payload\FieldsErrorsSchema;
use Cdn77\Api\Core\Domain\Exception\ClapDomainException;
use Cdn77\Api\Core\Domain\Exception\SupportsFieldErrorResponse;
use Cdn77\Api\Core\Domain\Value\Enums\EnumValues;
use Cdn77\Api\Job\Domain\Value\JobType;
use Cdn77\Api\Stats\Domain\Value\StatsType;
use Cdn77\Api\Tests\TestCase;
use Cdn77\Api\Tests\Utils\WithSerializer;
use Generator;
use PHPUnit\Framework\Attributes\CoversClass;
use PHPUnit\Framework\Attributes\DataProvider;
use PHPUnit\Framework\Attributes\Group;
use RuntimeException;

#[CoversClass(FieldsErrorsSchema::class)]
#[Group('integration')]
final class FieldsErrorsSchemaTest extends TestCase
{
    use WithSerializer;

    /**
     * @param list<string|int> $validValues
     *
     * @throws RuntimeException
     */
    #[DataProvider('forInvalidValueInPathProvider')]
    public function testForInvalidValueInPath(
        string $value,
        array $validValues,
        string $expectedSchema,
    ): void {
        $fieldErrorsSchema = FieldsErrorsSchema::forInvalidValueInPath($value, $validValues);

        self::assertJsonStringEqualsJsonString(
            $expectedSchema,
            $this->getSerializer()->serialize($fieldErrorsSchema, 'json'),
        );
    }

    /** @return Generator<string, array{string, list<string|int>, string}> */
    public static function forInvalidValueInPathProvider(): Generator
    {
        yield 'Job type' => [
            'not-prefetch',
            EnumValues::get(JobType::class),
            <<<'JSON'
{
  "errors": [
    "Invalid value \"not-prefetch\" in path. Valid values are: \"purge\", \"prefetch\", \"purge-all\""
  ],
  "fields": {}
}
JSON,
        ];

        yield 'Summable type' => [
            'bandwidth',
            StatsType::getSummable(),
            <<<'JSON'
{
  "errors": [
    "Invalid value \"bandwidth\" in path. Valid values are: \"headers\", \"traffic\", \"hit-miss\", \"costs\""
   ],
  "fields": {}
}
JSON,
        ];
    }

    /** @throws RuntimeException */
    #[DataProvider('fromDomainFieldExceptionProvider')]
    public function testFromDomainFieldException(
        SupportsFieldErrorResponse&ClapDomainException $exception,
        string $expectedSchema,
    ): void {
        $fieldErrorsSchema = FieldsErrorsSchema::fromDomainFieldException($exception, $exception->fieldName());

        self::assertJsonStringEqualsJsonString(
            $expectedSchema,
            $this->getSerializer()->serialize($fieldErrorsSchema, 'json'),
        );
    }

    /** @return Generator<string, array{SupportsFieldErrorResponse, string}> */
    public static function fromDomainFieldExceptionProvider(): Generator
    {
        yield 'Instant Ssl toggle failed' => [
            InstantSslToggleFailed::deprecated(),
            <<<'JSON'
            {
              "errors": [],
              "fields": { 
                "instant_ssl.enabled": [
                   "This feature is deprecated. Please refer to API documentation for more information."
                   ]
               }
            }
            JSON,
        ];
    }
}
