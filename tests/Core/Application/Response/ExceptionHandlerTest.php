<?php

declare(strict_types=1);

namespace Cdn77\Api\Tests\Core\Application\Response;

use Cdn77\Api\Cdn\Domain\Exception\CdnNotFound;
use Cdn77\Api\Core\Application\Response\DomainExceptionResponseResolver;
use Cdn77\Api\Core\Application\Response\ExceptionHandler;
use Cdn77\Api\Invoice\Domain\Exception\InvalidVatNumber;
use Cdn77\Api\Invoice\Domain\Exception\XeroContactCouldNotBeCreated;
use Cdn77\Api\Job\Domain\Exception\PurgeAllDisabled;
use Cdn77\Api\Tests\TestCase;
use Cdn77\TestUtils\Stub;
use Doctrine\DBAL\Exception\UniqueConstraintViolationException;
use Exception;
use JMS\Serializer\SerializerInterface;
use Mockery;
use Paygate\Types\Exception\PaygateException;
use PHPUnit\Framework\Attributes\CoversClass;
use PHPUnit\Framework\Attributes\DataProvider;
use PHPUnit\Framework\Attributes\Group;
use Psr\Log\NullLogger;
use Symfony\Component\HttpFoundation\Response;
use Throwable;
use Webmozart\Assert\InvalidArgumentException;

#[CoversClass(ExceptionHandler::class)]
#[Group('unit')]
final class ExceptionHandlerTest extends TestCase
{
    #[DataProvider('providerHandleException')]
    public function testHandleException(Throwable $exception, int $responseCode): void
    {
        $logger = new NullLogger();
        $serializer = Mockery::mock(SerializerInterface::class);
        $domainExceptionResponseResolver = new DomainExceptionResponseResolver($logger, $serializer);

        $serializer
            ->shouldReceive('serialize')
            ->once();

        $exceptionHandler = new ExceptionHandler($domainExceptionResponseResolver, $logger, $serializer);

        $response = $exceptionHandler->handle($exception);

        self::assertSame($responseCode, $response->getStatusCode());
    }

    /** @return iterable<array{Throwable, int}> */
    public static function providerHandleException(): iterable
    {
        yield 'handle purge all disabled exception response' => [
            new PurgeAllDisabled(),
            Response::HTTP_FORBIDDEN,
        ];

        yield 'handle webmozart invalid argument exception response' => [
            new InvalidArgumentException(),
            Response::HTTP_UNPROCESSABLE_ENTITY,
        ];

        yield 'handle invalid vat number exception response' => [
            new InvalidVatNumber(),
            Response::HTTP_UNPROCESSABLE_ENTITY,
        ];

        yield 'handle xero contact could not be created exception response' => [
            new XeroContactCouldNotBeCreated(),
            Response::HTTP_UNPROCESSABLE_ENTITY,
        ];

        yield 'handle unique constraint violation exception response' => [
            Stub::create(UniqueConstraintViolationException::class),
            Response::HTTP_UNPROCESSABLE_ENTITY,
        ];

        yield 'handle domain exception response' => [
            new CdnNotFound(),
            Response::HTTP_NOT_FOUND,
        ];

        yield 'handle paygate exception response' => [
            new PaygateException(''),
            Response::HTTP_UNPROCESSABLE_ENTITY,
        ];

        yield 'handle unknown exception response' => [
            new Exception(),
            Response::HTTP_INTERNAL_SERVER_ERROR,
        ];
    }
}
