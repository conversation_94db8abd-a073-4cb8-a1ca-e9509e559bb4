<?php

declare(strict_types=1);

namespace Cdn77\Api\Tests\Core\Application\Symfony\Authenticator;

use Cdn77\Api\Core\Application\Symfony\Authenticator\JwtAuthenticator;
use Cdn77\Api\Core\Domain\Entity\Authentication\SessionId;
use Cdn77\Api\Core\Domain\Entity\Customer\Customer;
use Cdn77\Api\Core\Domain\Entity\Customer\CustomerId;
use Cdn77\Api\Core\Domain\Entity\Customer\CustomerUuid;
use Cdn77\Api\Core\Domain\Value\Customer\PlainPassword;
use Cdn77\Api\Customer\Domain\PasswordHasher;
use Cdn77\Api\Tests\ORMFixtures\Api\SessionFactory;
use Cdn77\Api\Tests\ORMFixtures\Customer\CustomerFactory;
use Cdn77\Api\Tests\TestCase;
use Cdn77\Api\Tests\Utils\FlushAndClear;
use Cdn77\Api\Tests\Utils\TokenGenerator;
use Cdn77\Api\Tests\Utils\WithBrowser;
use Cdn77\TestUtils\Stub;
use DateTimeImmutable;
use PHPUnit\Framework\Attributes\CoversClass;
use PHPUnit\Framework\Attributes\Group;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Webmozart\Assert\Assert;

#[CoversClass(JwtAuthenticator::class)]
#[Group('integration')]
final class JwtAuthenticatorTest extends TestCase
{
    use WithBrowser;

    public function testMissingAuthToken(): void
    {
        $response = $this->performHttpRequest(Request::METHOD_GET, '/v3/cdn');

        self::assertHttpCode(Response::HTTP_UNAUTHORIZED, $response);
        self::assertJsonResponse(
            ['reason' => 'Authentication required.'],
            $response,
        );
    }

    public function testInvalidAuthToken(): void
    {
        $token = TokenGenerator::generateJwt(
            new DateTimeImmutable(),
            CustomerId::fromInteger(123),
            CustomerUuid::new(),
            PlainPassword::random(),
            SessionId::new(),
            'gdafefgh1234ABCDEFGH5678!=aBcDeE',
        );

        $this->loadFixtures();
        $response = $this->performHttpRequest(
            Request::METHOD_GET,
            '/v3/cdn',
            $this->prepareAuthHeaders($token),
        );

        self::assertHttpCode(Response::HTTP_FORBIDDEN, $response);
        self::assertJsonResponse(
            ['reason' => 'Bad credentials.'],
            $response,
        );
    }

    public function testInvalidAuthType(): void
    {
        ['testing_customer' => $customer] = $this->loadFixtures();

        Assert::isInstanceOf($customer, Customer::class);

        $token = TokenGenerator::generateJwt(
            new DateTimeImmutable(),
            $customer->getId(),
            $customer->getNewId(),
            PlainPassword::random(),
            SessionId::new(),
        );

        $response = $this->performHttpRequest(
            Request::METHOD_GET,
            '/v3/cdn',
            $this->prepareAuthHeaders($token, 'Basic'),
        );

        self::assertHttpCode(Response::HTTP_UNAUTHORIZED, $response);
        self::assertJsonResponse(
            ['reason' => 'Authentication required.'],
            $response,
        );
    }

    public function testValidAuthTokenAndType(): void
    {
        ['testing_customer' => $customer] = $this->loadFixtures();

        Assert::isInstanceOf($customer, Customer::class);

        $passwordSecret = PlainPassword::random();
        $hash = (new PasswordHasher(4))->hash($passwordSecret);

        $entityManager = $this->entityManager();
        $session = SessionFactory::new($entityManager)->create(
            $customer->getNewId(),
            $hash,
        );
        $entityManager->persist($session);

        FlushAndClear::do($entityManager);

        $token = TokenGenerator::generateJwt(
            $session->createdAt(),
            $customer->getId(),
            $customer->getNewId(),
            $passwordSecret,
            $session->id(),
        );

        $response = $this->performHttpRequest(
            Request::METHOD_GET,
            '/v3/cdn',
            $this->prepareAuthHeaders($token),
        );

        self::assertHttpCode(Response::HTTP_OK, $response);
    }

    public function testSuspendedCustomerWithExpiredAuthToken(): void
    {
        $this->loadFixturesFromFile(__DIR__ . '/fixtures/common.yaml');

        $entityManager = $this->entityManager();

        $parent = CustomerFactory::new($entityManager)->create(CustomerId::fromInteger(123422));
        $entityManager->persist($parent);

        $customer = CustomerFactory::new($entityManager)->create(CustomerId::fromInteger(123423));
        $suspendedCustomer = Stub::extend(
            $customer,
            [
                Customer::FieldSuspendedAt => new DateTimeImmutable('2024-10-28 12:25'),
                Customer::FieldParentAccountId => $parent->getId()->toInt(),
            ],
        );
        $entityManager->persist($suspendedCustomer);

        $passwordSecret = PlainPassword::random();
        $hash = (new PasswordHasher(4))->hash($passwordSecret);

        $entityManager = $this->entityManager();
        $session = SessionFactory::new($entityManager)->create(
            $suspendedCustomer->getNewId(),
            $hash,
            createdAt: new DateTimeImmutable('2024-10-27 12:25'),
            expiresAt: new DateTimeImmutable('2024-10-28 12:25'),
        );
        $entityManager->persist($session);

        FlushAndClear::do($entityManager);

        $token = TokenGenerator::generateJwt(
            $session->createdAt(),
            $customer->getId(),
            $customer->getNewId(),
            $passwordSecret,
            $session->id(),
        );

        $response = $this->performHttpRequest(
            Request::METHOD_GET,
            '/v3/cdn',
            $this->prepareAuthHeaders($token),
        );

        self::assertHttpCode(Response::HTTP_FORBIDDEN, $response);
    }
}
