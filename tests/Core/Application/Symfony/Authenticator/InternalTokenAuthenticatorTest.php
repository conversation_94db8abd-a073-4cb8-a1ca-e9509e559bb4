<?php

declare(strict_types=1);

namespace Cdn77\Api\Tests\Core\Application\Symfony\Authenticator;

use Cdn77\Api\Core\Application\Symfony\Authenticator\InternalTokenAuthenticator;
use Cdn77\Api\Core\Domain\Entity\Api\PersonalTokenId;
use Cdn77\Api\Core\Domain\Entity\Customer\Customer;
use Cdn77\Api\Tests\TestCase;
use Cdn77\Api\Tests\Utils\TokenGenerator;
use Cdn77\Api\Tests\Utils\WithBrowser;
use PHPUnit\Framework\Attributes\CoversClass;
use PHPUnit\Framework\Attributes\Group;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Webmozart\Assert\Assert;

#[CoversClass(InternalTokenAuthenticator::class)]
#[Group('integration')]
final class InternalTokenAuthenticatorTest extends TestCase
{
    use WithBrowser;

    public function testSuccessfulInternalPing(): void
    {
        ['testing_customer_superadmin' => $admin] = $this
            ->loadFixturesFromFile(__DIR__ . '/fixtures/common.yaml');

        Assert::isInstanceOf($admin, Customer::class);

        $token = TokenGenerator::generateApplication($admin->getNewId());
        $response = $this->sendInternalPingRequest($token, 'Bearer');

        self::assertHttpCode(Response::HTTP_NO_CONTENT, $response);
    }

    public function testUnauthenticatedInternalPing(): void
    {
        ['testing_customer' => $customer] = $this
            ->loadFixturesFromFile(__DIR__ . '/fixtures/common.yaml');

        Assert::isInstanceOf($customer, Customer::class);

        $token = TokenGenerator::generateApplication($customer->getNewId());
        $response = $this->sendInternalPingRequest($token, 'Bearer');

        self::assertHttpCode(Response::HTTP_FORBIDDEN, $response);
        self::assertJsonResponse(['reason' => 'Bad credentials.'], $response);
    }

    public function testWrongAuthTypeInternal(): void
    {
        $token = TokenGenerator::generate(
            PersonalTokenId::fromString('4d9fd3cc-bb1b-4c52-a3cf-076f410dce03'),
            'nonexistingtoken',
        );
        $response = $this->sendInternalPingRequest($token, 'Basic');

        self::assertHttpCode(Response::HTTP_UNAUTHORIZED, $response);
        self::assertJsonResponse(['reason' => 'Authentication required.'], $response);
    }

    private function sendInternalPingRequest(string $token, string $type): Response
    {
        return $this->performHttpRequest(
            Request::METHOD_GET,
            '/v3/internal/ping',
            $this->prepareAuthHeaders($token, $type),
        );
    }
}
