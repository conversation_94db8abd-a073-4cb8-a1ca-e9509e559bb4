<?php

declare(strict_types=1);

namespace Cdn77\Api\Tests\Core\Infrastructure\Currency;

use Brick\Math\BigDecimal;
use Brick\Math\Exception\MathException;
use Brick\Money\Exception\MoneyException;
use Brick\Money\Money;
use Cdn77\Api\Core\Infrastructure\Currency\BrickCurrencyConverter;
use Cdn77\Api\CoreLibrary\Money\ContextFactory;
use Cdn77\Api\CoreLibrary\Money\CurrencyCode;
use Cdn77\Api\Tests\ORMFixtures\Currency\ExchangeRateFactory;
use Cdn77\Api\Tests\TestCase;
use Cdn77\Api\Tests\Utils\FlushAndClear;
use Cdn77\Api\Tests\Utils\WithKernel;
use Cdn77\Api\Tests\Utils\WithPostgres;
use DateTimeImmutable;
use Generator;
use PHPUnit\Framework\Attributes\CoversClass;
use PHPUnit\Framework\Attributes\DataProvider;
use PHPUnit\Framework\Attributes\Group;

#[CoversClass(BrickCurrencyConverter::class)]
#[Group('integration')]
final class BrickCurrencyConverterTest extends TestCase
{
    use WithPostgres;
    use WithKernel;

    #[DataProvider('convertDataProvider')]
    public function testConvert(
        Money $price,
        Money $targetCurrencyPrice,
    ): void {
        $entityManager = $this->entityManager();
        $rate = ExchangeRateFactory::new($entityManager)->create();
        $entityManager->persist($rate);
        FlushAndClear::do($entityManager);

        $converter = $this->getContainerService(BrickCurrencyConverter::class);
        $convertedPrice = $converter->convert(
            $price,
            CurrencyCode::fromCurrency($targetCurrencyPrice->getCurrency()),
        );
        self::assertMoneyEquals($targetCurrencyPrice, $convertedPrice);

        $convertedPrice = $converter->convert(
            $targetCurrencyPrice,
            CurrencyCode::fromCurrency($price->getCurrency()),
        );

        self::assertMoneyEquals($price, $convertedPrice);
    }

    /**
     * @return Generator<string, array{Money, Money}>
     *
     * @throws MathException
     * @throws MoneyException
     */
    public static function convertDataProvider(): Generator
    {
        foreach (CurrencyCode::cases() as $currencyCode) {
            yield $currencyCode->value . ' converts to the same currency' => [
                Money::of(1, $currencyCode->toCurrency()),
                Money::of(1, $currencyCode->toCurrency()),
            ];
        }

        yield 'zero converts to zero' => [
            Money::zero(CurrencyCode::USD->toCurrency()),
            Money::zero(CurrencyCode::EUR->toCurrency()),
        ];

        yield '1 USD converts to 0.9 EUR' => [
            Money::of('1', CurrencyCode::USD->toCurrency()),
            Money::of('0.9', CurrencyCode::EUR->toCurrency()),
        ];

        yield '1 USD converts to 0.75 GBP' => [
            Money::of('1', CurrencyCode::USD->toCurrency()),
            Money::of('0.75', CurrencyCode::GBP->toCurrency()),
        ];

        yield '1 USD converts to 5.5 BRL' => [
            Money::of('1', CurrencyCode::USD->toCurrency()),
            Money::of('5.5', CurrencyCode::BRL->toCurrency()),
        ];

        yield '1 USD converts to 22.42 CZK' => [
            Money::of('1', CurrencyCode::USD->toCurrency()),
            Money::of('22.42', CurrencyCode::CZK->toCurrency()),
        ];

        yield '0.9 EUR converts to 0.75 GBP' => [
            Money::of('0.9', CurrencyCode::EUR->toCurrency()),
            Money::of('0.75', CurrencyCode::GBP->toCurrency()),
        ];

        yield '22.42 CZK converts to 0.9 EUR' => [
            Money::of('22.42', CurrencyCode::CZK->toCurrency()),
            Money::of('0.9', CurrencyCode::EUR->toCurrency()),
        ];

        yield '56.1 USD convert to 1257.76 CZK' => [
            Money::of('56.10', CurrencyCode::USD->toCurrency()),
            Money::of('1257.76', CurrencyCode::CZK->toCurrency()),
        ];

        yield '56.123456 USD convert to 1258.28788352 CZK' => [
            Money::of('56.123456', CurrencyCode::USD->toCurrency(), ContextFactory::preciseChargeContext()),
            Money::of('1258.28788352', CurrencyCode::CZK->toCurrency(), ContextFactory::preciseChargeContext()),
        ];
    }

    #[DataProvider('convertAtDateDataProvider')]
    public function testConvertAtDate(
        Money $price,
        Money $targetCurrencyPrice,
    ): void {
        $entityManager = $this->entityManager();
        $rate = ExchangeRateFactory::new($entityManager)->create();
        $entityManager->persist($rate);
        $lastYearRate = ExchangeRateFactory::new($entityManager)->create(
            BigDecimal::of('0.8'),
            BigDecimal::of('0.9'),
            BigDecimal::of('6.5'),
            BigDecimal::of('25'),
            new DateTimeImmutable('2023-08-27 22:05:01'),
        );
        $entityManager->persist($lastYearRate);
        FlushAndClear::do($entityManager);

        $conversionDate = new DateTimeImmutable('2023-08-27 10:05:01');
        $converter = $this->getContainerService(BrickCurrencyConverter::class);
        $convertedPrice = $converter->convertAtDate(
            $price,
            CurrencyCode::fromCurrency($targetCurrencyPrice->getCurrency()),
            $conversionDate,
        );
        self::assertMoneyEquals($targetCurrencyPrice, $convertedPrice);

        $convertedPrice = $converter->convertAtDate(
            $targetCurrencyPrice,
            CurrencyCode::fromCurrency($price->getCurrency()),
            $conversionDate,
        );

        self::assertMoneyEquals($price, $convertedPrice);
    }

    /**
     * @return Generator<string, array{Money, Money}>
     *
     * @throws MathException
     * @throws MoneyException
     */
    public static function convertAtDateDataProvider(): Generator
    {
        foreach (CurrencyCode::cases() as $currencyCode) {
            yield $currencyCode->value . ' converts to the same currency' => [
                Money::of(1, $currencyCode->toCurrency()),
                Money::of(1, $currencyCode->toCurrency()),
            ];
        }

        yield 'zero converts to zero' => [
            Money::zero(CurrencyCode::USD->toCurrency()),
            Money::zero(CurrencyCode::EUR->toCurrency()),
        ];

        yield '1 USD converts to 0.8 EUR' => [
            Money::of('1', CurrencyCode::USD->toCurrency()),
            Money::of('0.8', CurrencyCode::EUR->toCurrency()),
        ];

        yield '1 USD converts to 0.9 GBP' => [
            Money::of('1', CurrencyCode::USD->toCurrency()),
            Money::of('0.9', CurrencyCode::GBP->toCurrency()),
        ];

        yield '1 USD converts to 6.5 BRL' => [
            Money::of('1', CurrencyCode::USD->toCurrency()),
            Money::of('6.5', CurrencyCode::BRL->toCurrency()),
        ];

        yield '1 USD converts to 25 CZK' => [
            Money::of('1', CurrencyCode::USD->toCurrency()),
            Money::of('25', CurrencyCode::CZK->toCurrency()),
        ];

        yield '0.8 EUR converts to 0.9 GBP' => [
            Money::of('0.8', CurrencyCode::EUR->toCurrency()),
            Money::of('0.9', CurrencyCode::GBP->toCurrency()),
        ];

        yield '25 CZK converts to 0.8 EUR' => [
            Money::of('25', CurrencyCode::CZK->toCurrency()),
            Money::of('0.8', CurrencyCode::EUR->toCurrency()),
        ];
    }
}
