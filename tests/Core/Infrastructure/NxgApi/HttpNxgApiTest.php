<?php

declare(strict_types=1);

namespace Cdn77\Api\Tests\Core\Infrastructure\NxgApi;

use Cdn77\Api\Core\Domain\Encryption\ValueEncryptor;
use Cdn77\Api\Core\Infrastructure\NxgApi\HttpNxgApi;
use Cdn77\Api\Core\Infrastructure\NxgApi\ResourceFactory;
use Cdn77\Api\Ssl\Domain\Contract\SslId;
use Cdn77\NxgApiClient\ApiClient;
use Cdn77\NxgApiClient\Response;
use Defuse\Crypto\Exception\BadFormatException;
use Defuse\Crypto\Exception\EnvironmentIsBrokenException;
use Defuse\Crypto\Key;
use PHPUnit\Framework\Attributes\CoversClass;
use PHPUnit\Framework\Attributes\Group;
use PHPUnit\Framework\TestCase;
use Psr\Log\LoggerInterface;
use Symfony\Component\HttpFoundation\Response as HttpResponse;

#[Group('unit')]
#[CoversClass(HttpNxgApi::class)]
final class HttpNxgApiTest extends TestCase
{
    public function testLoadPrivateKeyUuidsReturnsCorrectMap(): void
    {
        $expectedPayload = [
            'a6b8afc4-b407-40a1-ba47-a5f8caa3df3d' => 123,
            '678e0272-c023-45c1-b7e0-4f28fc982c4d' => 456,
        ];

        $nxgApi = $this->prepareHttpNxgApi($expectedPayload);
        $result = $nxgApi->loadPrivateKeyUuids();

        self::assertCount(2, $result);

        foreach ($expectedPayload as $uuid => $value) {
            self::assertTrue($result->hasKey(SslId::fromString($uuid)));
            self::assertEquals($value, $result->get(SslId::fromString($uuid))->toInt());
        }
    }

    /**
     * @param array<string, int> $expectedPayload
     *
     * @throws BadFormatException
     * @throws EnvironmentIsBrokenException
     */
    private function prepareHttpNxgApi(array $expectedPayload): HttpNxgApi
    {
        $client = $this->createMock(ApiClient::class);
        $logger = $this->createMock(LoggerInterface::class);

        $valueEncryptor = new ValueEncryptor(Key::createNewRandomKey()->saveToAsciiSafeString());
        $resourceFactory = new ResourceFactory($valueEncryptor);

        $response = new Response(HttpResponse::HTTP_OK, $expectedPayload);
        $client->method('send')->willReturn($response);

        return new HttpNxgApi($client, $logger, $resourceFactory);
    }
}
