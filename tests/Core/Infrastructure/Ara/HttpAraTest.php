<?php

declare(strict_types=1);

namespace Cdn77\Api\Tests\Core\Infrastructure\Ara;

use Brick\Math\RoundingMode;
use Cdn77\Api\Core\Domain\Entity\Cdn\CdnId;
use Cdn77\Api\Core\Infrastructure\Ara\HttpAra;
use Cdn77\Api\Tests\TestCase;
use Cdn77\AraClient\AraClient;
use Cdn77\AraClient\Stats\Sum\VO\Sum;
use DateTimeImmutable;
use Mockery;
use PHPUnit\Framework\Attributes\CoversClass;
use PHPUnit\Framework\Attributes\DataProvider;
use PHPUnit\Framework\Attributes\Group;

use function count;

#[Group('unit')]
#[CoversClass(HttpAra::class)]
final class HttpAraTest extends TestCase
{
    /** @param list<CdnId> $cdnIds */
    #[DataProvider('providerCdnIdsAndSum')]
    public function testGetSum(array $cdnIds, Sum $priceSum): void
    {
        $client = Mockery::mock(AraClient::class);
        $ara = new HttpAra($client);
        $from = new DateTimeImmutable('2019-10-01 00:00:00');
        $to = new DateTimeImmutable('2019-10-31 00:00:00');

        if (count($cdnIds) > 0) {
            $client->shouldReceive('request')
                ->once()
                ->andReturn($priceSum);
        }

        $result = $ara->requestPriceSum($cdnIds, $from, $to);

        self::assertTrue($result->isEqualTo(
            $priceSum->getResult()->toScale(2, RoundingMode::HALF_UP),
        ));
    }

    /** @return iterable<array{list<CdnId>, Sum}> */
    public static function providerCdnIdsAndSum(): iterable
    {
        yield 'Test request price sum with Cdns' => [[CdnId::fromInteger(1)], new Sum(558000.123456789012345)];
        yield 'Test request price sum with no cdns' => [[], new Sum(0)];
    }
}
