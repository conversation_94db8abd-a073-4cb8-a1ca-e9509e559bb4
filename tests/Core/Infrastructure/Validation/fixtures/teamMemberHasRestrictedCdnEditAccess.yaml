include:
    - common.yaml

Cdn77\Api\Core\Domain\Value\AccessRestriction\ResourceRestriction:
    test_restriction:
        __construct: false
        type: <identity(Cdn77\Api\Core\Domain\Value\AccessRestriction\ResourceRestrictionType::Cdn)>
        ids: [<(@testing_access_cdn->getId())>]

Cdn77\Api\Core\Domain\Entity\Api\TeamMemberAccessConfiguration:
    team_member_access:
        __construct: false
        id: <identity(Symfony\Component\Uid\Uuid::fromString('ea9586f1-f0cb-4a54-9606-f0705cf7f9a2'))>
        accessType: <(Cdn77\Api\Core\Domain\Value\AccessRestriction\AccessType::CdnEdit)>
        customerId: <(@testing_team_member->getNewId()->toUid())>
        restrictions: '{}'
        __calls:
            - updateRestrictions: [<(@test_restriction)>]
