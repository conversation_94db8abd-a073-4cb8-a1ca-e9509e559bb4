<?php

declare(strict_types=1);

namespace Cdn77\Api\Tests\Core\Domain\Entity\DataManipulation;

use Cdn77\Api\Core\Domain\Entity\Cdn\CdnId;
use Cdn77\Api\Core\Domain\Entity\DataManipulation\PurgeAll;
use Cdn77\Api\Job\Domain\Value\JobState;
use Cdn77\Api\Job\Domain\Value\JobType;
use Cdn77\Api\Job\Domain\Value\Paths;
use Cdn77\Api\Tests\TestCase;
use DateTimeImmutable;
use PHPUnit\Framework\Attributes\CoversClass;
use PHPUnit\Framework\Attributes\Group;

#[CoversClass(PurgeAll::class)]
#[Group('unit')]
final class PurgeAllTest extends TestCase
{
    public function testConstruct(): void
    {
        $cdnId = CdnId::fromInteger(123556789);
        $paths = Paths::fromAllPaths();
        $now = new DateTimeImmutable('2021-08-18 12:12:12');

        $purgeAll = PurgeAll::new($cdnId, $paths, $now);

        self::assertSame(JobType::PurgeAll, $purgeAll->getType());
        self::assertSame(JobState::Done, $purgeAll->getState());
        self::assertTrue($purgeAll->getCdnId()->equals($cdnId));
        self::assertSame($now, $purgeAll->getScheduledAt());
        self::assertSame($now, $purgeAll->getCompletedAt());
    }
}
