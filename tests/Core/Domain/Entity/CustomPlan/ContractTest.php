<?php

declare(strict_types=1);

namespace Cdn77\Api\Tests\Core\Domain\Entity\CustomPlan;

use Brick\Math\BigDecimal;
use Brick\Math\Exception\MathException;
use Brick\Money\Exception\MoneyException;
use Brick\Money\Money;
use Cdn77\Api\Core\Domain\Entity\Customer\Customer;
use Cdn77\Api\Core\Domain\Entity\Customer\CustomerUuid;
use Cdn77\Api\Core\Domain\Entity\CustomPlan\Contract;
use Cdn77\Api\Core\Domain\Entity\CustomPlan\CustomPlan;
use Cdn77\Api\Core\Domain\Entity\CustomPlan\CustomPlanId;
use Cdn77\Api\CoreLibrary\Money\CurrencyCode;
use Cdn77\Api\CustomPlan\Domain\Dto\DescriptionLines;
use Cdn77\Api\CustomPlan\Domain\Dto\InvoiceLines;
use Cdn77\Api\CustomPlan\Domain\Dto\MailGreeting;
use Cdn77\Api\CustomPlan\Domain\Dto\PaymentInterval;
use Cdn77\Api\CustomPlan\Domain\Dto\PlanVolume;
use Cdn77\Api\CustomPlan\Domain\Value\BillingUnit;
use Cdn77\Api\CustomPlan\Domain\Value\ContractState;
use Cdn77\Api\CustomPlan\Domain\Value\OperationCode;
use Cdn77\Api\CustomPlan\Domain\Value\PaymentPlan;
use Cdn77\Api\Tests\TestCase;
use Cdn77\TestUtils\Stub;
use DateInterval;
use DateTimeImmutable;
use PHPUnit\Framework\Attributes\CoversClass;
use PHPUnit\Framework\Attributes\Group;

#[CoversClass(Contract::class)]
#[Group('unit')]
final class ContractTest extends TestCase
{
    /**
     * @throws MathException
     * @throws MoneyException
     */
    public function testConstruct(): void
    {
        $activeFrom = new DateTimeImmutable('2021-09-01 00:00:00');
        $activeTo = new DateTimeImmutable('2021-09-30 23:59:59');
        $billingUnit = BillingUnit::TebiBytes;
        $createdAt = new DateTimeImmutable('2021-08-17');
        $customPlanId = CustomPlanId::fromString('4e7a4abe-7d7d-45e3-ab73-88a262d58c3f');
        $customerId = CustomerUuid::fromString('7c2ea3ff-9f50-48c4-9272-efc21f6ae03f');
        $customer = Stub::create(Customer::class, ['newId' => $customerId->toUid()]);
        $customPlan = Stub::create(CustomPlan::class, [
            CustomPlan::FieldId => $customPlanId->toUid(),
            CustomPlan::FieldCustomer => $customer,
            CustomPlan::FieldBillingUnit => $billingUnit,
        ]);
        $description = DescriptionLines::fromArray([
            'Storage' => '10 TB per month',
            'Custom plan' => '10 TB',
        ]);
        $operationCode = OperationCode::Nothing;
        $paymentInterval = new PaymentInterval(new DateInterval('P1M'));
        $paymentPlan = PaymentPlan::Prepaid;
        $priceUsd = Money::of(2190, CurrencyCode::USD->toCurrency());
        $state = ContractState::Paid;
        $planVolume = new PlanVolume($billingUnit, BigDecimal::of(1111));
        $internalNote = 'Custom plan internal note';
        $invoiceLines = InvoiceLines::empty();
        $message = 'Custom plan note';
        $greeting = MailGreeting::hello();

        $contract = new Contract(
            $activeFrom,
            $activeTo,
            $createdAt,
            $customPlan,
            $description,
            $operationCode,
            $paymentInterval,
            $paymentPlan,
            $planVolume,
            $priceUsd,
            $state,
            $internalNote,
            $invoiceLines,
            $message,
            $greeting,
        );

        self::assertSame($state, $contract->getState());
        self::assertSame($paymentPlan, $contract->getPaymentPlan());
        self::assertSame($planVolume->billingUnit, $contract->getPlanVolume()->billingUnit);
        self::assertTrue($contract->customPlan()->getId()->equals($customPlanId));
        self::assertSame($activeTo, $contract->getActiveTo());
        self::assertSame($activeFrom, $contract->getActiveFrom());
        self::assertMoneyEquals($priceUsd, $contract->getPrice());
        self::assertTrue($planVolume->volume->isEqualTo($contract->getPlanVolume()->volume));
        self::assertSame($description->toJsonString(), $contract->getDescription()->toJsonString());
        self::assertSame($description->toArray(), $contract->getDescription()->toArray());
        self::assertSame($paymentInterval->get(), $contract->getPaymentInterval()->get());
        self::assertSame($internalNote, $contract->getInternalNote());
        self::assertSame($message, $contract->getMessage());
        self::assertSame(InvoiceLines::empty()->invoiceLines, $contract->getInvoiceLines()->invoiceLines);
        self::assertSame($operationCode, $contract->operationCode());
        self::assertSame($greeting->value, $contract->mailGreeting()->value);
    }
}
