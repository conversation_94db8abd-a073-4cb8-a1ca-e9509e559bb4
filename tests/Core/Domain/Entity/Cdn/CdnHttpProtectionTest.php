<?php

declare(strict_types=1);

namespace Cdn77\Api\Tests\Core\Domain\Entity\Cdn;

use Cdn77\Api\Cdn\Domain\Value\AccessProtectionType;
use Cdn77\Api\Core\Domain\Entity\Cdn\CdnHttpProtection;
use Cdn77\Api\Core\Domain\Entity\Cdn\CdnLegacyId;
use Cdn77\Api\Tests\TestCase;
use PHPUnit\Framework\Attributes\CoversClass;
use PHPUnit\Framework\Attributes\Group;

#[CoversClass(CdnHttpProtection::class)]
#[Group('unit')]
final class CdnHttpProtectionTest extends TestCase
{
    public function testConstruct(): void
    {
        $cdnLegacyId = CdnLegacyId::fromInteger(123456);
        $hlpDenyEmptyReferer = true;
        $hlpType = AccessProtectionType::PassList;
        $gpType = AccessProtectionType::PassList;
        $ippType = AccessProtectionType::BlockList;

        $cdnHttpProtection = new CdnHttpProtection(
            $cdnLegacyId,
            $hlpDenyEmptyReferer,
            $hlpType,
            $ippType,
            $gpType,
        );

        self::assertTrue($cdnLegacyId->equals($cdnHttpProtection->getCdnLegacyId()));
        self::assertSame($cdnHttpProtection->getHlpType(), $hlpType);
        self::assertSame($cdnHttpProtection->getGpType(), $gpType);
        self::assertSame($cdnHttpProtection->getIppType(), $ippType);
        self::assertTrue($cdnHttpProtection->isEmptyRefererDenied());
    }
}
