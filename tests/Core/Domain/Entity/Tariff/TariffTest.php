<?php

declare(strict_types=1);

namespace Cdn77\Api\Tests\Core\Domain\Entity\Tariff;

use Brick\Math\Exception\MathException;
use Brick\Money\Exception\MoneyException;
use Brick\Money\Money;
use Cdn77\Api\Core\Domain\Entity\Customer\Customer;
use Cdn77\Api\Core\Domain\Entity\Customer\CustomerId;
use Cdn77\Api\Core\Domain\Entity\Tariff\Credit;
use Cdn77\Api\Core\Domain\Entity\Tariff\Tariff;
use Cdn77\Api\CoreLibrary\Money\CurrencyCode;
use Cdn77\Api\Tests\TestCase;
use Cdn77\TestUtils\Stub;
use DateTimeImmutable;
use PHPUnit\Framework\Attributes\CoversClass;
use PHPUnit\Framework\Attributes\Group;
use Webmozart\Assert\Assert;

use function sprintf;

#[CoversClass(Tariff::class)]
#[Group('unit')]
final class TariffTest extends TestCase
{
    /**
     * @throws MathException
     * @throws MoneyException
     */
    public function testCreateFirstCreditAndTariff(): void
    {
        $customer = Stub::create(Customer::class, ['id' => 1]);
        $depositorId = CustomerId::fromInteger(2);
        $usdCurrency = CurrencyCode::USD->toCurrency();
        $credit = Credit::create(
            Money::of(1, $usdCurrency),
            Money::of(0, $usdCurrency),
        );
        $createdAt = new DateTimeImmutable('2020-01-30 12:12:12');

        $tariff = Tariff::create($customer, $depositorId, $credit, $createdAt);

        self::assertSame($customer, $tariff->getCustomer());
        Assert::notNull($tariff->getDepositorId());
        self::assertTrue($depositorId->equals($tariff->getDepositorId()));
        self::assertSame($credit, $tariff->getCredit());
        self::assertMoneyEquals($credit->getMoney(), $tariff->getCredit()->getMoney());
        self::assertSame($createdAt, $tariff->getCreationTime());
        self::assertNull($tariff->getValidSince());
        self::assertNull($tariff->getLastRefreshTime());
        self::assertNull($tariff->getDeactivationTime());
        self::assertEquals(
            $createdAt->modify(sprintf('+%s days', Tariff::CreditValidForDays)),
            $tariff->getExpiresAt(),
        );
    }

    /**
     * @throws MathException
     * @throws MoneyException
     */
    public function testCreateFirstWithCredit(): void
    {
        $usdCurrency = CurrencyCode::USD->toCurrency();
        $money = Money::of(199, $usdCurrency);
        $bonusMoney = Money::zero($usdCurrency);
        $customer = Stub::create(Customer::class, ['id' => 123]);
        $createdAt = new DateTimeImmutable();

        $tariff = Tariff::firstWithCredit($money, $bonusMoney, $customer, $createdAt);

        self::assertMoneyEquals($tariff->getCredit()->getCurrentCredit(), $money->plus($bonusMoney));
        self::assertSame($customer, $tariff->getCustomer());
        self::assertSame($createdAt, $tariff->getCreationTime());
    }

    /**
     * @throws MathException
     * @throws MoneyException
     */
    public function testCreateForCustomerWithCredit(): void
    {
        $usdCurrency = CurrencyCode::USD->toCurrency();
        $money = Money::of(299, $usdCurrency);
        $bonusMoney = Money::of(15, $usdCurrency);
        $customer = Stub::create(Customer::class, ['id' => 1234]);
        $depositorId = CustomerId::fromInteger(2);
        $chargedAt = new DateTimeImmutable('today 8:45am');
        $credit = Stub::create(Credit::class, [
            'money' => 1.94,
            'usedMoney' => 0,
            'overLimitMoney' => 0,
        ]);

        $tariff = Tariff::forCustomerWithCredit($money, $bonusMoney, $customer, $credit, $chargedAt, $depositorId);

        //date should be shifted on tariff creation, to support (legacy) spendings calculation on day rounding
        $expectedCreationTime = $chargedAt->modify('midnight');
        $expectedCredit = $money
            ->plus($credit->getMoney())
            ->minus($credit->getOverLimitMoney())
            ->plus($bonusMoney);

        self::assertMoneyEquals($tariff->getCredit()->getMoney(), $money->plus($bonusMoney));
        self::assertMoneyEquals($tariff->getCredit()->getOverLimitMoney(), $credit->getMoney()->negated());
        self::assertMoneyEquals($tariff->getCredit()->getCurrentCredit(), $expectedCredit);
        self::assertEquals($expectedCreationTime, $tariff->getCreationTime());
    }

    /**
     * @throws MathException
     * @throws MoneyException
     */
    public function testWithdrawCredit(): void
    {
        $usdCurrency = CurrencyCode::USD->toCurrency();
        $credit = Stub::create(Credit::class, ['money' => 199.99]);
        $amountToWithdraw = Money::of(9.99, $usdCurrency);
        $finalAmount = Money::of(190, $usdCurrency);

        $credit->withdraw($amountToWithdraw);

        self::assertMoneyEquals($finalAmount, $credit->getMoney());
    }
}
