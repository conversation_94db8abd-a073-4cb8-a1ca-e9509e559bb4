<?php

declare(strict_types=1);

namespace Cdn77\Api\Tests\Core\Domain\Encryption;

use Cdn77\Api\Core\Domain\Encryption\CredentialsEncryptor;
use Cdn77\Api\Core\Domain\Encryption\Sodium\ValueEncryptor;
use Cdn77\Api\Core\Domain\Value\ObjectStorage\EncryptedObjectStorageCredentials;
use Cdn77\Api\Core\Domain\Value\ObjectStorage\ObjectStorageCredentials;
use Cdn77\Api\Core\Domain\Value\ObjectStorage\ObjectStorageUserName;
use Cdn77\Api\Origin\Domain\Value\S3AccessKey;
use Cdn77\Api\Origin\Domain\Value\S3Secret;
use Cdn77\Api\Tests\TestCase;
use PHPUnit\Framework\Attributes\CoversClass;
use PHPUnit\Framework\Attributes\Group;

use function Safe\sodium_crypto_secretbox_open;
use function sodium_hex2bin;
use function sodium_memzero;
use function substr;

use const SODIUM_CRYPTO_SECRETBOX_NONCEBYTES;

#[Group('integration')]
#[CoversClass(CredentialsEncryptor::class)]
#[CoversClass(EncryptedObjectStorageCredentials::class)]
final class CredentialsEncryptorTest extends TestCase
{
    public function testEncrypt(): void
    {
        $encryptionKey = 'a28c0818e4b63a65a0ca1a67f5251cc7eef81826c79332978736d962f2137a48';
        $credentials = new ObjectStorageCredentials(
            ObjectStorageUserName::fromString('user'),
            new S3AccessKey('AKAI1234567890'),
            new S3Secret('s3cr3tv4lu3un3crypt3d'),
            'test',
        );

        $credentialsEncryptor = new CredentialsEncryptor(new ValueEncryptor($encryptionKey));

        $encryptedCredentials = $credentialsEncryptor->encrypt($credentials);

        $accessKey = sodium_hex2bin($encryptedCredentials->accessKey->value);
        $decryptedAccessKey = sodium_crypto_secretbox_open(
            substr($accessKey, SODIUM_CRYPTO_SECRETBOX_NONCEBYTES, null),
            substr($accessKey, 0, SODIUM_CRYPTO_SECRETBOX_NONCEBYTES),
            sodium_hex2bin($encryptionKey),
        );

        self::assertSame($credentials->accessKey->value, $decryptedAccessKey);

        sodium_memzero($decryptedAccessKey);

        $secret = sodium_hex2bin($encryptedCredentials->secret->value);
        $decryptedSecret = sodium_crypto_secretbox_open(
            substr($secret, SODIUM_CRYPTO_SECRETBOX_NONCEBYTES, null),
            substr($secret, 0, SODIUM_CRYPTO_SECRETBOX_NONCEBYTES),
            sodium_hex2bin($encryptionKey),
        );

        self::assertSame($credentials->secret->value, $decryptedSecret);

        sodium_memzero($encryptionKey);
        sodium_memzero($decryptedSecret);
    }
}
