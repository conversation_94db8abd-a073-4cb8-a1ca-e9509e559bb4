<?php

declare(strict_types=1);

namespace Cdn77\Api\Tests\Core\Domain\Value\ObjectStorage;

use Cdn77\Api\Core\Domain\Exception\ObjectStorage\InvalidBucketName;
use Cdn77\Api\Core\Domain\Value\ObjectStorage\BucketName;
use Cdn77\Api\Tests\TestCase;
use Generator;
use PHPUnit\Framework\Attributes\CoversClass;
use PHPUnit\Framework\Attributes\DataProvider;
use PHPUnit\Framework\Attributes\Group;

#[CoversClass(BucketName::class)]
#[Group('unit')]
final class BucketNameTest extends TestCase
{
    /** @throws InvalidBucketName */
    #[DataProvider('validFromStringDataProvider')]
    public function testValidFromString(string $name): void
    {
        $bucketName = BucketName::fromString($name);
        self::assertSame($name, $bucketName->value);
    }

    /** @return Generator<list<string>> */
    public static function validFromStringDataProvider(): Generator
    {
        yield from self::providerValidSafeString();

        yield ['1337.foo.bar'];
    }

    /** @throws InvalidBucketName */
    #[DataProvider('providerValidSafeString')]
    public function testValidFromSafeString(string $name): void
    {
        $bucketName = BucketName::fromString($name);
        self::assertSame($name, $bucketName->value);
    }

    /** @return Generator<list<string>> */
    public static function providerValidSafeString(): Generator
    {
        yield ['bucket'];
        yield ['bucket-name'];
        yield ['bucket-1'];
        yield ['1-bucket'];
        yield ['1337'];
    }

    /** @throws InvalidBucketName */
    #[DataProvider('providerInvalidString')]
    public function testInvalidFromString(string $name): void
    {
        self::expectException(InvalidBucketName::class);
        BucketName::fromString($name);
    }

    /** @return Generator<list<string>> */
    public static function providerInvalidString(): Generator
    {
        yield ['INVALID-BUCKET'];
        yield ['invalid_bucket'];
        yield ['invalid bucket'];
    }

    /** @throws InvalidBucketName */
    #[DataProvider('providerInvalidSafeString')]
    public function testInvalidFromSafeString(string $name): void
    {
        self::expectException(InvalidBucketName::class);
        BucketName::fromSafeString($name);
    }

    /** @return Generator<list<string>> */
    public static function providerInvalidSafeString(): Generator
    {
        yield from self::providerInvalidString();

        yield ['invalid.bucket'];
    }
}
