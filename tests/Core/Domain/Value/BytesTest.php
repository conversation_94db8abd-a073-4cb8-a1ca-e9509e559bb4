<?php

declare(strict_types=1);

namespace Cdn77\Api\Tests\Core\Domain\Value;

use Cdn77\Api\Core\Domain\Value\Bytes;
use Cdn77\Api\Tests\TestCase;
use Generator;
use PHPUnit\Framework\Attributes\CoversClass;
use PHPUnit\Framework\Attributes\DataProvider;
use PHPUnit\Framework\Attributes\Group;

use const PHP_INT_MAX;

#[CoversClass(Bytes::class)]
#[Group('unit')]
final class BytesTest extends TestCase
{
    #[DataProvider('bytesProvider')]
    public function testByte(int $bytes, int $expectedBytes): void
    {
        self::assertSame($expectedBytes, Bytes::create($bytes)->value);
    }

    /** @return Generator<int, array{int, int}> */
    public static function bytesProvider(): Generator
    {
        yield [0, 0];
        yield [1, 1];
        yield [100, 100];
        yield [PHP_INT_MAX, PHP_INT_MAX];
    }

    #[DataProvider('gibibytesProvider')]
    public function testGibibyte(int $gibibytes, int $expectedBytes): void
    {
        self::assertSame($expectedBytes, Bytes::fromGibibytes($gibibytes)->value);
    }

    /** @return Generator<int, array{int, int}> */
    public static function gibibytesProvider(): Generator
    {
        yield [0, 0];
        yield [1, 1024 ** 3];
        yield [100, 100 * 1024 ** 3];
    }

    #[DataProvider('tebibytesProvider')]
    public function testTebibyte(int $tebibytes, int $expectedBytes): void
    {
        self::assertSame($expectedBytes, Bytes::fromTebibytes($tebibytes)->value);
    }

    /** @return Generator<int, array{int, int}> */
    public static function tebibytesProvider(): Generator
    {
        yield [0, 0];
        yield [1, 1024 ** 4];
        yield [100, 100 * 1024 ** 4];
    }

    public function testEquals(): void
    {
        self::assertSame(Bytes::create(100), Bytes::create(100));
        self::assertSame(Bytes::fromGibibytes(100), Bytes::fromGibibytes(100));
        self::assertSame(Bytes::fromTebibytes(100), Bytes::fromTebibytes(100));
        self::assertSame(Bytes::create(1024 ** 3), Bytes::fromGibibytes(1));
        self::assertSame(Bytes::create(1024 ** 4), Bytes::fromTebibytes(1));
        self::assertSame(Bytes::fromGibibytes(1024), Bytes::fromTebibytes(1));
    }

    public function testFormatEquals(): void
    {
        self::assertSame('2.0 MiB', Bytes::fromMebibytes(2)->toFormattedString());
        self::assertSame('4.0 GiB', Bytes::fromGibibytes(4)->toFormattedString());
        self::assertSame('1.0 TiB', Bytes::fromTebibytes(1)->toFormattedString());
        self::assertSame('0.000 B', Bytes::create(0)->toFormattedString(3));
        self::assertSame('1.0 B', Bytes::create(1)->toFormattedString());
        self::assertSame('1.00 B', Bytes::create(1)->toFormattedString(2));
        self::assertSame('0 B', Bytes::create(-0)->toFormattedString(0));
        self::assertSame('0 B', Bytes::create(+0)->toFormattedString(0));
        self::assertSame('-123.0 B', Bytes::create(-123)->toFormattedString(1));
        self::assertSame('123.000 B', Bytes::create(123)->toFormattedString(3));
        self::assertSame('1 kiB', Bytes::create(1024)->toFormattedString(0));
        self::assertSame('3.182 kiB', Bytes::create(3258)->toFormattedString(3));
        self::assertSame('31.8 kiB', Bytes::create(32582)->toFormattedString(1));
        self::assertSame('32 kiB', Bytes::create(32582)->toFormattedString(0));
        self::assertSame('1 MiB', Bytes::create(1024 * 1024)->toFormattedString(0));
        self::assertSame('65.61 MiB', Bytes::create(68798972)->toFormattedString(2));
        self::assertSame('6.4068 GiB', Bytes::create(6879228972)->toFormattedString(4));
        self::assertSame('30.349 GiB', Bytes::create(32587132740)->toFormattedString(3));
        self::assertSame('296.2976 TiB', Bytes::create(325782687132740)->toFormattedString(4));
        self::assertSame('-325782687132740 B', Bytes::create(-325782687132740)->toFormattedString(0));
        self::assertSame('-325782687132740.00 B', Bytes::create(-325782687132740)->toFormattedString(2));
        self::assertSame('2.432 PiB', Bytes::create(2738281121898392)->toFormattedString(3));
        self::assertSame('8.0000 EiB', Bytes::create(9223372036854775807)->toFormattedString(4));
    }

    #[DataProvider('toTebiBytesProvider')]
    public function testToTebiBytes(
        Bytes $bytes,
        int $scale,
        string $expectedTibiBytes,
    ): void {
        self::assertSame(
            $expectedTibiBytes,
            $bytes->toTebiBytes($scale)->__toString(),
        );
    }

    /** @return Generator<int, array{Bytes, int, string}> */
    public static function toTebiBytesProvider(): Generator
    {
        yield [Bytes::fromTebibytes(1), 0, '1'];
        yield [Bytes::fromTebibytes(1)->plus(Bytes::fromGibibytes(400)), 0, '1'];
        yield [Bytes::fromTebibytes(1)->plus(Bytes::fromGibibytes(500)), 0, '1'];
        yield [Bytes::fromTebibytes(1)->plus(Bytes::fromGibibytes(600)), 0, '2'];
        yield [Bytes::fromTebibytes(1)->plus(Bytes::fromGibibytes(800)), 1, '1.8'];
        yield [Bytes::fromTebibytes(1)->plus(Bytes::fromGibibytes(880)), 2, '1.86'];
        yield [Bytes::fromTebibytes(1), 1, '1.0'];
    }
}
