<?php

declare(strict_types=1);

namespace Cdn77\Api\Tests\Core\Domain\Stats;

use Cdn77\Api\Core\Domain\Dto\TimeRange;
use Cdn77\Api\Core\Domain\Entity\Customer\Customer;
use Cdn77\Api\Core\Domain\Entity\Customer\CustomerId;
use Cdn77\Api\Core\Domain\Stats\StatsSourceSelector;
use Cdn77\Api\Stats\Domain\Value\Aggregation;
use Cdn77\Api\Stats\Domain\Value\DataSource;
use Cdn77\Api\Stats\Domain\Value\StatsType;
use Cdn77\Api\Tests\TestCase;
use Cdn77\TestUtils\Stub;
use DateTimeImmutable;
use InvalidArgumentException;
use PHPUnit\Framework\Attributes\CoversClass;
use PHPUnit\Framework\Attributes\DataProvider;
use PHPUnit\Framework\Attributes\Group;
use Symfony\Component\Uid\Uuid;

/**
 * @phpstan-type ProviderDataShape array{
 *     DataSource|null,
 *     Aggregation,
 *     StatsType,
 *     TimeRange,
 *     Customer,
 *     DateTimeImmutable,
 *     bool,
 *     DataSource
 * }
 */
#[Group('unit')]
#[CoversClass(StatsSourceSelector::class)]
final class StatsSourceSelectorTest extends TestCase
{
    public function testStatsSourceSelectorDefault(): void
    {
        $sourceSelector = new StatsSourceSelector(false);

        $now = new DateTimeImmutable('2022-05-07 midnight');
        $customer = Stub::create(
            Customer::class,
            ['id' => 1, 'newId' => Uuid::fromString('ca10321e-d235-4dc7-a160-e5a24e4feee1')],
        );

        $source = $sourceSelector->getDataSource(
            null,
            Aggregation::fromString('1-h'),
            StatsType::Bandwidth,
            new TimeRange(
                new DateTimeImmutable('2022-05-06'),
                $now,
            ),
            $customer,
            $now,
        );
        self::assertSame(DataSource::ClickHouse, $source);
    }

    #[DataProvider('provideParams')]
    public function testStatsSourceSelector(
        DataSource|null $preferredDataSource,
        Aggregation $aggregation,
        StatsType $statsType,
        TimeRange $timeRange,
        Customer $customer,
        DateTimeImmutable $now,
        bool $avoidClickHouse,
        DataSource $expectedSource,
    ): void {
        $sourceSelector = new StatsSourceSelector($avoidClickHouse);
        $source = $sourceSelector->getDataSource(
            $preferredDataSource,
            $aggregation,
            $statsType,
            $timeRange,
            $customer,
            $now,
        );
        self::assertSame($expectedSource, $source);
    }

    /**
     * @return iterable<string, ProviderDataShape>
     *
     * @throws InvalidArgumentException
     */
    public static function provideParams(): iterable
    {
        $now = new DateTimeImmutable('2022-05-07 midnight');
        $customer = Stub::create(
            Customer::class,
            [
                Customer::FieldId => 1,
                Customer::FieldNewId => Uuid::fromString('ca10321e-d235-4dc7-a160-e5a24e4feee1'),
            ],
        );

        yield 'forced use ClickHouse' => [
            DataSource::ClickHouse,
            Aggregation::fromString('1-h'),
            StatsType::Bandwidth,
            new TimeRange(
                new DateTimeImmutable('2022-05-06'),
                $now,
            ),
            $customer,
            $now,
            false,
            DataSource::ClickHouse,
        ];

        yield 'forced use ARA' => [
            DataSource::Ara,
            Aggregation::fromString('1-h'),
            StatsType::Traffic,
            new TimeRange(
                new DateTimeImmutable('2022-05-06'),
                $now,
            ),
            $customer,
            $now,
            false,
            DataSource::Ara,
        ];

        yield 'from in past should use ARA' => [
            null,
            Aggregation::fromString('1-h'),
            StatsType::Traffic,
            new TimeRange(
                new DateTimeImmutable('2020-10-01'),
                $now,
            ),
            $customer,
            $now,
            false,
            DataSource::Ara,
        ];

        yield 'costs should use ARA' => [
            null,
            Aggregation::fromString('1-h'),
            StatsType::Costs,
            new TimeRange(
                new DateTimeImmutable('2022-05-06'),
                $now,
            ),
            $customer,
            $now,
            false,
            DataSource::Ara,
        ];

        yield 'avoidClickhouse should use ARA' => [
            null,
            Aggregation::fromString('1-h'),
            StatsType::Traffic,
            new TimeRange(
                new DateTimeImmutable('2022-05-06'),
                $now,
            ),
            $customer,
            $now,
            true,
            DataSource::Ara,
        ];

        yield 'tiktok should use ARA' => [
            null,
            Aggregation::fromString('1-h'),
            StatsType::Traffic,
            new TimeRange(
                new DateTimeImmutable('2023-05-01'),
                $now,
            ),
            Stub::create(
                Customer::class,
                [
                    Customer::FieldId => 73142,
                    Customer::FieldNewId => Uuid::fromString('bea8f9f5-e300-4b5d-817a-accf07834169'),
                ],
            ),
            $now,
            false,
            DataSource::Ara,
        ];

        yield 'team member of tiktok should use ARA' => [
            null,
            Aggregation::fromString('1-h'),
            StatsType::Traffic,
            new TimeRange(
                new DateTimeImmutable('2023-05-01'),
                $now,
            ),
            Stub::create(
                Customer::class,
                [
                    Customer::FieldId => 78336,
                    Customer::FieldParentAccountId => 73142,
                    Customer::FieldNewId => Uuid::fromString('edf04806-7f5c-4209-957f-f64477702a1b'),
                ],
            ),
            $now,
            false,
            DataSource::Ara,
        ];

        yield 'ordinary customer should use ClickHouse' => [
            null,
            Aggregation::fromString('1-h'),
            StatsType::Traffic,
            new TimeRange(
                new DateTimeImmutable('2023-04-01'),
                $now,
            ),
            $customer,
            $now,
            false,
            DataSource::ClickHouse,
        ];

        yield 'tik tok live-streaming should use ClickHouseLiveStreaming' => [
            null,
            Aggregation::fromString('1-h'),
            StatsType::Traffic,
            new TimeRange(
                new DateTimeImmutable('2023-04-01'),
                $now,
            ),
            Stub::create(Customer::class, [Customer::FieldId => CustomerId::tikTokStreaming()->toInt()]),
            $now,
            false,
            DataSource::ClickHouseLiveStreaming,
        ];
    }
}
