<?php

declare(strict_types=1);

namespace Cdn77\ValueObject\Tests\Identifier;

use Cdn77\ValueObject\Identifier\IntegerIdentifier;
use Cdn77\ValueObject\Tests\Identifier\Fixture\IntegerId;
use PHPUnit\Framework\Attributes\CoversClass;
use PHPUnit\Framework\Attributes\Group;

#[CoversClass(IntegerIdentifier::class)]
#[Group('unit')]
final class IntegerIdentifierTest extends IdentifierTestCase
{
    public static function providerIsNil(): iterable
    {
        yield [true, IntegerId::fromString('0')];
        yield [false, IntegerId::fromString('1')];
    }

    public static function providerCache(): iterable
    {
        yield 'nil' => [IntegerId::nil(), IntegerId::nil()];
        yield 'fromInteger' => [IntegerId::fromInteger(1), IntegerId::fromInteger(1)];
        yield 'fromString' => [IntegerId::fromString('1'), IntegerId::fromString('1')];
        yield 'fromNullableString string' => [
            IntegerId::fromNullableString('1'),
            IntegerId::fromNullableString('1'),
        ];

        yield 'fromNullableString null' => [
            IntegerId::fromNullableString(null),
            IntegerId::fromNullableString(null),
        ];
    }
}
