<?php

declare(strict_types=1);

namespace Cdn77\Api\Tests\CustomPlan\Application\Payload;

use Cdn77\Api\Core\Domain\Entity\CustomPlan\Contract;
use Cdn77\Api\Core\Domain\Entity\CustomPlan\ContractId;
use Cdn77\Api\Core\Domain\Entity\CustomPlan\CustomPlan;
use Cdn77\Api\CoreLibrary\Money\CurrencyCode;
use Cdn77\Api\CustomPlan\Application\Payload\ContractDetailsSchema;
use Cdn77\Api\CustomPlan\Application\Payload\ContractSchema;
use Cdn77\Api\CustomPlan\Application\Payload\ServicePeriodSchema;
use Cdn77\Api\CustomPlan\Domain\Dto\DescriptionLines;
use Cdn77\Api\CustomPlan\Domain\Value\BillingUnit;
use Cdn77\Api\CustomPlan\Domain\Value\ContractState;
use Cdn77\Api\Tests\TestCase;
use Cdn77\Api\Tests\Utils\WithSerializer;
use Cdn77\TestUtils\Stub;
use DateTimeImmutable;
use PHPUnit\Framework\Attributes\CoversClass;
use PHPUnit\Framework\Attributes\Group;

use function Psl\Json\encode;

#[CoversClass(ContractSchema::class)]
#[Group('integration')]
final class ContractSchemaTest extends TestCase
{
    use WithSerializer;

    public function testSerialize(): void
    {
        $usdCurrencyCode = CurrencyCode::USD;
        $description = DescriptionLines::fromArray(['Custom plan' => '300 TB per month']);
        $contract = Stub::create(Contract::class, [
            Contract::FieldId => ContractId::fromString('870eb924-f82e-4244-a241-334f210f8a9d')->toUid(),
            Contract::FieldActiveFrom => new DateTimeImmutable('2021-07-01 midnight'),
            Contract::FieldActiveTo => new DateTimeImmutable('2021-07-31 23:59:59'),
            Contract::FieldCustomPlan => Stub::create(CustomPlan::class, [
                CustomPlan::FieldBillingUnit => BillingUnit::TebiBytes,
            ]),
            Contract::FieldCurrency => $usdCurrencyCode,
            Contract::FieldDescription => $description->toJsonString(),
            Contract::FieldPrice => 199,
            Contract::FieldState => ContractState::Paid,
            Contract::FieldBillingUnit => BillingUnit::TebiBytes,
            Contract::FieldVolume => 300,
            Contract::FieldMessage => 'Dear Customer ...',
        ]);

        $customPlanSchema = ContractSchema::fromEntity($contract);

        $schema = $this->getSerializer()->serialize($customPlanSchema, 'json');

        $expectedSchema = encode([
            ContractSchema::FieldId => $contract->getId()->toString(),
            ContractSchema::FieldServicePeriod => [
                ServicePeriodSchema::FieldActiveFrom => $contract->getActiveFrom()->format(DateTimeImmutable::ATOM),
                ServicePeriodSchema::FieldActiveTo => $contract->getActiveTo()->format(DateTimeImmutable::ATOM),
            ],
            ContractSchema::FieldDetails => [
                ContractDetailsSchema::FieldBillingUnit => $contract->getPlanVolume()->billingUnit->value,
                ContractDetailsSchema::FieldCurrency => $usdCurrencyCode->value,
                ContractDetailsSchema::FieldDescription => $description->toArray(),
                ContractDetailsSchema::FieldPrice => $contract->getPrice()->getAmount()->toFloat(),
                ContractDetailsSchema::FieldState => $contract->getState(),
                ContractDetailsSchema::FieldVolume => $contract->getPlanVolume()->volume->toFloat(),
            ],
            ContractSchema::FieldMessage => $contract->getMessage(),
        ]);

        self::assertJsonStringEqualsJsonString($expectedSchema, $schema);
    }
}
