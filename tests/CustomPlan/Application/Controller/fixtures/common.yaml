include:
  - ../../../../fixtures/authentication.yaml

Cdn77\Api\Core\Domain\Entity\Customer\Credentials:
  guardian_credentials:
    __construct: false
    email: '<email()>'
    password: '<(@testing_customer_hashed_password->toString())>'
    fullName: Testing Guardian
    phoneNumber: '<e164PhoneNumber()>'

Cdn77\Api\Core\Domain\Entity\Customer\Customer:
  guardian:
    __construct: false
    id: 50227
    newId: <identity(Symfony\Component\Uid\Uuid::fromString('225638fd-3241-4329-acf9-a5c742eb74d5'))>
    publicId: <identity(Symfony\Component\Uid\Uuid::fromString('31e02a27-3eb7-4cc3-bb7d-c9f286b67f3a'))>
    credentials: '@guardian_credentials'
    role: '<(Cdn77\Api\Core\Domain\Value\Customer\UserRole::Admin)>'
    createdAt: <identity(new DateTimeImmutable('2016-10-16'))>

Cdn77\Api\Core\Domain\Entity\Customer\AccountSettings:
  testing_customer_settings:
    __construct: false
    id: 10645
    customerId: '<(@testing_customer->getId()->toInt())>'
    minPayment: 1
    peckoAllowed: false
    zeroVAT: false
    foreverFreeRawLogs: false
    objectStorageEnabled: false

Cdn77\Api\Core\Domain\Entity\Invoice\Country:
  testing_country:
    __construct: false
    id: 1
    name: "Czech rep"
    shortName: "czechia"
    iso: CZ
    iso3: CZE
    continentCode: <(Cdn77\Api\Core\Domain\Continent\Value\ContinentCode::Europe)>
    euMember: true
    vatPercent: 21

Cdn77\Api\Core\Domain\Entity\Invoice\InvoiceCustomer:
  testing_invoice_customer:
    __construct: false
    id: 1
    country: '@testing_country'
    customerId: '<(@testing_customer->getId()->toInt())>'
    updatedAt: <identity(new DateTimeImmutable('2022-06-02 12:21:22'))>

Cdn77\Api\Core\Domain\Entity\Invoice\XeroContact:
  testing_xero_contact:
    __construct: false
    id: 1
    customer: '@testing_customer'
    xeroUuid:  <identity(Symfony\Component\Uid\Uuid::fromString('65bf2e26-cc40-45bd-afa9-811b974c7410'))>
    name: 'testing_xero_contact'
    createdAt: <identity(new DateTimeImmutable('2022-06-02 12:21:22'))>
    xeroType: <(Cdn77\Api\Core\Domain\Value\XeroType::Legacy)>
