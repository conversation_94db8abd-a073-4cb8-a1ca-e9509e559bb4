<?php

declare(strict_types=1);

namespace Cdn77\Api\Tests\Cdn\Domain\Configuration;

use Cdn77\Api\Cdn\Domain\Configuration\CdnSslConfigurator;
use Cdn77\Api\Cdn\Domain\Dto\SslSettings;
use Cdn77\Api\Cdn\Domain\Repository\CdnSslRepository;
use Cdn77\Api\Cdn\Domain\Value\NoAssignedSsl;
use Cdn77\Api\Core\Domain\Entity\Cdn\Cdn;
use Cdn77\Api\Core\Domain\Entity\Cdn\CdnHttp;
use Cdn77\Api\Core\Domain\Entity\Cdn\CdnId;
use Cdn77\Api\Core\Domain\Entity\Cdn\CdnSsl;
use Cdn77\Api\Core\Domain\Entity\Customer\CustomerId;
use Cdn77\Api\Core\Domain\Entity\Ssl\Ssl;
use Cdn77\Api\Core\Domain\Repository\Cdn\CdnHttpRepository;
use Cdn77\Api\Core\Domain\Repository\SslRepository;
use Cdn77\Api\Core\Domain\Value\AccessRestriction\AccessType;
use Cdn77\Api\Ssl\Domain\Contract\SslId;
use Cdn77\Api\Ssl\Domain\Enum\SslType;
use Cdn77\Api\Ssl\Domain\Exception\SslNotFound;
use Cdn77\Api\Ssl\Domain\Validation\SslAccessValidator;
use Cdn77\Api\Tests\TestCase;
use Cdn77\TestUtils\Stub;
use DateTimeImmutable;
use Generator;
use InvalidArgumentException;
use Mockery;
use PHPUnit\Framework\Attributes\CoversClass;
use PHPUnit\Framework\Attributes\DataProvider;
use PHPUnit\Framework\Attributes\Group;
use Symfony\Component\Uid\Uuid;

#[CoversClass(CdnSslConfigurator::class)]
#[Group('unit')]
final class CdnSslConfiguratorTest extends TestCase
{
    /** @throws SslNotFound */
    public function testConfigureInstantSslWhenAlreadyEnabled(): void
    {
        $now = new DateTimeImmutable('2022-08-11 12:30');
        $cdnId = CdnId::fromInteger(123456);
        $customerId = CustomerId::fromInteger(12345);
        $sslSettings = Stub::create(SslSettings::class, ['type' => SslType::InstantSsl]);
        $cdn = Stub::create(Cdn::class, ['id' => $cdnId->toInt()]);

        $cdnHttp = Stub::create(CdnHttp::class, [CdnHttp::FieldInstantSsl => $now]);

        $cdnHttpRepository = Mockery::mock(CdnHttpRepository::class);
        $cdnSslRepository = Mockery::mock(CdnSslRepository::class);
        $sslAccessValidator = Mockery::mock(SslAccessValidator::class);
        $sslRepository = Mockery::mock(SslRepository::class);

        $cdnHttpRepository
            ->shouldReceive('getForCdn')
            ->once()
            ->with($cdnId)
            ->andReturn($cdnHttp);
        $cdnSslRepository
            ->shouldReceive('findCurrentlyAssignedForCdnAndSslType')
            ->once()
            ->with($cdnId, SslType::Sni)
            ->andReturnNull();

        $configurator = new CdnSslConfigurator(
            $cdnHttpRepository,
            $cdnSslRepository,
            $sslAccessValidator,
            $sslRepository,
        );

        self::assertNull($configurator->configure($cdn, $customerId, $sslSettings, $now));
    }

    /** @throws SslNotFound */
    #[DataProvider('providerNoSslSetup')]
    public function testConfigureForType(
        SslType $sslType,
        CdnHttp $cdnHttp,
        DateTimeImmutable $now,
        CdnSsl|null $currentCdnSsl,
    ): void {
        $cdnId = CdnId::fromInteger(123456);
        $customerId = CustomerId::fromInteger(12345);
        $sslSettings = Stub::create(SslSettings::class, ['type' => $sslType]);
        $cdn = Stub::create(Cdn::class, ['id' => $cdnId->toInt()]);

        $cdnHttpRepository = Mockery::mock(CdnHttpRepository::class);
        $cdnSslRepository = Mockery::mock(CdnSslRepository::class);
        $sslAccessValidator = Mockery::mock(SslAccessValidator::class);
        $sslRepository = Mockery::mock(SslRepository::class);

        $cdnHttpRepository
            ->shouldReceive('getForCdn')
            ->once()
            ->with($cdnId)
            ->andReturn($cdnHttp);
        $cdnSslRepository
            ->shouldReceive('findCurrentlyAssignedForCdnAndSslType')
            ->once()
            ->with($cdnId, SslType::Sni)
            ->andReturn($currentCdnSsl);

        if ($currentCdnSsl !== null) {
            $cdnSslRepository
                ->shouldReceive('remove')
                ->once()
                ->with($currentCdnSsl);
        }

        $configurator = new CdnSslConfigurator(
            $cdnHttpRepository,
            $cdnSslRepository,
            $sslAccessValidator,
            $sslRepository,
        );

        $configuredSsl = $configurator->configure($cdn, $customerId, $sslSettings, $now);

        self::assertSame($sslType === SslType::None, $cdnHttp->hasInstantSslDisabled());
        self::assertInstanceOf(NoAssignedSsl::class, $configuredSsl);
    }

    /**
     * @return Generator<string, array{SslType, CdnHttp, DateTimeImmutable, CdnSsl|null}>
     *
     * @throws InvalidArgumentException
     */
    public static function providerNoSslSetup(): Generator
    {
        $now = new DateTimeImmutable('2022-08-11 12:30');

        yield 'Setup none from none' => [
            SslType::None,
            Stub::create(CdnHttp::class, [CdnHttp::FieldInstantSsl => null]),
            $now,
            null,
        ];

        yield 'Setup none from instant' => [
            SslType::None,
            Stub::create(CdnHttp::class, [CdnHttp::FieldInstantSsl => $now]),
            $now,
            null,
        ];

        yield 'Setup none from SNI' => [
            SslType::None,
            Stub::create(CdnHttp::class, [CdnHttp::FieldInstantSsl => null]),
            $now,
            Stub::create(CdnSsl::class, [
                CdnSsl::FieldSsl => Stub::create(Ssl::class, [
                    Ssl::FieldId => Uuid::fromString('0543f230-05f2-4206-b955-b8c309c6d860'),
                ]),
            ]),
        ];

        yield 'Setup instant from none' => [
            SslType::InstantSsl,
            Stub::create(CdnHttp::class, [CdnHttp::FieldInstantSsl => null]),
            $now,
            null,
        ];

        yield 'Setup instant from SNI' => [
            SslType::InstantSsl,
            Stub::create(CdnHttp::class, [CdnHttp::FieldInstantSsl => null]),
            $now,
            Stub::create(CdnSsl::class, [
                CdnSsl::FieldSsl => Stub::create(Ssl::class, [
                    Ssl::FieldId => Uuid::fromString('0543f230-05f2-4206-b955-b8c309c6d860'),
                ]),
            ]),
        ];
    }

    /** @throws SslNotFound */
    #[DataProvider('providerSniSetup')]
    public function testConfigureSniSsl(
        CdnHttp $cdnHttp,
        DateTimeImmutable $now,
        Ssl $ssl,
        CdnSsl|null $currentCdnSsl,
    ): void {
        $cdnId = CdnId::fromInteger(123456);
        $customerId = CustomerId::fromInteger(12345);
        $sslId = SslId::fromString('f926eec9-da9b-44b5-85f7-f2438f4a71e3');
        $sslType = SslType::Sni;
        $sslSettings = Stub::create(SslSettings::class, [
            'type' => $sslType,
            'sslId' => $sslId,
        ]);
        $cdn = Stub::create(Cdn::class, ['id' => $cdnId->toInt()]);

        $cdnHttpRepository = Mockery::mock(CdnHttpRepository::class);
        $cdnSslRepository = Mockery::mock(CdnSslRepository::class);
        $sslAccessValidator = Mockery::mock(SslAccessValidator::class);
        $sslRepository = Mockery::mock(SslRepository::class);

        $cdnHttpRepository
            ->shouldReceive('getForCdn')
            ->with($cdnId)
            ->andReturn($cdnHttp);
        $sslRepository
            ->shouldReceive('getForType')
            ->once()
            ->with($sslId, $sslType)
            ->andReturn($ssl);
        $sslAccessValidator
            ->shouldReceive('isAllowed')
            ->once()
            ->with($ssl, $customerId, AccessType::SslRead)
            ->andReturn(true);
        $cdnSslRepository
            ->shouldReceive('findCurrentlyAssignedForCdnAndSslType')
            ->once()
            ->with($cdnId, $sslType)
            ->andReturn($currentCdnSsl);

        if ($currentCdnSsl !== null) {
            $cdnSslRepository
                ->shouldReceive('remove')
                ->once()
                ->with($currentCdnSsl);
        }

        $cdnSslRepository
            ->shouldReceive('add')
            ->once();

        $configurator = new CdnSslConfigurator(
            $cdnHttpRepository,
            $cdnSslRepository,
            $sslAccessValidator,
            $sslRepository,
        );

        $configuredSsl = $configurator->configure($cdn, $customerId, $sslSettings, $now);

        self::assertTrue($cdnHttp->hasInstantSslDisabled());
        self::assertSame($ssl, $configuredSsl);
    }

    /**
     * @return Generator<string, array{CdnHttp, DateTimeImmutable, Ssl, CdnSsl|null}>
     *
     * @throws InvalidArgumentException
     */
    public static function providerSniSetup(): Generator
    {
        $ssl = Stub::create(Ssl::class, []);
        $now = new DateTimeImmutable('2022-08-11 12:30');

        yield 'SNI Setup From Instant Ssl' => [
            Stub::create(CdnHttp::class, [CdnHttp::FieldInstantSsl => $now]),
            $now,
            $ssl,
            null,
        ];

        yield 'Reconfigure SNI' => [
            Stub::create(CdnHttp::class, [CdnHttp::FieldInstantSsl => null]),
            $now,
            $ssl,
            Stub::create(CdnSsl::class, [
                CdnSsl::FieldSsl => Stub::create(Ssl::class, [
                    Ssl::FieldId => Uuid::fromString('0543f230-05f2-4206-b955-b8c309c6d860'),
                ]),
            ]),
        ];

        yield 'SNI Setup From None' => [
            Stub::create(CdnHttp::class, [CdnHttp::FieldInstantSsl => null]),
            $now,
            $ssl,
            null,
        ];
    }
}
