<?php

declare(strict_types=1);

namespace Cdn77\Api\Tests\Cdn\Domain\Command\Tsunami;

use Cdn77\Api\Cdn\Domain\Command\Tsunami\UpdateTsunami;
use Cdn77\Api\Cdn\Domain\Command\Tsunami\UpdateTsunamiHandler;
use Cdn77\Api\Cdn\Domain\Dto\Tsunami\Stats;
use Cdn77\Api\Cdn\Domain\Dto\Tsunami\UpdateScope;
use Cdn77\Api\Cdn\Domain\Finder\CdnDomainsFinder;
use Cdn77\Api\Cdn\Domain\Repository\TsunamiGroupRepository;
use Cdn77\Api\Cdn\Domain\Tsunami\AllowedTargetsResolver;
use Cdn77\Api\Cdn\Domain\Tsunami\CdnDomainLevelsMapper;
use Cdn77\Api\Cdn\Domain\Tsunami\CdnResourceTargetsResolver;
use Cdn77\Api\Cdn\Domain\Tsunami\TargetIpResolver;
use Cdn77\Api\Cdn\Domain\Validation\TsunamiAccessValidator;
use Cdn77\Api\Core\Domain\Entity\Cdn\Cdn;
use Cdn77\Api\Core\Domain\Entity\Cdn\CdnId;
use Cdn77\Api\Core\Domain\Entity\Cdn\TsunamiGroup;
use Cdn77\Api\Core\Domain\Entity\Customer\AccountSettings;
use Cdn77\Api\Core\Domain\Entity\Customer\Customer;
use Cdn77\Api\Core\Domain\Entity\Customer\CustomerUuid;
use Cdn77\Api\Core\Domain\Entity\Datacenter\ServerGroupId;
use Cdn77\Api\Core\Domain\Hurricane\HurricaneApi;
use Cdn77\Api\Core\Domain\Hurricane\Tsunami\ActiveDdos;
use Cdn77\Api\Core\Domain\Hurricane\Tsunami\ProtectionLevel;
use Cdn77\Api\Core\Domain\MC\Dto\Anycast;
use Cdn77\Api\Core\Domain\MC\MCApi;
use Cdn77\Api\Core\Domain\Repository\Cdn\CdnRepository;
use Cdn77\Api\Core\Domain\Repository\Customer\AccountSettingsRepository;
use Cdn77\Api\Core\Domain\Repository\CustomerRepository;
use Cdn77\Api\Core\Domain\Resolver\AffectedCustomerResolver;
use Cdn77\Api\Core\Domain\Validation\CdnAccessValidator;
use Cdn77\Api\Core\Domain\Value\IpAddress;
use Cdn77\Api\Core\Infrastructure\Hurricane\Endpoint\ActiveDdosListEndpoint;
use Cdn77\Api\Server\Domain\Dto\Server;
use Cdn77\Api\Server\Domain\Value\PopId;
use Cdn77\Api\Tests\TestCase;
use Cdn77\TestUtils\Stub;
use Ds\Map;
use Ds\Pair;
use Ds\Set;
use Ds\Vector;
use Mockery;
use PHPUnit\Framework\Attributes\CoversClass;
use PHPUnit\Framework\Attributes\Group;
use Symfony\Component\Uid\Uuid;

use function Cdn77\Functions\mapFromEntries;
use function Cdn77\Functions\mapFromIterable;

#[CoversClass(UpdateTsunamiHandler::class)]
#[Group('unit')]
final class UpdateTsunamiHandlerTest extends TestCase
{
    public function testUpdateWithCdnProtectionLevels(): void
    {
        $customerId = CustomerUuid::cdnAll();
        $cdnIdA = CdnId::fromInteger(123456780);
        $cdnIdB = CdnId::fromInteger(123456781);
        $protectionLevelA = ProtectionLevel::High;
        $protectionLevelB = ProtectionLevel::Low;
        $defaultProtectionLevel = ProtectionLevel::None;
        $domainA = 'cname.cdn1.com';
        $domainB = 'origin.cdn1.com';
        $domainC = '123456780.cdn77.com';
        $domainD = 'cname.cdn2.org';
        $domainE = 'origin.cdn2.org';
        $domainF = '123456781.cdn77.com';
        $tsunamiServerGroup = ServerGroupId::fromInteger(1);

        $customer = Stub::create(Customer::class, [
            Customer::FieldNewId => Uuid::fromString('9ce1efd5-ade5-4389-aa51-8b1f1cd1e172'),
        ]);
        $tsunamiEnabledGroup = Stub::create(TsunamiGroup::class, [
            TsunamiGroup::FieldServerGroupId => $tsunamiServerGroup->toInt(),
            TsunamiGroup::FieldTsunamiEnabled => true,
        ]);
        $tsunamiPop = PopId::fromInteger(1);
        $tsunamiTargetIp = IpAddress::fromString('***********');
        $tsunamiServer = Stub::create(Server::class, [
            'ips' => new Set([$tsunamiTargetIp]),
            'popId' => $tsunamiPop,
        ]);
        $tsunamiAnycastIp = IpAddress::fromString('***********');
        $tsunamiAnycast = Stub::create(Anycast::class, [
            'popIds' => new Set([$tsunamiPop]),
            'ips' => new Set([$tsunamiAnycastIp]),
        ]);

        $accountSettingsRepository = Mockery::mock(AccountSettingsRepository::class);
        $cdnAccessValidator = Mockery::mock(CdnAccessValidator::class);
        $cdnDomainsFinder = Mockery::mock(CdnDomainsFinder::class);
        $cdnRepository = Mockery::mock(CdnRepository::class);
        $customerRepository = Mockery::mock(CustomerRepository::class);
        $hurricaneApi = Mockery::mock(HurricaneApi::class);
        $mcApi = Mockery::mock(MCApi::class);
        $tsunamiGroupRepository = Mockery::mock(TsunamiGroupRepository::class);
        $tsunamiAccessValidator = new TsunamiAccessValidator($accountSettingsRepository);

        $affectedCustomerResolver = new AffectedCustomerResolver($customerRepository);
        $targetIpResolver = new TargetIpResolver($mcApi);
        $cdnResourceTargetsResolver = new CdnResourceTargetsResolver(
            $cdnRepository,
            $targetIpResolver,
            $tsunamiGroupRepository,
        );
        $tsunamiAllowedTargetsResolver = new AllowedTargetsResolver($targetIpResolver, $tsunamiGroupRepository);

        $accountSettingsRepository
            ->expects('getForCustomerUuid')
            ->andReturn(Stub::create(
                AccountSettings::class,
                [AccountSettings::FieldTsunamiEnabled => true],
            ));
        $customerRepository
            ->expects('getForId')
            ->with($customerId)
            ->andReturn($customer);
        $cdnAccessValidator
            ->expects('areAllAccessible')
            ->andReturn(true);
        $cdnDomainsFinder
            ->expects('findForCdns')
            ->andReturn(mapFromIterable(
                [
                    $cdnIdA->toInt() => new Set([$domainA, $domainB, $domainC]),
                    $cdnIdB->toInt() => new Set([$domainD, $domainE, $domainF]),
                ],
                static fn (int $cdnId, Set $domains) => new Pair(CdnId::fromInteger($cdnId), $domains),
            ),);
        $cdnRepository
            ->expects('findByIds')
            ->andReturn([
                Stub::create(Cdn::class, [
                    Cdn::FieldGroupId => $tsunamiServerGroup->toInt(),
                ]),
                Stub::create(Cdn::class, [
                    Cdn::FieldGroupId => $tsunamiServerGroup->toInt(),
                ]),
            ]);
        $tsunamiGroupRepository
            ->expects('findForServerGroups')
            ->andYield($tsunamiEnabledGroup);
        $mcApi
            ->expects('getServersForGroupId')
            ->andReturn(new Vector([$tsunamiServer]));
        $mcApi
            ->expects('getAnycasts')
            ->andReturn(new Vector([$tsunamiAnycast]));
        $hurricaneApi
            ->expects('listActiveDdoses')
            ->andReturn(new Set([
                new ActiveDdos(
                    Uuid::fromString('e836dad3-3916-4f6b-a574-e8f14c63ad58'),
                    $tsunamiTargetIp,
                    '80',
                    true,
                    Stats::fromArray([
                        ActiveDdosListEndpoint::ResponseFieldTsunamiInfoLevels => [],
                        'Level' => 1,
                        'Request' => 1,
                        'Blocked' => 1,
                        'Whitelisted' => 0,
                        'Passed' => 0,
                    ]),
                ),
                new ActiveDdos(
                    Uuid::fromString('2bccaade-5d1f-4a47-a3b7-19f6dc6e68a2'),
                    $tsunamiAnycastIp,
                    '80',
                    true,
                    Stats::fromArray([
                        ActiveDdosListEndpoint::ResponseFieldTsunamiInfoLevels => [],
                        'Level' => 1,
                        'Request' => 1,
                        'Blocked' => 1,
                        'Whitelisted' => 0,
                        'Passed' => 0,
                    ]),
                ),
            ]));
        $hurricaneApi
            ->expects('changeProtectionLevel')
            ->twice()
            ->withArgs(static fn (
                CustomerUuid $customerId,
                Uuid $tsunamiId,
                ProtectionLevel $protectionLevel,
                Map $domainProtectionLevels,
            ) => $domainProtectionLevels->toArray() === [
                $domainA => $protectionLevelA,
                $domainB => $protectionLevelA,
                $domainC => $protectionLevelA,
                $domainD => $protectionLevelB,
                $domainE => $protectionLevelB,
                $domainF => $protectionLevelB,
            ]);

        $handler = new UpdateTsunamiHandler(
            $affectedCustomerResolver,
            $tsunamiAllowedTargetsResolver,
            $cdnAccessValidator,
            new CdnDomainLevelsMapper($cdnDomainsFinder),
            $cdnResourceTargetsResolver,
            $customerRepository,
            $hurricaneApi,
            $tsunamiAccessValidator,
        );

        $handler->handle(new UpdateTsunami(
            $customerId,
            new UpdateScope(
                new Set(),
                new Set([$cdnIdA, $cdnIdB]),
                $defaultProtectionLevel,
                mapFromEntries([
                    [$cdnIdA, $protectionLevelA],
                    [$cdnIdB, $protectionLevelB],
                ]),
            ),
        ));
    }
}
