<?php

declare(strict_types=1);

namespace Cdn77\Api\Tests\Cdn\Domain\Command;

use Cdn77\Api\Cdn\Domain\Command\ChangeLocationGroup;
use Cdn77\Api\Cdn\Domain\Command\ChangeLocationGroupHandler;
use Cdn77\Api\Core\Domain\Entity\Cdn\Cdn;
use Cdn77\Api\Core\Domain\Entity\Datacenter\ServerGroupId;
use Cdn77\Api\Core\Domain\NxgApi\NxgApi;
use Cdn77\Api\Core\Domain\Repository\Cdn\CdnRepository;
use Cdn77\Api\Tests\TestCase;
use Cdn77\TestUtils\Stub;
use Mockery;
use PHPUnit\Framework\Attributes\CoversClass;
use PHPUnit\Framework\Attributes\Group;

#[CoversClass(ChangeLocationGroupHandler::class)]
#[CoversClass(ChangeLocationGroup::class)]
#[Group('unit')]
final class ChangeLocationGroupHandlerTest extends TestCase
{
    public function testChangeLocationGroupHandlerSuccess(): void
    {
        $serverGroupId = ServerGroupId::fromInteger(4);
        $cdn = Stub::create(Cdn::class, [
            'id' => 1,
            'groupId' => 159,
        ]);
        $cdnId = $cdn->getId();

        $cdnRepository = Mockery::mock(CdnRepository::class);
        $nxgApi = Mockery::mock(NxgApi::class);
        $commandHandler = new ChangeLocationGroupHandler($cdnRepository, $nxgApi);
        $command = new ChangeLocationGroup($cdnId, $serverGroupId);

        $cdnRepository
            ->shouldReceive('get')
            ->once()
            ->with($cdnId)
            ->andReturn($cdn);
        $nxgApi->shouldReceive('changeLocationGroup')->once()->with($cdnId, $serverGroupId);

        $commandHandler->handle($command);

        self::assertTrue($serverGroupId->equals($cdn->getGroupId()));
    }
}
