<?php

declare(strict_types=1);

namespace Cdn77\Api\Tests\Cdn\Application\Payload\ResponseHeaders;

use Cdn77\Api\Cdn\Application\Payload\ResponseHeaders\ResponseHeadersSchema;
use Cdn77\Api\Cdn\Domain\Exception\InvalidCustomHeader;
use Cdn77\Api\Cdn\Domain\Value\Parameters\CustomResponseHeaders;
use Cdn77\Api\Tests\TestCase;
use Cdn77\Api\Tests\Utils\WithSerializer;
use Generator;
use JMS\Serializer\Exception\RuntimeException;
use PHPUnit\Framework\Attributes\CoversClass;
use PHPUnit\Framework\Attributes\DataProvider;
use PHPUnit\Framework\Attributes\Group;

use function array_filter;
use function iterator_to_array;

#[CoversClass(ResponseHeadersSchema::class)]
#[Group('integration')]
final class ResponseHeadersSchemaTest extends TestCase
{
    use WithSerializer;

    /**
     * @throws InvalidCustomHeader
     * @throws RuntimeException
     */
    #[DataProvider('providerPayload')]
    public function testDeserializePayload(
        string $payload,
        int $expectedCount,
        CustomResponseHeaders $expectedCustomResponseHeaders,
    ): void {
        $schema = $this->getSerializer()->deserialize($payload, ResponseHeadersSchema::class, 'json');
        $errors = array_filter(iterator_to_array($schema->validateSchemaProperties()));

        self::assertCount($expectedCount, $errors);

        if ($errors !== []) {
            return;
        }

        $customResponseHeaders = $schema->toDto();

        self::assertSame($expectedCustomResponseHeaders->toArray(), $customResponseHeaders->toArray());
    }

    /**
     * @return Generator<string, array<string|int|CustomResponseHeaders>>
     *
     * @throws InvalidCustomHeader
     */
    public static function providerPayload(): Generator
    {
        yield 'empty array' => [
            <<<'JSON'
{
    "headers": []
}
JSON,
            0,
            CustomResponseHeaders::empty(),
        ];

        yield 'empty object' => [
            <<<'JSON'
{
    "headers": {}
}
JSON,
            0,
            CustomResponseHeaders::empty(),
        ];

        yield 'response headers' => [
            <<<'JSON'
{
    "headers": [{"name": "Access-Control-Allow-Origin", "value": "test.com"}]
}
JSON,
            0,
            CustomResponseHeaders::fromArray(['Access-Control-Allow-Origin' => 'test.com']),
        ];
    }
}
