include:
    - common.yaml

Cdn77\Api\Core\Domain\Entity\Cdn\Cdn:
    testing_cdn:
        __construct: false
        id: 1235679
        legacyId: 93425436
        customer: '@testing_customer'
        label: test_label
        url: '12345679.rsc.cdn77.dev'
        creationTime: <identity(new DateTimeImmutable('2022-01-18'))>
        type: <(Cdn77\Api\Cdn\Domain\Value\CdnType::Http)>
        groupId: 4
    testing_cdn_empty_values:
        __construct: false
        id: 12356797
        legacyId: 93425437
        customer: '@testing_customer'
        label: test_label
        url: '123456797.rsc.cdn77.dev'
        creationTime: <identity(new DateTimeImmutable('2022-01-18'))>
        type: <(Cdn77\Api\Cdn\Domain\Value\CdnType::Http)>
        groupId: 4

Cdn77\Api\Core\Domain\Entity\Origin\StreamOrigin:
    testing_stream_origin:
        __construct: false
        id: 423642
        host: 'test-1.s.cdn77.com'
        continent: Europe
        country: Prague
        uuid: <identity(Symfony\Component\Uid\Uuid::fromString('ea49d2e3-75c4-4e2c-8da6-75e1963a4587'))>
    testing_stream_origin_second:
        __construct: false
        id: 423643
        host: 'test-2.s.cdn77.com'
        continent: Europe
        country: Prague
        uuid: <identity(Symfony\Component\Uid\Uuid::fromString('c7968f8b-0fcf-4ff9-9beb-675f58ce02e1'))>

Cdn77\Api\Core\Domain\Entity\Cdn\StreamCdn:
    testing_stream_cdn:
        __construct: false
        id: 231432
        cdn: '@testing_cdn'
        createdAt: <identity(new DateTimeImmutable('2022-01-18'))>
        protocol: <(Cdn77\Api\Cdn\Domain\Value\StreamProtocol::Rtmp)>
        origin: '@testing_stream_origin'
        port: 1936
        path: 'static'
        password: 'def50200ca614e64d6982f5d4b2c6b2e4000d4238f37eecdc847fa8c4da9da0eaf523912ade326221cb2304b61839ae4bd9b1f1e30c9656a9fc9e386102fe88b89c213a19334d0a5de82ee4af928ed7492c5df1271f800ae7197b40df781f8223679'
        queryKey: '123456789'
    testing_stream_cdn_second:
        __construct: false
        id: 231433
        cdn: '@testing_cdn_empty_values'
        createdAt: <identity(new DateTimeImmutable('2022-01-18'))>
        protocol: <(Cdn77\Api\Cdn\Domain\Value\StreamProtocol::Rtmp)>
        origin: '@testing_stream_origin_second'
        port: 1937
        path: ''
        password: ''
        queryKey: '1234567890'

Cdn77\Api\Core\Domain\Entity\Cdn\CdnHttp:
    testing_cdn_http:
        __construct: false
        id: 987622225
        maxAge: <(Cdn77\Api\Cdn\Domain\Value\MaxAge::default()->minutes)>
        cdnLegacyId: 93425436
        corsOriginHeader: false
        allQueryStringsIgnoredAt: <identity(new DateTimeImmutable('2018-11-29'))>
        forwardHostHeader: true
        httpsRedirectCode: null
        ignoreSetCookie: null
        instantSsl: <identity(new DateTimeImmutable('2018-11-29'))>
        mp4PseudoOn: null
        originId: null
        storageSecret: null
        secureToken: null
        secureTokenType: <(Cdn77\Api\Cdn\Domain\Value\SecureTokenType::None)>
        quic: true
        waf: true
        rateLimit: true
        contentDispositionType: <(Cdn77\Api\Cdn\Domain\Value\Parameters\ContentDispositionType::Parameter)>
        customOriginHeaders: '{"TestKey": "TestValue"}'
        customResponseHeaders: >
            <identity('[{"name": "Access-Control-Allow-Origin", "value": "test.com"}, {"name": "some-header", "value": "some-value"}]')>
        followRedirectCodes: []
    testing_cdn_http_second:
        __construct: false
        id: 987622226
        maxAge: <(Cdn77\Api\Cdn\Domain\Value\MaxAge::default()->minutes)>
        cdnLegacyId: 93425437
        corsOriginHeader: false
        allQueryStringsIgnoredAt: <identity(new DateTimeImmutable('2018-11-29'))>
        forwardHostHeader: true
        httpsRedirectCode: null
        ignoreSetCookie: null
        instantSsl: <identity(new DateTimeImmutable('2018-11-29'))>
        mp4PseudoOn: null
        originId: null
        storageSecret: null
        secureToken: null
        secureTokenType: <(Cdn77\Api\Cdn\Domain\Value\SecureTokenType::None)>
        quic: true
        waf: true
        rateLimit: true
        contentDispositionType: <(Cdn77\Api\Cdn\Domain\Value\Parameters\ContentDispositionType::Parameter)>
        customOriginHeaders: '{"TestKey": "TestValue"}'
        customResponseHeaders: >
            <identity('[{"name": "Access-Control-Allow-Origin", "value": "test.com"}, {"name": "some-header", "value": "some-value"}]')>
        followRedirectCodes: []

Cdn77\Api\Core\Domain\Entity\Cdn\CdnHttpProtection:
    testing_cdn_protection:
        __construct: false
        id: 1243244234
        cdnLegacyId: 93425436
        gpType: null
        ippType: null
        hlpType: null
        hlpDenyEmptyReferer: false
    testing_cdn_protection_second:
        __construct: false
        id: 1243244235
        cdnLegacyId: 93425437
        gpType: null
        ippType: null
        hlpType: null
        hlpDenyEmptyReferer: false

Cdn77\Api\Core\Domain\Entity\Customer\CustomerCdnSettings:
    testing_customer_cdn_settings:
        __construct: false
        id: 10902
        customerId: '<(@testing_customer->getId()->toInt())>'
        dataCentersEditEnabled: true
        maxCnames: 10
        maxCdns: 10
        maxTrialCdns: 10
        maxDeletedCdns: 10
        maxGeoProtectionCountries: 10
        maxHotlinkProtectionDomains: 10
        maxIpProtectionAddresses: 10
        maxIgnoredQueryParams: 10
        maxIgnoredQueryParamsLength: 10
        minDataCenters: 1
        purgeAllDisabled: false
        prefetchMode: <(Cdn77\Api\Core\Domain\Value\PrefetchMode::Disabled)>
