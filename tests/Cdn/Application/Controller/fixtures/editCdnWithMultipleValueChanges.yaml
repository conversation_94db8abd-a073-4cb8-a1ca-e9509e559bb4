include:
    - common.yaml

Cdn77\Api\Core\Domain\Entity\Cdn\Cdn:
    testing_cdn:
        __construct: false
        id: 123456780
        legacyId: 12344
        customer: '@testing_customer'
        label: test_cdn
        url: '123456780.rsc.cdn77.eu'
        creationTime: <identity(new DateTimeImmutable('2018-11-29'))>
        type: <(Cdn77\Api\Cdn\Domain\Value\CdnType::Http)>
        groupId: 4

Cdn77\Api\Core\Domain\Entity\Origin\Origin:
    test_origin:
        __construct: false
        id: <identity(Symfony\Component\Uid\Uuid::fromString('0b839424-0421-4e0d-b458-7f7e95c13021'))>
        awsAccessKeyId: null
        awsAccessKeySecret: null
        awsRegion: null
        baseDir: '/path/to/dir'
        customerId: <(@testing_customer->getNewId()->toUid())>
        createdAt: <identity(new DateTimeImmutable('2021-02-02 12:21:22'))>
        label: 'Cdn origin'
        port: null
        scheme: <(Cdn77\Api\Core\Domain\Value\OriginScheme::Https)>
        timeout: null
        type: <(Cdn77\Api\Core\Domain\Value\Origin\ConnectionType::Url)>
        host: 'origin.test.com'

Cdn77\Api\Core\Domain\Entity\Cdn\CdnHttp:
    testing_cdn_http:
        __construct: false
        id: 18901
        maxAge: <(Cdn77\Api\Cdn\Domain\Value\MaxAge::default()->minutes)>
        cdnLegacyId: 12344
        corsOriginHeader: false
        allQueryStringsIgnoredAt: null
        forwardHostHeader: true
        httpsRedirectCode: null
        ignoreSetCookie: null
        instantSsl: <identity(new DateTimeImmutable('2018-11-29'))>
        mp4PseudoOn: null
        originId: <(@test_origin->getId()->toUid())>
        storageSecret: null
        secureToken: null
        secureTokenType: <(Cdn77\Api\Cdn\Domain\Value\SecureTokenType::None)>
        quic: false
        waf: false
        followRedirectCodes: []

Cdn77\Api\Core\Domain\Entity\AddOn\AddOn:
    test_sni_addon:
        __construct: false
        id: 123456788
        activatedAt: <identity(new DateTimeImmutable('2021-02-02 12:21:22'))>
        customer: "@testing_customer"
        name: 'Custom SNI SSL for example.com'
        period: 1

Cdn77\Api\Core\Domain\Entity\Ssl\Ssl:
    test_ssl:
        __construct: false
        legacyId: 12345
        id: <identity(Symfony\Component\Uid\Uuid::fromString('0f4ac4e6-fb50-43f0-970b-5f690e2a2d4c'))>
        addOn: "@test_sni_addon"
        certificate: '---BEGIN CERTIFICATE---MIIEvwIBADANBgkqhkiG9w0BAQEFAAS---END CERTIFICATE---'
        expiresAt: <identity(new DateTimeImmutable('2024-04-04T14:53:22+02:00'))>
        privateKey: '---BEGIN RSA PRIVATE KEY---MNQCS6FJUOgqF7bsfs2MsoDP---END PRIVATE KEY---'
        type: <identity(Cdn77\Api\Ssl\Domain\Enum\SslType::Sni)>
