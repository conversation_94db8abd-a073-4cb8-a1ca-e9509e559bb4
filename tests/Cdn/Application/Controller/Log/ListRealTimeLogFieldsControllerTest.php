<?php

declare(strict_types=1);

namespace Cdn77\Api\Tests\Cdn\Application\Controller\Log;

use Cdn77\Api\Cdn\Application\Controller\Log\ListRealTimeLogFieldsController;
use Cdn77\Api\Cdn\Domain\Value\Log\LoggingFieldAvailability;
use Cdn77\Api\Cdn\Domain\Value\Log\LoggingFieldName;
use Cdn77\Api\Core\Domain\Entity\Customer\CustomerUuid;
use Cdn77\Api\Core\Domain\Entity\RealTimeLog\RealTimeLogFieldId;
use Cdn77\Api\Tests\ORMFixtures\Api\PersonalTokenFactory;
use Cdn77\Api\Tests\ORMFixtures\Cdn\RealTimeLogFieldCategoryFactory;
use Cdn77\Api\Tests\ORMFixtures\Cdn\RealTimeLogFieldFactory;
use Cdn77\Api\Tests\ORMFixtures\Customer\CustomerFactory;
use Cdn77\Api\Tests\TestCase;
use Cdn77\Api\Tests\Utils\FlushAndClear;
use Cdn77\Api\Tests\Utils\TokenGenerator;
use Cdn77\Api\Tests\Utils\WithBrowser;
use Cdn77\Api\Tests\Utils\WithPostgres;
use PHPUnit\Framework\Attributes\CoversClass;
use PHPUnit\Framework\Attributes\Group;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

#[CoversClass(ListRealTimeLogFieldsController::class)]
#[Group('integration')]
final class ListRealTimeLogFieldsControllerTest extends TestCase
{
    use WithPostgres;
    use WithBrowser;

    public function testListController(): void
    {
        $entityManager = $this->entityManager();

        $customerId = CustomerUuid::fromString('90f09518-2f97-4f17-84e2-6b278e3e6725');
        $customer = CustomerFactory::new($entityManager)->create(customerUuid: $customerId);
        $entityManager->persist($customer);

        $timestampCategory = RealTimeLogFieldCategoryFactory::new($entityManager)
            ->create('Timestamp & Timing Data', 1);
        $entityManager->persist($timestampCategory);
        $cacheCategory = RealTimeLogFieldCategoryFactory::new($entityManager)
            ->create('Cache & Content Data', 2);
        $entityManager->persist($cacheCategory);

        $timestampField = RealTimeLogFieldFactory::new($entityManager)->create(
            RealTimeLogFieldId::fromString('be7c0570-7f48-4cf5-85d0-b4f8ed1928b0'),
            LoggingFieldName::fromString('timestamp'),
            LoggingFieldAvailability::Public,
            $timestampCategory,
            1,
            'Human readable conversion of the startTimeMs field in the ISO 8601 format.',
        );
        $entityManager->persist($timestampField);
        $startTimeMsField = RealTimeLogFieldFactory::new($entityManager)->create(
            RealTimeLogFieldId::fromString('cffe28b5-2a57-456c-b126-6e9f4609d990'),
            LoggingFieldName::fromString('startTimeMs'),
            LoggingFieldAvailability::Public,
            $timestampCategory,
            2,
            'Unix timestamp at which the edge received request from the client.',
        );
        $entityManager->persist($startTimeMsField);
        $resourceIdField = RealTimeLogFieldFactory::new($entityManager)->create(
            RealTimeLogFieldId::fromString('40bc4666-b2d7-4292-95da-82512febd67f'),
            LoggingFieldName::fromString('resourceID'),
            LoggingFieldAvailability::Public,
            $cacheCategory,
            1,
            'ID of the CDN77 resource.',
        );
        $entityManager->persist($resourceIdField);
        $internalLayerField = RealTimeLogFieldFactory::new($entityManager)->create(
            RealTimeLogFieldId::fromString('ebbc8995-e489-4ce6-8053-3561d6b83614'),
            LoggingFieldName::fromString('layer'),
            LoggingFieldAvailability::Internal,
        );
        $entityManager->persist($internalLayerField);

        $token = PersonalTokenFactory::new($entityManager)->create($customer->getId());
        $entityManager->persist($token);

        FlushAndClear::do($entityManager);

        $response = $this->performHttpRequest(
            Request::METHOD_GET,
            '/v3/cdn/real-time-logs/fields',
            $this->prepareAuthHeaders(TokenGenerator::generate($token->getId(), 'validClapApiBearerTokenIs32*Char')),
        );

        self::assertHttpCode(Response::HTTP_OK, $response);
        self::assertJsonStringEqualsJsonString(
            <<<'JSON'
[
  {
    "id": "be7c0570-7f48-4cf5-85d0-b4f8ed1928b0",
    "name": "timestamp",
    "category": "Timestamp & Timing Data",
    "description": "Human readable conversion of the startTimeMs field in the ISO 8601 format."
  },
  {
    "id": "cffe28b5-2a57-456c-b126-6e9f4609d990",
    "name": "startTimeMs",
    "category": "Timestamp & Timing Data",
    "description": "Unix timestamp at which the edge received request from the client."
  },
  {
    "id": "40bc4666-b2d7-4292-95da-82512febd67f",
    "name": "resourceID",
    "category": "Cache & Content Data",
    "description": "ID of the CDN77 resource."
  }
]
JSON,
            (string) $response->getContent(),
        );
    }

    public function testEmptyList(): void
    {
        $entityManager = $this->entityManager();
        $customerId = CustomerUuid::fromString('90f09518-2f97-4f17-84e2-6b278e3e6725');
        $customer = CustomerFactory::new($entityManager)->create(customerUuid: $customerId);
        $entityManager->persist($customer);
        $token = PersonalTokenFactory::new($entityManager)->create($customer->getId());
        $entityManager->persist($token);

        FlushAndClear::do($entityManager);

        $response = $this->performHttpRequest(
            Request::METHOD_GET,
            '/v3/cdn/real-time-logs/fields',
            $this->prepareAuthHeaders(TokenGenerator::generate($token->getId(), 'validClapApiBearerTokenIs32*Char')),
        );

        self::assertHttpCode(Response::HTTP_OK, $response);
        self::assertJsonStringEqualsJsonString(
            <<<'JSON'
[]
JSON,
            (string) $response->getContent(),
        );
    }
}
