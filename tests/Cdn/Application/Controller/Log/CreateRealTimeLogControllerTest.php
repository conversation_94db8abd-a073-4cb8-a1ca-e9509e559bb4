<?php

declare(strict_types=1);

namespace Cdn77\Api\Tests\Cdn\Application\Controller\Log;

use Cdn77\Api\Cdn\Application\Controller\Log\CreateRealTimeLogController;
use Cdn77\Api\Cdn\Application\Payload\Forman\FieldSchema;
use Cdn77\Api\Cdn\Application\Payload\Log\CreateRealTimeLogSchema;
use Cdn77\Api\Cdn\Domain\Forman\FormanApi;
use Cdn77\Api\Cdn\Domain\Value\Log\LoggingFieldAvailability;
use Cdn77\Api\Cdn\Domain\Value\Log\LoggingFieldName;
use Cdn77\Api\Cdn\Domain\Value\Log\OutputFormat;
use Cdn77\Api\Cdn\Infrastructure\Forman\FakeFormanApi;
use Cdn77\Api\Core\Domain\Entity\Cdn\CdnId;
use Cdn77\Api\Core\Domain\Entity\Cdn\CdnLegacyId;
use Cdn77\Api\Core\Domain\Entity\Customer\CustomerUuid;
use Cdn77\Api\Core\Domain\Entity\RealTimeLog\RealTimeLog;
use Cdn77\Api\Core\Domain\Entity\RealTimeLog\RealTimeLogFieldId;
use Cdn77\Api\Core\Domain\Value\Label;
use Cdn77\Api\Core\Domain\Value\ObjectStorage\BucketName;
use Cdn77\Api\Tests\ORMFixtures\Api\PersonalTokenFactory;
use Cdn77\Api\Tests\ORMFixtures\Cdn\CdnFactory;
use Cdn77\Api\Tests\ORMFixtures\Cdn\RealTimeLogFieldCategoryFactory;
use Cdn77\Api\Tests\ORMFixtures\Cdn\RealTimeLogFieldFactory;
use Cdn77\Api\Tests\ORMFixtures\Customer\AccountSettingsFactory;
use Cdn77\Api\Tests\ORMFixtures\Customer\CustomerFactory;
use Cdn77\Api\Tests\ORMFixtures\ObjectStorage\RgwClusterFactory;
use Cdn77\Api\Tests\ORMFixtures\Origin\CustomerOriginSettingsFactory;
use Cdn77\Api\Tests\ORMFixtures\Origin\OriginFactory;
use Cdn77\Api\Tests\TestCase;
use Cdn77\Api\Tests\Utils\FlushAndClear;
use Cdn77\Api\Tests\Utils\TokenGenerator;
use Cdn77\Api\Tests\Utils\WithBrowser;
use Cdn77\Api\Tests\Utils\WithPostgres;
use PHPUnit\Framework\Attributes\CoversClass;
use PHPUnit\Framework\Attributes\Group;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

use function Psl\Json\encode;

#[CoversClass(CreateRealTimeLogController::class)]
#[Group('integration')]
final class CreateRealTimeLogControllerTest extends TestCase
{
    use WithPostgres;
    use WithBrowser;

    public function testCreateRealTimeLogForCustomer(): void
    {
        $entityManager = $this->entityManager();
        $customerId = CustomerUuid::fromString('90f09518-2f97-4f17-84e2-6b278e3e6725');
        $customer = CustomerFactory::new($entityManager)->create(customerUuid: $customerId);
        $this->createFields();
        $entityManager->persist($customer);
        $customerSettings = AccountSettingsFactory::new($entityManager)->create(
            $customer->getId(),
            realTimeLogEnabled: true,
        );
        $entityManager->persist($customerSettings);
        $cluster = RgwClusterFactory::new($entityManager)->create();
        $entityManager->persist($cluster);
        $bucketName = BucketName::fromString('my-bucket-name');
        $origin = OriginFactory::new($entityManager)
            ->createObjectStorage($customerId, $bucketName);
        $entityManager->persist($origin);
        $originId = $origin->getId();
        $customerOriginSettings = CustomerOriginSettingsFactory::new($entityManager)->create(
            $customerId,
            defaultRealTimeLogOriginId: $originId,
        );
        $entityManager->persist($customerOriginSettings);
        $cdnId = CdnId::fromInteger(123);
        $cdn = CdnFactory::new($entityManager)->create(
            $customer,
            $cdnId,
            CdnLegacyId::fromInteger(1),
            label: Label::fromString('Test CDN 123'),
        );
        $entityManager->persist($cdn);
        $otherCdnId = CdnId::fromInteger(12345);
        $otherCdn = CdnFactory::new($entityManager)->create(
            $customer,
            $otherCdnId,
            CdnLegacyId::fromInteger(2),
            label: Label::fromString('Test CDN 12345'),
        );
        $entityManager->persist($otherCdn);

        $token = PersonalTokenFactory::new($entityManager)->create($customer->getId());
        $entityManager->persist($token);

        FlushAndClear::do($entityManager);

        $response = $this->performHttpRequest(
            Request::METHOD_POST,
            '/v3/cdn/real-time-log/create/main',
            $this->prepareAuthHeaders(TokenGenerator::generate($token->getId(), 'validClapApiBearerTokenIs32*Char')),
            content: encode([
                CreateRealTimeLogSchema::FieldFormat => 'JSON',
                CreateRealTimeLogSchema::FieldFields => [
                    [
                        FieldSchema::FieldName => LoggingFieldName::Timestamp,
                        FieldSchema::FieldOutputName => 'ts',
                    ],
                    [
                        FieldSchema::FieldName => LoggingFieldName::StartTimeMs,
                        FieldSchema::FieldOutputName => 'startTimeMs',
                    ],
                    [
                        FieldSchema::FieldName => LoggingFieldName::ResourceID,
                        FieldSchema::FieldOutputName => 'resourceID',
                    ],
                ],
                CreateRealTimeLogSchema::FieldLogDirectory => 'main-log',
            ]),
        );

        self::assertHttpCode(Response::HTTP_CREATED, $response);

        $realTimeLogs = $entityManager->getRepository(RealTimeLog::class)
            ->findBy([RealTimeLog::FieldCustomerId => $customer->getNewId()->toString()]);

        self::assertCount(1, $realTimeLogs);
        $realTimeLog = $realTimeLogs[0];
        $realTimeLogId = $realTimeLog->id();
        self::assertEquals(OutputFormat::JSON, $realTimeLog->format());

        $originIdRaw = $originId->toString();
        $realTimeLogIdRaw = $realTimeLogId->toString();
        self::assertJsonStringEqualsJsonString(
            <<<JSON
{
  "id": "$realTimeLogIdRaw",
  "scope": "main",
  "format": "JSON",
  "path": {
    "root": "real-time-logs",
    "directory": "main-log"
  },
  "is_active": true,
  "object_storage": {
    "id": "$originIdRaw",
    "bucket_name": "my-bucket-name",
    "ttl_config": {}
  }
}
JSON,
            (string) $response->getContent(),
        );

        $formanApi = $this->getContainerService(FormanApi::class);
        self::assertInstanceOf(FakeFormanApi::class, $formanApi);
        $enabledCalls = $formanApi->enabledCalls();
        self::assertCount(1, $enabledCalls);
        $enabledCall = $enabledCalls->get(0);
        self::assertSame($realTimeLogId, $enabledCall->key);
    }

    private function createFields(): void
    {
        $entityManager = $this->entityManager();
        $timestampCategory = RealTimeLogFieldCategoryFactory::new($entityManager)
            ->create('Timestamp & Timing Data', 1);
        $entityManager->persist($timestampCategory);
        $cacheCategory = RealTimeLogFieldCategoryFactory::new($entityManager)
            ->create('Cache & Content Data', 2);
        $entityManager->persist($cacheCategory);

        $timestampField = RealTimeLogFieldFactory::new($entityManager)->create(
            RealTimeLogFieldId::fromString('be7c0570-7f48-4cf5-85d0-b4f8ed1928b0'),
            LoggingFieldName::fromString(LoggingFieldName::Timestamp),
            LoggingFieldAvailability::Public,
            $timestampCategory,
            1,
            'Human readable conversion of the startTimeMs field in the ISO 8601 format.',
        );
        $entityManager->persist($timestampField);
        $startTimeMsField = RealTimeLogFieldFactory::new($entityManager)->create(
            RealTimeLogFieldId::fromString('cffe28b5-2a57-456c-b126-6e9f4609d990'),
            LoggingFieldName::fromString(LoggingFieldName::StartTimeMs),
            LoggingFieldAvailability::Public,
            $timestampCategory,
            2,
            'Unix timestamp at which the edge received request from the client.',
        );
        $entityManager->persist($startTimeMsField);
        $resourceIdField = RealTimeLogFieldFactory::new($entityManager)->create(
            RealTimeLogFieldId::fromString('40bc4666-b2d7-4292-95da-82512febd67f'),
            LoggingFieldName::fromString(LoggingFieldName::ResourceID),
            LoggingFieldAvailability::Public,
            $cacheCategory,
            1,
            'ID of the CDN77 resource.',
        );
        $entityManager->persist($resourceIdField);
    }
}
