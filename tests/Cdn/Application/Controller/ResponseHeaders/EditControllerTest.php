<?php

declare(strict_types=1);

namespace Cdn77\Api\Tests\Cdn\Application\Controller\ResponseHeaders;

use Cdn77\Api\Cdn\Application\Controller\EditController;
use Cdn77\Api\Cdn\Application\Payload\EditCdnSchema;
use Cdn77\Api\Cdn\Application\Payload\ResponseHeaders\HeaderSchema;
use Cdn77\Api\Cdn\Application\Payload\ResponseHeaders\ResponseHeadersSchema;
use Cdn77\Api\Cdn\Domain\Exception\InvalidCustomHeader;
use Cdn77\Api\Cdn\Domain\Value\Parameters\CustomResponseHeaders;
use Cdn77\Api\Core\Domain\Entity\Cdn\Cdn;
use Cdn77\Api\Core\Domain\Entity\Cdn\CdnHttp;
use Cdn77\Api\Tests\ORMFixtures\Api\PersonalTokenFactory;
use Cdn77\Api\Tests\ORMFixtures\Cdn\CdnFactory;
use Cdn77\Api\Tests\ORMFixtures\Cdn\CdnHttpFactory;
use Cdn77\Api\Tests\ORMFixtures\Customer\CustomerFactory;
use Cdn77\Api\Tests\ORMFixtures\Origin\OriginFactory;
use Cdn77\Api\Tests\TestCase;
use Cdn77\Api\Tests\Utils\FlushAndClear;
use Cdn77\Api\Tests\Utils\TokenGenerator;
use Cdn77\Api\Tests\Utils\WithBrowser;
use Exception;
use Generator;
use PHPUnit\Framework\Attributes\CoversClass;
use PHPUnit\Framework\Attributes\DataProvider;
use PHPUnit\Framework\Attributes\Group;
use Psl\Json\Exception\EncodeException;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Webmozart\Assert\Assert;

use function Safe\json_encode;
use function sprintf;

#[CoversClass(EditController::class)]
#[Group('integration')]
final class EditControllerTest extends TestCase
{
    use WithBrowser;

    /** @throws Exception */
    public function testCustomResponseHeaders(): void
    {
        $cdn = $this->prepare();

        $expectedHeaders = [new HeaderSchema('Access-Control-Allow-Origin', 'test.com')];
        $response = $this->callApiAndGetResponse($cdn, [ResponseHeadersSchema::FieldHeaders => $expectedHeaders]);

        $this->assertNoContentResponse($response, $cdn, $expectedHeaders);
    }

    /** @throws Exception */
    public function testChangeCustomResponseHeaders(): void
    {
        $cdn = $this->prepare(CustomResponseHeaders::fromArray(['Access-Control-Allow-Origin' => 'test.cz']));

        $expectedHeaders = [
            new HeaderSchema('Access-Control-Allow-Origin', 'another.com'),
            new HeaderSchema('Other', 'some-value'),
        ];

        $response = $this->callApiAndGetResponse($cdn, [ResponseHeadersSchema::FieldHeaders => $expectedHeaders]);

        $this->assertNoContentResponse($response, $cdn, $expectedHeaders);
    }

    /** @throws Exception */
    public function testDisableCustomResponseHeadersWithEmptyArray(): void
    {
        $cdn = $this->prepare(CustomResponseHeaders::fromArray(['Access-Control-Allow-Origin' => 'test.cz']));

        $expectedHeaders = [];
        $response = $this->callApiAndGetResponse($cdn, [ResponseHeadersSchema::FieldHeaders => $expectedHeaders]);

        $this->assertNoContentResponse($response, $cdn, $expectedHeaders);
    }

    /** @throws Exception */
    public function testUnallowedNullInHeaders(): void
    {
        $cdn = $this->prepare(CustomResponseHeaders::fromArray(['Access-Control-Allow-Origin' => 'test.cz']));

        $response = $this->callApiAndGetResponse($cdn, [ResponseHeadersSchema::FieldHeaders => null]);

        self::assertHttpCode(Response::HTTP_UNPROCESSABLE_ENTITY, $response);
    }

    /** @throws Exception */
    #[DataProvider('providerFail')]
    public function testUnallowedCustomResponseHeaders(string $forbiddenHeaderName): void
    {
        $cdn = $this->prepare();

        $response = $this->callApiAndGetResponse($cdn, [
            ResponseHeadersSchema::FieldHeaders => [new HeaderSchema($forbiddenHeaderName, 'some-value')],
        ]);

        self::assertHttpCode(Response::HTTP_UNPROCESSABLE_ENTITY, $response);

        $cdnHttp = $this->entityManager()
            ->getRepository(CdnHttp::class)
            ->findOneBy(['cdnLegacyId' => $cdn->getLegacyId()->toInt()]);
        Assert::isInstanceOf($cdnHttp, CdnHttp::class);

        self::assertTrue($cdnHttp->customResponseHeaders()->areEmpty());
    }

    public static function providerFail(): Generator
    {
        $forbiddenHeaderNames = [
            'accept-ranges',
            'alt-svc',
            'connection',
            'content-encoding',
            'content-length',
            'content-range',
            'date',
            'trailer',
            'transfer-encoding',
            'upgrade',
            'server',
            'www-authenticate',
            'WWW-Authenticate', // to check case insensitive
        ];

        foreach ($forbiddenHeaderNames as $index => $forbiddenHeaderName) {
            yield sprintf('Forbidden header name %d', $index) => [$forbiddenHeaderName];
        }

        yield 'Invalid name with colon' => ['Access-Control-Allow-Origin:'];
        yield 'Forbidden pattern' => ['x-77-'];
    }

    /**
     * @param array<HeaderSchema> $expectedHeaders
     *
     * @throws InvalidCustomHeader
     */
    private function assertNoContentResponse(Response $response, Cdn $cdn, array $expectedHeaders): void
    {
        self::assertHttpCode(Response::HTTP_NO_CONTENT, $response);

        $cdnHttp = $this->entityManager()
            ->getRepository(CdnHttp::class)
            ->findOneBy(['cdnLegacyId' => $cdn->getLegacyId()->toInt()]);
        Assert::isInstanceOf($cdnHttp, CdnHttp::class);

        $expectedHeadersArray = [];
        foreach ($expectedHeaders as $header) {
            $expectedHeadersArray[$header->name] = $header->value;
        }

        self::assertSame(
            CustomResponseHeaders::fromArray($expectedHeadersArray)->toArray(),
            $cdnHttp->customResponseHeaders()->toArray(),
        );
    }

    /**
     * @param array<string, array<HeaderSchema>|null>|null $responseHeaders
     *
     * @throws Exception
     */
    private function callApiAndGetResponse(Cdn $cdn, array|null $responseHeaders): Response
    {
        $entityManager = $this->entityManager();
        $personalToken = PersonalTokenFactory::new($entityManager)->create($cdn->getOwner()->getId());
        $entityManager->persist($personalToken);
        $token = TokenGenerator::generate($personalToken->getId(), 'validClapApiBearerTokenIs32*Char');

        FlushAndClear::do($entityManager);

        return $this->performHttpRequest(
            Request::METHOD_PATCH,
            '/v3/cdn/' . $cdn->getId()->toString(),
            $this->prepareAuthHeaders($token),
            [],
            [],
            json_encode([EditCdnSchema::FieldResponseHeaders => $responseHeaders]),
        );
    }

    /** @throws EncodeException */
    private function prepare(CustomResponseHeaders|null $customResponseHeaders = null): Cdn
    {
        $entityManager = $this->entityManager();

        $customer = CustomerFactory::new($entityManager)->create();
        $entityManager->persist($customer);

        $origin = OriginFactory::new($entityManager)->create($customer->getNewId());
        $entityManager->persist($origin);

        $cdn = CdnFactory::new($entityManager)->create($customer);
        $entityManager->persist($cdn);

        $currentCdnHttp = CdnHttpFactory::new($entityManager)->create(
            $cdn->getLegacyId(),
            $origin->cdnOrigin(),
            customResponseHeaders: $customResponseHeaders,
        );
        $entityManager->persist($currentCdnHttp);

        FlushAndClear::do($entityManager);

        $cdnHttp = $this->entityManager()
            ->getRepository(CdnHttp::class)
            ->findOneBy(['cdnLegacyId' => $cdn->getLegacyId()->toInt()]);
        Assert::isInstanceOf($cdnHttp, CdnHttp::class);

        if ($customResponseHeaders === null) {
            self::assertTrue($cdnHttp->customResponseHeaders()->areEmpty());
        } else {
            self::assertSame($customResponseHeaders->toArray(), $cdnHttp->customResponseHeaders()->toArray());
        }

        return $cdn;
    }
}
