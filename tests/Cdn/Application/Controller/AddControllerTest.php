<?php

declare(strict_types=1);

namespace Cdn77\Api\Tests\Cdn\Application\Controller;

use Cdn77\Api\Cdn\Application\Controller\AddController;
use Cdn77\Api\Cdn\Application\Payload\CdnSchema;
use Cdn77\Api\Cdn\Application\Payload\NewCdnSchema;
use Cdn77\Api\Cdn\Domain\Value\Parameters\FollowRedirectType;
use Cdn77\Api\Cname\Application\Payload\CnameSchema;
use Cdn77\Api\Core\Domain\Entity\Api\PersonalToken;
use Cdn77\Api\Core\Domain\Entity\Cdn\Cdn;
use Cdn77\Api\Core\Domain\Entity\Cdn\CdnHttp;
use Cdn77\Api\Core\Domain\Entity\Cname\Cname;
use Cdn77\Api\Core\Domain\Entity\Customer\Customer;
use Cdn77\Api\Core\Domain\Entity\Customer\CustomerId;
use Cdn77\Api\Core\Domain\Entity\Origin\Origin;
use Cdn77\Api\Tests\ORMFixtures\Api\PersonalTokenFactory;
use Cdn77\Api\Tests\ORMFixtures\Customer\AccountFlagsFactory;
use Cdn77\Api\Tests\ORMFixtures\Customer\AccountSettingsFactory;
use Cdn77\Api\Tests\ORMFixtures\Customer\CustomerFactory;
use Cdn77\Api\Tests\ORMFixtures\Customer\CustomerPaymentSettingsFactory;
use Cdn77\Api\Tests\ORMFixtures\CustomerCdnSettings\CustomerCdnSettingsFactory;
use Cdn77\Api\Tests\ORMFixtures\Origin\OriginFactory;
use Cdn77\Api\Tests\TestCase;
use Cdn77\Api\Tests\Utils\FlushAndClear;
use Cdn77\Api\Tests\Utils\TokenGenerator;
use Cdn77\Api\Tests\Utils\WithBrowser;
use PHPUnit\Framework\Attributes\CoversClass;
use PHPUnit\Framework\Attributes\Group;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Webmozart\Assert\Assert;

use function Psl\Json\decode;
use function Psl\Json\encode;
use function sprintf;

#[CoversClass(AddController::class)]
#[Group('integration')]
final class AddControllerTest extends TestCase
{
    use WithBrowser;

    public function testAddCdn(): void
    {
        $entityManager = $this->entityManager();

        $customer = CustomerFactory::new($entityManager)->create();
        $entityManager->persist($customer);

        $accountFlags = AccountFlagsFactory::new($entityManager)->create($customer->getId());
        $entityManager->persist($accountFlags);

        $accountSettings = AccountSettingsFactory::new($entityManager)->create($customer->getId());
        $entityManager->persist($accountSettings);

        $customerCdnSettings = CustomerCdnSettingsFactory::new($entityManager)->create($customer->getId());
        $entityManager->persist($customerCdnSettings);

        $customerPaymentSettings = CustomerPaymentSettingsFactory::new($entityManager)->create($customer->getId());
        $entityManager->persist($customerPaymentSettings);

        $token = PersonalTokenFactory::new($entityManager)->create($customer->getId());
        $entityManager->persist($token);

        $origin = OriginFactory::new($entityManager)->create($customer->getNewId());
        $entityManager->persist($origin);

        FlushAndClear::do($entityManager);

        $label = 'Test CDN';
        $cnamesRaw = [
            ' trim.this.space',
            'trim.this.too  ',
            'foo.bar.baz.com',
        ];
        $originId = $origin->getId()->toString();
        $token = TokenGenerator::generate($token->getId(), 'validClapApiBearerTokenIs32*Char');
        $response = $this->performHttpRequest(
            Request::METHOD_POST,
            '/v3/cdn',
            $this->prepareAuthHeaders($token),
            [],
            [],
            encode([
                NewCdnSchema::FieldLabel => $label,
                NewCdnSchema::FieldOriginId => $originId,
                NewCdnSchema::FieldCnames => $cnamesRaw,
            ]),
        );

        self::assertHttpCode(Response::HTTP_CREATED, $response);

        $cdn = decode((string) $response->getContent());
        self::assertIsArray($cdn);
        self::assertArrayHasKey(CdnSchema::FieldId, $cdn);
        self::assertArrayHasKey(CdnSchema::FieldLabel, $cdn);
        self::assertArrayHasKey(CdnSchema::FieldOriginId, $cdn);
        self::assertArrayHasKey(CdnSchema::FieldCnames, $cdn);
        self::assertIsArray($cdn[CdnSchema::FieldCnames]);
        self::assertCount(3, $cdn[CdnSchema::FieldCnames]);
        self::assertIsArray($cdn[CdnSchema::FieldCnames][0]);
        self::assertArrayHasKey(CnameSchema::FieldCname, $cdn[CdnSchema::FieldCnames][0]);
        self::assertSame('trim.this.space', $cdn[CdnSchema::FieldCnames][0][CnameSchema::FieldCname]);
        self::assertIsArray($cdn[CdnSchema::FieldCnames][1]);
        self::assertArrayHasKey(CnameSchema::FieldCname, $cdn[CdnSchema::FieldCnames][0]);
        self::assertSame('trim.this.too', $cdn[CdnSchema::FieldCnames][1][CnameSchema::FieldCname]);
        self::assertIsArray($cdn[CdnSchema::FieldCnames][2]);
        self::assertArrayHasKey(CnameSchema::FieldCname, $cdn[CdnSchema::FieldCnames][0]);
        self::assertSame('foo.bar.baz.com', $cdn[CdnSchema::FieldCnames][2][CnameSchema::FieldCname]);
        self::assertSame($label, $cdn[CdnSchema::FieldLabel]);
        self::assertSame($originId, $cdn[CdnSchema::FieldOriginId]);

        $cdnId = $cdn[CdnSchema::FieldId];
        self::assertIsInt($cdnId);
        $cnames = $entityManager->getRepository(Cname::class)->findBy(['cdnId' => $cdn[CdnSchema::FieldId]]);
        self::assertCount(3, $cnames);
        $cdn = $entityManager->getRepository(Cdn::class)->findOneBy([Cdn::FieldId => $cdnId]);
        self::assertNotNull($cdn);
        self::assertSame($label, $cdn->label()->value);

        $cdnHttp = $entityManager
            ->getRepository(CdnHttp::class)
            ->findOneBy([CdnHttp::FieldCdnLegacyId => $cdn->getLegacyId()->toInt()]);
        self::assertNotNull($cdnHttp);

        $followRedirect = $cdnHttp->followRedirect();
        self::assertSame(FollowRedirectType::None, $followRedirect->type);
        self::assertSame([], $followRedirect->codes());

        self::assertTrue($cdnHttp->customOriginHeaders()->areEmpty());
        self::assertTrue($cdnHttp->customResponseHeaders()->areEmpty());
    }

    public function testAddMojoCdn(): void
    {
        $entityManager = $this->entityManager();

        $customer = CustomerFactory::new($entityManager)->create(
            CustomerId::mojo(),
        );
        $entityManager->persist($customer);

        $accountFlags = AccountFlagsFactory::new($entityManager)->create($customer->getId());
        $entityManager->persist($accountFlags);

        $accountSettings = AccountSettingsFactory::new($entityManager)->create($customer->getId());
        $entityManager->persist($accountSettings);

        $customerCdnSettings = CustomerCdnSettingsFactory::new($entityManager)->create($customer->getId());
        $entityManager->persist($customerCdnSettings);

        $customerPaymentSettings = CustomerPaymentSettingsFactory::new($entityManager)->create($customer->getId());
        $entityManager->persist($customerPaymentSettings);

        $token = PersonalTokenFactory::new($entityManager)->create($customer->getId());
        $entityManager->persist($token);

        $origin = OriginFactory::new($entityManager)->create($customer->getNewId());
        $entityManager->persist($origin);

        FlushAndClear::do($entityManager);

        $label = 'Test CDN';

        $originId = $origin->getId()->toString();
        $token = TokenGenerator::generate($token->getId(), 'validClapApiBearerTokenIs32*Char');
        $response = $this->performHttpRequest(
            Request::METHOD_POST,
            '/v3/cdn',
            $this->prepareAuthHeaders($token),
            [],
            [],
            encode([
                NewCdnSchema::FieldLabel => $label,
                NewCdnSchema::FieldOriginId => $originId,
            ]),
        );

        self::assertHttpCode(Response::HTTP_CREATED, $response);

        $responseData = decode((string) $response->getContent());
        self::assertIsArray($responseData);
        self::assertArrayHasKey(CdnSchema::FieldId, $responseData);
        self::assertArrayHasKey(CdnSchema::FieldLabel, $responseData);
        self::assertArrayHasKey(CdnSchema::FieldOriginId, $responseData);
        self::assertArrayHasKey(CdnSchema::FieldCnames, $responseData);
        self::assertIsArray($responseData[CdnSchema::FieldCnames]);
        self::assertEmpty($responseData[CdnSchema::FieldCnames]);
        self::assertSame($label, $responseData[CdnSchema::FieldLabel]);
        self::assertSame($originId, $responseData[CdnSchema::FieldOriginId]);

        $cdnId = $responseData[CdnSchema::FieldId];
        self::assertIsInt($cdnId);

        $cdn = $entityManager->getRepository(Cdn::class)->findOneBy([Cdn::FieldId => $cdnId]);
        self::assertNotNull($cdn);
        self::assertSame($label, $cdn->label()->value);

        $cdnHttp = $entityManager
            ->getRepository(CdnHttp::class)
            ->findOneBy([CdnHttp::FieldCdnLegacyId => $cdn->getLegacyId()->toInt()]);
        self::assertNotNull($cdnHttp);

        $followRedirect = $cdnHttp->followRedirect();

        self::assertSame(FollowRedirectType::List, $followRedirect->type);
        self::assertSame([301, 302], $followRedirect->codes());
    }

    public function testAddCraCdn(): void
    {
        [
            'test_customer_cra' => $customer,
            'test_customer_api_personal_token' => $personalToken,
            'test_origin' => $origin,
        ] = $this->loadFixtures();

        Assert::isInstanceOf($customer, Customer::class);
        Assert::isInstanceOf($personalToken, PersonalToken::class);
        Assert::isInstanceOf($origin, Origin::class);

        $originId = $origin->getId();
        $label = 'CRA Cdn';
        $token = TokenGenerator::generate($personalToken->getId(), 'validClapApiBearerTokenIs32*Char');
        $response = $this->performHttpRequest(
            Request::METHOD_POST,
            '/v3/cdn',
            $this->prepareAuthHeaders($token),
            [],
            [],
            <<<JSON
{
    "label": "$label",
    "origin_id": "$originId"
}
JSON,
        );

        self::assertHttpCode(Response::HTTP_CREATED, $response);
        self::assertIsString($response->getContent());

        $responseData = decode($response->getContent());
        self::assertIsArray($responseData);
        self::assertArrayHasKey(CdnSchema::FieldId, $responseData);

        $cdnId = $responseData[CdnSchema::FieldId];
        self::assertIsInt($cdnId);

        $expectedCname = sprintf('%d.ssl.cdn.cra.cz', $cdnId);
        self::assertArrayHasKey(CdnSchema::FieldCnames, $responseData);
        self::assertIsArray($responseData[CdnSchema::FieldCnames]);
        self::assertIsArray($responseData[CdnSchema::FieldCnames][0]);
        self::assertArrayHasKey(CnameSchema::FieldCname, $responseData[CdnSchema::FieldCnames][0]);
        self::assertSame($expectedCname, $responseData[CdnSchema::FieldCnames][0]['cname']);

        $entityManager = $this->entityManager();

        $cdn = $entityManager->getRepository(Cdn::class)->findOneBy(['id' => $cdnId]);
        Assert::isInstanceOf($cdn, Cdn::class);

        self::assertSame($label, $cdn->label()->value);
        self::assertSame($cdnId, $cdn->getId()->toInt());

        $cnames = $entityManager->getRepository(Cname::class)->findBy(['cdnId' => $cdnId]);
        self::assertCount(1, $cnames);
        self::assertSame(sprintf('%s.ssl.cdn.cra.cz', $cdnId), $cnames[0]->getCname());
    }
}
