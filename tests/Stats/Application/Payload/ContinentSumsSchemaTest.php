<?php

declare(strict_types=1);

namespace Cdn77\Api\Tests\Stats\Application\Payload;

use Brick\Math\BigDecimal;
use Brick\Math\Exception\MathException;
use Cdn77\Api\Core\Domain\Continent\Value\ContinentCode;
use Cdn77\Api\Stats\Application\Payload\ContinentSumsSchema;
use Cdn77\Api\Stats\Domain\Dto\ContinentSums;
use Cdn77\Api\Stats\Domain\Dto\DatacenterSums;
use Cdn77\Api\Stats\Domain\Value\Sum;
use Cdn77\Api\Tests\TestCase;
use Cdn77\Api\Tests\Utils\WithSerializer;
use JMS\Serializer\Exception\RuntimeException;
use PHPUnit\Framework\Attributes\CoversClass;
use PHPUnit\Framework\Attributes\Group;

#[CoversClass(ContinentSumsSchema::class)]
#[Group('integration')]
final class ContinentSumsSchemaTest extends TestCase
{
    use WithSerializer;

    /**
     * @throws MathException
     * @throws RuntimeException
     */
    public function testSerialize(): void
    {
        $euDatacenterSums = new DatacenterSums([
            'london' => new Sum(BigDecimal::of(12345)),
            'prague' => new Sum(BigDecimal::of(12345)),
        ]);
        $usDatacenterSums = new DatacenterSums([
            'miami' => new Sum(BigDecimal::of(12345)),
            'dallas' => new Sum(BigDecimal::of(12345)),
        ]);

        $continentSums = new ContinentSums(
            [
                ContinentCode::Europe->value => $euDatacenterSums,
                ContinentCode::NorthAmerica->value => $usDatacenterSums,
            ],
        );
        $continentSumsSchema = ContinentSumsSchema::fromContinentSums($continentSums);

        $schema = $this->getSerializer()->serialize($continentSumsSchema, 'json');
        $expectedSchema = <<<'JSON'
            {
                "EU": {
                    "london": {
                        "sum": 12345
                    },
                    "prague": {
                        "sum": 12345
                    }
                },
                "NA": {
                    "dallas": {
                        "sum": 12345
                    },
                    "miami": {
                        "sum": 12345
                    }
                 }
             }
            JSON;

        self::assertJsonStringEqualsJsonString($expectedSchema, $schema);
    }
}
