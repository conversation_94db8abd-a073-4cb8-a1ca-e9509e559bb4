<?php

declare(strict_types=1);

namespace Cdn77\Api\Tests\Stats\Domain\Validation;

use Cdn77\Api\Core\Domain\Entity\Customer\Customer;
use Cdn77\Api\Core\Domain\Entity\Customer\CustomerId;
use Cdn77\Api\Core\Domain\Repository\CustomerRepository;
use Cdn77\Api\Core\Domain\Value\Customer\UserRole;
use Cdn77\Api\Stats\Domain\Exception\StatsAccessNotAllowed;
use Cdn77\Api\Stats\Domain\Validation\CacheStatsAccessValidator;
use Cdn77\Api\Tests\TestCase;
use Cdn77\TestUtils\Stub;
use Mockery;
use PHPUnit\Framework\Attributes\CoversClass;
use PHPUnit\Framework\Attributes\DataProvider;
use PHPUnit\Framework\Attributes\Group;

#[Group('unit')]
#[CoversClass(CacheStatsAccessValidator::class)]
final class CacheStatsAccessValidatorTest extends TestCase
{
    #[DataProvider('getFieldTypeAndCustomer')]
    public function testValidate(CustomerId $customerId, UserRole $userRole, bool $expectException = false): void
    {
        $customer = Stub::create(Customer::class, [
            Customer::FieldId => $customerId->toInt(),
            Customer::FieldRole => $userRole,
        ]);

        $customerRepository = Mockery::mock(CustomerRepository::class);

        $customerRepository->shouldReceive('get')
            ->once()
            ->with($customerId)
            ->andReturn($customer);

        if ($expectException) {
            $this->expectException(StatsAccessNotAllowed::class);
        }

        $validator = new CacheStatsAccessValidator($customerRepository);
        $validator->validate($customerId);
    }

    /** @return iterable<array<CustomerId|UserRole|bool>> */
    public static function getFieldTypeAndCustomer(): iterable
    {
        yield 'non cra customer accessing cache-stats denied' => [
            CustomerId::fromInteger(1),
            UserRole::User,
            true,
        ];

        yield 'cra accessing cache-stats allowed' => [CustomerId::getCRA(), UserRole::User, false];
        yield 'superadmin allowed' => [CustomerId::fromInteger(2), UserRole::SuperAdmin, false];
    }
}
