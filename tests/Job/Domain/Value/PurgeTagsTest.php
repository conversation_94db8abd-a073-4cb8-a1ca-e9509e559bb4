<?php

declare(strict_types=1);

namespace Cdn77\Api\Tests\Job\Domain\Value;

use Cdn77\Api\Job\Domain\Value\PurgeTags;
use Cdn77\Api\Tests\TestCase;
use Generator;
use PHPUnit\Framework\Attributes\CoversClass;
use PHPUnit\Framework\Attributes\DataProvider;
use PHPUnit\Framework\Attributes\Group;

#[Group('unit')]
#[CoversClass(PurgeTags::class)]
final class PurgeTagsTest extends TestCase
{
    #[DataProvider('providerTag')]
    public function testMatchTag(string $tag): void
    {
        self::assertMatchesRegularExpression(PurgeTags::PatternAllowedChars, $tag);
    }

    #[DataProvider('providerInvalidTag')]
    public function testInvalidTag(string $tag): void
    {
        self::assertDoesNotMatchRegularExpression(PurgeTags::PatternAllowedChars, $tag);
    }

    /** @return Generator<string, array{string}> */
    public static function providerTag(): Generator
    {
        yield 'default' => ['videos'];
        yield 'alphanumeric' => ['videos123'];
        yield 'underscore' => ['misc_123'];
        yield 'dash' => ['Stuff-123'];
        yield 'dot' => ['CONTENT.123'];
        yield 'colon' => ['images:123'];
    }

    /** @return Generator<string, array{string}> */
    public static function providerInvalidTag(): Generator
    {
        yield 'empty' => [''];
        yield 'space' => ['videos 123'];
        yield 'slash' => ['Videozz/123'];
        yield 'backslash' => ['videos\123'];
        yield 'question mark' => ['videos?123'];
        yield 'exclamation mark' => ['VIDEOS!123'];
        yield 'ampersand' => ['videos&123'];
        yield 'percent' => ['videos%123'];
        yield 'plus' => ['videos+123'];
    }
}
