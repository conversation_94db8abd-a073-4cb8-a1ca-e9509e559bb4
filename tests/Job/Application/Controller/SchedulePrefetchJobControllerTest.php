<?php

declare(strict_types=1);

namespace Cdn77\Api\Tests\Job\Application\Controller;

use Cdn77\Api\Core\Application\Payload\ErrorsSchema;
use Cdn77\Api\Core\Domain\Entity\Cname\Cname;
use Cdn77\Api\Core\Domain\Entity\Customer\Customer;
use Cdn77\Api\Core\Domain\Entity\Customer\CustomerCdnSettings;
use Cdn77\Api\Core\Domain\Value\PrefetchMode;
use Cdn77\Api\Job\Application\Controller\SchedulePrefetchJobController;
use Cdn77\Api\Job\Application\Payload\PrefetchJobSchema;
use Cdn77\Api\Tests\ORMFixtures\CustomerCdnSettings\CustomerCdnSettingsFactory;
use Cdn77\Api\Tests\TestCase;
use Cdn77\Api\Tests\Utils\FlushAndClear;
use Cdn77\Api\Tests\Utils\TokenGenerator;
use Cdn77\Api\Tests\Utils\WithBrowser;
use Cdn77\TestUtils\Stub;
use PHPUnit\Framework\Attributes\CoversClass;
use PHPUnit\Framework\Attributes\Group;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Webmozart\Assert\Assert;

use function Psl\Json\decode;
use function Psl\Json\encode;

#[CoversClass(SchedulePrefetchJobController::class)]
#[Group('integration')]
final class SchedulePrefetchJobControllerTest extends TestCase
{
    use WithBrowser;

    public function testPrefetchNotAuthenticated(): void
    {
        $response = $this->performHttpRequest(Request::METHOD_POST, '/v3/cdn/123456789/job/prefetch');

        self::assertHttpCode(Response::HTTP_UNAUTHORIZED, $response);
        self::assertJsonResponse(
            ['reason' => 'Authentication required.'],
            $response,
        );
    }

    public function testNonExistentCdn(): void
    {
        ['testing_customer' => $customer] =
            $this->loadFixturesFromFile(__DIR__ . '/fixtures/nonExistentCdn.yaml');

        Assert::isInstanceOf($customer, Customer::class);

        $entityManager = $this->entityManager();
        $customerCdnSettings = CustomerCdnSettingsFactory::new($entityManager)->create($customer->getId());
        $customerCdnSettings = Stub::extend(
            $customerCdnSettings,
            [CustomerCdnSettings::FieldPrefetchMode => PrefetchMode::Disabled],
        );
        $entityManager->persist($customerCdnSettings);
        FlushAndClear::do($entityManager);

        $token = TokenGenerator::generateApplication($customer->getNewId());
        $requestData = encode([
            'paths' => ['img1.jpg', 'img2.jpg'],
        ]);

        $response = $this->performHttpRequest(
            Request::METHOD_POST,
            '/v3/cdn/123456789/job/prefetch',
            $this->prepareAuthHeaders($token),
            [],
            [],
            $requestData,
        );

        self::assertHttpCode(Response::HTTP_NOT_FOUND, $response);
        self::assertJsonResponse(
            [
                ErrorsSchema::FieldErrors =>
                    ['CDN Resource with id "123456789" could not be found.'],
            ],
            $response,
        );
    }

    public function testPrefetchCdn(): void
    {
        ['testing_customer' => $customer] =
            $this->loadFixturesFromFile(__DIR__ . '/fixtures/existingCdn.yaml');
        Assert::isInstanceOf($customer, Customer::class);
        $entityManager = $this->entityManager();
        $customerCdnSettings = CustomerCdnSettingsFactory::new($entityManager)->create($customer->getId());
        $customerCdnSettings = Stub::extend(
            $customerCdnSettings,
            [CustomerCdnSettings::FieldPrefetchMode => PrefetchMode::Disabled],
        );
        $entityManager->persist($customerCdnSettings);
        FlushAndClear::do($entityManager);

        $token = TokenGenerator::generateApplication($customer->getNewId());
        $requestData = encode([
            'paths' => ['img1.jpg', 'img2.jpg', '/sub/img.png\*'],
            'upstream_host' => 'cnameA.cdn77.com',
        ]);

        $response = $this->performHttpRequest(
            Request::METHOD_POST,
            '/v3/cdn/123456789/job/prefetch',
            $this->prepareAuthHeaders($token),
            [],
            [],
            $requestData,
        );

        self::assertHttpCode(Response::HTTP_ACCEPTED, $response);

        self::assertIsString($response->getContent());
        $payload = decode($response->getContent());

        Assert::isArray($payload);

        self::assertCount(8, $payload);
        self::assertSame(['img1.jpg', 'img2.jpg', '/sub/img.png\*'], $payload['paths']);
    }

    public function testPrefetchCdnForHiddenPrefetchMode(): void
    {
        ['testing_customer' => $customer] =
            $this->loadFixturesFromFile(__DIR__ . '/fixtures/existingCdn.yaml');
        Assert::isInstanceOf($customer, Customer::class);
        $entityManager = $this->entityManager();
        $customerCdnSettings = CustomerCdnSettingsFactory::new($entityManager)->create($customer->getId());
        $customerCdnSettings = Stub::extend(
            $customerCdnSettings,
            [CustomerCdnSettings::FieldPrefetchMode => PrefetchMode::Hidden],
        );
        $entityManager->persist($customerCdnSettings);
        FlushAndClear::do($entityManager);

        $token = TokenGenerator::generateApplication($customer->getNewId());
        $requestData = encode([
            'paths' => ['img1.jpg', 'img2.jpg', '/sub/img.png\*'],
            'upstream_host' => 'cnameA.cdn77.com',
        ]);

        $response = $this->performHttpRequest(
            Request::METHOD_POST,
            '/v3/cdn/123456789/job/prefetch',
            $this->prepareAuthHeaders($token),
            [],
            [],
            $requestData,
        );

        self::assertHttpCode(Response::HTTP_NOT_FOUND, $response);
        self::assertJsonResponse(
            [
                ErrorsSchema::FieldErrors =>
                    ['Requested API endpoint doesn\'t exist.'],
            ],
            $response,
        );
    }

    public function testPrefetchCdnWithValidUpstreamHost(): void
    {
        [
            'testing_customer' => $customer,
            'testing_cname_b' => $cname,
        ] =
            $this->loadFixturesFromFile(__DIR__ . '/fixtures/existingCdn.yaml');

        Assert::isInstanceOf($customer, Customer::class);
        Assert::isInstanceOf($cname, Cname::class);

        $entityManager = $this->entityManager();
        $customerCdnSettings = CustomerCdnSettingsFactory::new($entityManager)->create($customer->getId());
        $customerCdnSettings = Stub::extend(
            $customerCdnSettings,
            [CustomerCdnSettings::FieldPrefetchMode => PrefetchMode::Disabled],
        );
        $entityManager->persist($customerCdnSettings);
        FlushAndClear::do($entityManager);

        $requestData = encode([
            'paths' => ['img1.jpg', 'img2.jpg', '*'],
            'upstream_host' => $cname->getCname(),
        ]);
        $token = TokenGenerator::generateApplication($customer->getNewId());
        $response = $this->performHttpRequest(
            Request::METHOD_POST,
            '/v3/cdn/123456789/job/prefetch',
            $this->prepareAuthHeaders($token),
            [],
            [],
            $requestData,
        );

        self::assertHttpCode(Response::HTTP_ACCEPTED, $response);

        self::assertIsString($response->getContent());
        $payload = decode($response->getContent());

        Assert::isArray($payload);

        self::assertCount(9, $payload);
        self::assertSame(['img1.jpg', 'img2.jpg'], $payload['paths']);
        self::assertSame(['*'], $payload[PrefetchJobSchema::FieldIgnoredPaths]);
    }

    public function testPrefetchCdnWithInvalidUpstreamHost(): void
    {
        ['testing_customer' => $customer] =
            $this->loadFixturesFromFile(__DIR__ . '/fixtures/existingCdn.yaml');

        Assert::isInstanceOf($customer, Customer::class);

        $entityManager = $this->entityManager();
        $customerCdnSettings = CustomerCdnSettingsFactory::new($entityManager)->create($customer->getId());
        $customerCdnSettings = Stub::extend(
            $customerCdnSettings,
            [CustomerCdnSettings::FieldPrefetchMode => PrefetchMode::Disabled],
        );
        $entityManager->persist($customerCdnSettings);
        FlushAndClear::do($entityManager);

        $requestData = encode([
            'paths' => ['img1.jpg', 'img2.jpg', '/sub/img.png*'],
            'upstream_host' => 'invalidcname.com',
        ]);
        $token = TokenGenerator::generateApplication($customer->getNewId());
        $response = $this->performHttpRequest(
            Request::METHOD_POST,
            '/v3/cdn/123456789/job/prefetch',
            $this->prepareAuthHeaders($token),
            [],
            [],
            $requestData,
        );

        self::assertHttpCode(Response::HTTP_UNPROCESSABLE_ENTITY, $response);
        self::assertJsonResponse(
            [
                ErrorsSchema::FieldErrors =>
                    ['Upstream host "invalidcname.com" must be the same as one of the CDN resource CNAMEs or CDN URL.'],
            ],
            $response,
        );
    }
}
