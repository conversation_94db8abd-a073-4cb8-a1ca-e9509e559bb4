<?php

declare(strict_types=1);

namespace Cdn77\Api\Tests\Plan\Domain\Dto;

use Cdn77\Api\Core\Domain\Entity\Plan\Plan;
use Cdn77\Api\Plan\Domain\Dto\MonthlyPeriod;
use Cdn77\Api\Tests\TestCase;
use Cdn77\TestUtils\Stub;
use DateTimeImmutable;
use Generator;
use PHPUnit\Framework\Attributes\CoversClass;
use PHPUnit\Framework\Attributes\DataProvider;
use PHPUnit\Framework\Attributes\Group;

#[Group('unit')]
#[CoversClass(MonthlyPeriod::class)]
final class MonthlyPeriodTest extends TestCase
{
    #[DataProvider('providerTestFromPlan')]
    public function testFromPlan(Plan $monthlyPlan, MonthlyPeriod $expectedNextServicePeriod): void
    {
        $now = new DateTimeImmutable('2022-11-18 12:02:02');
        $monthlyPeriod = MonthlyPeriod::fromPlan($monthlyPlan, $now);

        self::assertDateEquals($expectedNextServicePeriod->startOfMonthlyPeriod, $monthlyPeriod->startOfMonthlyPeriod);
        self::assertDateEquals($expectedNextServicePeriod->endOfMonthlyPeriod, $monthlyPeriod->endOfMonthlyPeriod);
    }

    /** @return Generator<string, array{Plan, MonthlyPeriod}> */
    public static function providerTestFromPlan(): Generator
    {
        yield 'MP created during the current month' => [
            Stub::create(Plan::class, [
                'validFrom' => new DateTimeImmutable('2022-11-11 00:00:00'),
            ]),
            new MonthlyPeriod(
                new DateTimeImmutable('2022-11-11 00:00:00'),
                new DateTimeImmutable('2022-11-30 23:59:59'),
            ),
        ];

        yield 'MP created for the following month' => [
            Stub::create(Plan::class, [
                'validFrom' => new DateTimeImmutable('2022-12-01 00:00:00'),
            ]),
            new MonthlyPeriod(
                new DateTimeImmutable('2022-11-01 00:00:00'),
                new DateTimeImmutable('2022-11-30 23:59:59'),
            ),
        ];

        yield 'MP created in the past' => [
            Stub::create(Plan::class, [
                'validFrom' => new DateTimeImmutable('2022-09-01 00:00:00'),
            ]),
            new MonthlyPeriod(
                new DateTimeImmutable('2022-11-01 00:00:00'),
                new DateTimeImmutable('2022-11-30 23:59:59'),
            ),
        ];
    }
}
