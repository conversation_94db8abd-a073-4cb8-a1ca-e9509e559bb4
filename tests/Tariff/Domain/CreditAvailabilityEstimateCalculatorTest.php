<?php

declare(strict_types=1);

namespace Cdn77\Api\Tests\Tariff\Domain;

use Brick\Money\Money;
use Cdn77\Api\CoreLibrary\Money\CurrencyCode;
use Cdn77\Api\Tariff\Domain\CreditAvailabilityEstimateCalculator;
use Cdn77\Api\Tests\TestCase;
use DateTimeImmutable;
use PHPUnit\Framework\Attributes\CoversClass;
use PHPUnit\Framework\Attributes\DataProvider;
use PHPUnit\Framework\Attributes\Group;

#[Group('unit')]
#[CoversClass(CreditAvailabilityEstimateCalculator::class)]
final class CreditAvailabilityEstimateCalculatorTest extends TestCase
{
    #[DataProvider('providerCreditAndDateTime')]
    public function testAvailability(
        Money $creditMoney,
        Money $creditSpentIn24Hours,
        DateTimeImmutable $creditExpiresAt,
        DateTimeImmutable $now,
        DateTimeImmutable $shouldLastUntil,
    ): void {
        $willLastUntil = CreditAvailabilityEstimateCalculator::calculate(
            $creditMoney,
            $creditSpentIn24Hours,
            $creditExpiresAt,
            $now,
        );

        self::assertEquals($shouldLastUntil, $willLastUntil);
    }

    /** @return iterable<array{Money, Money, DateTimeImmutable, DateTimeImmutable, DateTimeImmutable}> */
    public static function providerCreditAndDateTime(): iterable
    {
        $usdCurrency = CurrencyCode::USD->toCurrency();

        yield 'test credit expired' => [
            Money::of(10, $usdCurrency),
            Money::zero($usdCurrency),
            new DateTimeImmutable('2020-05-01 00:00:00'),
            new DateTimeImmutable('2020-05-22 14:23:12'),
            new DateTimeImmutable('2020-05-01 00:00:00'),
        ];

        yield 'test will last for 2 days' => [
            Money::of(100, $usdCurrency),
            Money::of(48.50, $usdCurrency),
            new DateTimeImmutable('2021-05-01 00:00:00'),
            new DateTimeImmutable('2020-05-22 14:50:11'),
            new DateTimeImmutable('2020-05-24 14:50:11'),
        ];

        yield 'no credit used in 24 hours will last till the expiration +1 day in leap year' => [
            Money::of(100, $usdCurrency),
            Money::zero($usdCurrency),
            new DateTimeImmutable('2021-05-01 00:00:00'),
            new DateTimeImmutable('2020-05-22 14:50:11'),
            new DateTimeImmutable('2021-05-02 00:00:00'),
        ];

        yield 'no credit used in 24 hours will last till the expiration day' => [
            Money::of(100, $usdCurrency),
            Money::zero($usdCurrency),
            new DateTimeImmutable('2021-06-01 00:00:00'),
            new DateTimeImmutable('2021-05-22 14:50:11'),
            new DateTimeImmutable('2021-06-01 00:00:00'),
        ];
    }
}
