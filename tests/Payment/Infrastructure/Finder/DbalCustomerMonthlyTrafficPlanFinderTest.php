<?php

declare(strict_types=1);

namespace Cdn77\Api\Tests\Payment\Infrastructure\Finder;

use Cdn77\Api\Core\Domain\Entity\Customer\CustomerId;
use Cdn77\Api\Core\Domain\Entity\MonthlyTrafficPlan\CustomerMonthlyTrafficPlanId;
use Cdn77\Api\Payment\Infrastructure\Finder\DbalCustomerMonthlyTrafficPlanFinder;
use Cdn77\Api\Tests\ORMFixtures\Customer\CustomerFactory;
use Cdn77\Api\Tests\ORMFixtures\Customer\CustomerPaymentSettingsFactory;
use Cdn77\Api\Tests\ORMFixtures\CustomerMonthlyTrafficPlan\CustomerMonthlyTrafficPlanFactory;
use Cdn77\Api\Tests\TestCase;
use Cdn77\Api\Tests\Utils\FlushAndClear;
use Cdn77\Api\Tests\Utils\WithKernel;
use Cdn77\Api\Tests\Utils\WithPostgres;
use DateTimeImmutable;
use PHPUnit\Framework\Attributes\CoversClass;
use PHPUnit\Framework\Attributes\Group;

#[Group('integration')]
#[CoversClass(DbalCustomerMonthlyTrafficPlanFinder::class)]
final class DbalCustomerMonthlyTrafficPlanFinderTest extends TestCase
{
    use WithKernel;
    use WithPostgres;

    public function testFindAllForRechargeNow(): void
    {
        $now = new DateTimeImmutable('2025-03-31 13:59:20');

        $entityManager = $this->entityManager();

        $customerWithRenewedId = CustomerId::fromInteger(12345);
        $customerWithRenewed = CustomerFactory::new($entityManager)->create($customerWithRenewedId);
        $entityManager->persist($customerWithRenewed);

        $customerWithRenewedSettings = CustomerPaymentSettingsFactory::new($entityManager)
            ->create($customerWithRenewedId);
        $customerWithRenewedSettings->enableMonthlyPlanAutoRenewal();
        $entityManager->persist($customerWithRenewedSettings);

        $previousCustomerMonthlyTrafficPlan = CustomerMonthlyTrafficPlanFactory::new($entityManager)->create(
            customer: $customerWithRenewed,
            activeUntil:  new DateTimeImmutable('2025-03-31 23:59:59'),
        );
        $entityManager->persist($previousCustomerMonthlyTrafficPlan);
        $renewedCustomerMonthlyTrafficPlan = CustomerMonthlyTrafficPlanFactory::new($entityManager)->create(
            customer: $customerWithRenewed,
            activeUntil:  new DateTimeImmutable('2025-04-30 23:59:59'),
        );
        $entityManager->persist($renewedCustomerMonthlyTrafficPlan);

        $customerWithTerminatedId = CustomerId::fromInteger(12344);
        $customerWithTerminated = CustomerFactory::new($entityManager)->create($customerWithTerminatedId);
        $entityManager->persist($customerWithTerminated);

        $customerWithTerminatedSettings = CustomerPaymentSettingsFactory::new($entityManager)
            ->create($customerWithTerminatedId);
        $customerWithTerminatedSettings->enableMonthlyPlanAutoRenewal();
        $entityManager->persist($customerWithTerminatedSettings);

        $terminatedCustomerMonthlyTrafficPlan = CustomerMonthlyTrafficPlanFactory::new($entityManager)->create(
            customer: $customerWithTerminated,
            activeUntil:  new DateTimeImmutable('2025-03-31 23:59:59'),
        );
        $terminatedCustomerMonthlyTrafficPlan->terminate();
        $entityManager->persist($terminatedCustomerMonthlyTrafficPlan);

        $customerId = CustomerId::fromInteger(12343);
        $customer = CustomerFactory::new($entityManager)->create($customerId);
        $entityManager->persist($customer);

        $customerSettings = CustomerPaymentSettingsFactory::new($entityManager)
            ->create($customerId);
        $customerSettings->enableMonthlyPlanAutoRenewal();
        $entityManager->persist($customerSettings);

        $customerMonthlyTrafficPlan = CustomerMonthlyTrafficPlanFactory::new($entityManager)->create(
            customer: $customer,
            activeUntil:  new DateTimeImmutable('2025-03-31 23:59:59'),
        );
        $entityManager->persist($customerMonthlyTrafficPlan);

        FlushAndClear::do($entityManager);

        /** @var DbalCustomerMonthlyTrafficPlanFinder $finder */
        $finder = $this->getContainerService(DbalCustomerMonthlyTrafficPlanFinder::class);

        $result = $finder->findAllForRechargeNow($now);

        self::assertCount(2, $result);

        $result->sort(
            static fn (
                CustomerMonthlyTrafficPlanId $a,
                CustomerMonthlyTrafficPlanId $b,
            ) => $a->toInt() <=> $b->toInt(),
        );

        $terminatedId = $result->first();
        self::assertSame($terminatedCustomerMonthlyTrafficPlan->getId(), $terminatedId);

        $renewableId = $result->last();
        self::assertSame($customerMonthlyTrafficPlan->getId(), $renewableId);
    }
}
