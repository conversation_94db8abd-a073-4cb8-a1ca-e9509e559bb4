<?php

declare(strict_types=1);

namespace Cdn77\Api\Tests\Customer\Application\Payload\TeamMember;

use Cdn77\Api\Core\Domain\Value\AccessRestriction\ResourceRestrictionType;
use Cdn77\Api\Customer\Application\Payload\TeamMember\AccessRestrictionsSchema;
use Cdn77\Api\Tests\TestCase;
use Cdn77\Api\Tests\Utils\WithSerializer;
use PHPUnit\Framework\Attributes\CoversClass;
use PHPUnit\Framework\Attributes\Group;

#[CoversClass(AccessRestrictionsSchema::class)]
#[Group('integration')]
final class AccessRestrictionSchemaTest extends TestCase
{
    use WithSerializer;

    public function testDeserialize(): void
    {
        $serializer = $this->getSerializer();
        $json = <<<'JSON'
{
  "restriction_type": "cdn",
  "ids": [1, 2, 3]
}
JSON;

        $result = $serializer->deserialize($json, AccessRestrictionsSchema::class, 'json');

        self::assertSame(ResourceRestrictionType::Cdn, $result->restrictionType);
        self::assertEquals([1, 2, 3], $result->ids);
    }

    public function testDeserializeMixed(): void
    {
        $serializer = $this->getSerializer();
        $json = <<<'JSON'
{
    "restriction_type": "origin",
    "ids": ["1", 2, 3]
}
JSON;
        $result = $serializer->deserialize($json, AccessRestrictionsSchema::class, 'json');

        self::assertSame(ResourceRestrictionType::Origin, $result->restrictionType);
        self::assertEquals(['1', 2, 3], $result->ids);
    }
}
