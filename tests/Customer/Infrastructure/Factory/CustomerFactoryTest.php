<?php

declare(strict_types=1);

namespace Cdn77\Api\Tests\Customer\Infrastructure\Factory;

use Cdn77\Api\Core\Domain\Entity\Customer\Credentials;
use Cdn77\Api\Core\Domain\Entity\Customer\CustomerId;
use Cdn77\Api\Core\Domain\Value\Customer\EmailAddress;
use Cdn77\Api\Core\Domain\Value\Customer\Name;
use Cdn77\Api\Core\Domain\Value\IpAddress;
use Cdn77\Api\CoreLibrary\GeoIp\GeoIpDatabaseReader;
use Cdn77\Api\Customer\Domain\Repository\AccountFlagsRepository;
use Cdn77\Api\Customer\Domain\Repository\AccountSettingsRepository;
use Cdn77\Api\Customer\Domain\Repository\CustomerCdnSettingsRepository;
use Cdn77\Api\Customer\Domain\Repository\CustomerOriginSettingsRepository;
use Cdn77\Api\Customer\Domain\Repository\CustomerPaymentSettingsRepository;
use Cdn77\Api\Customer\Domain\Repository\CustomerRepository;
use Cdn77\Api\Customer\Domain\Repository\CustomerTrafficRepository;
use Cdn77\Api\Customer\Domain\Value\PasswordHash;
use Cdn77\Api\Customer\Infrastructure\Factory\CustomerFactory;
use Cdn77\Api\Invoice\Domain\Repository\CountryRepository;
use Cdn77\Api\Invoice\Domain\Repository\InvoiceCustomerRepository;
use Cdn77\Api\Tests\TestCase;
use DateTimeImmutable;
use Mockery;
use PHPUnit\Framework\Attributes\CoversClass;
use PHPUnit\Framework\Attributes\Group;
use Psr\Log\NullLogger;

#[Group('unit')]
#[CoversClass(CustomerFactory::class)]
final class CustomerFactoryTest extends TestCase
{
    public function testCreate(): void
    {
        $customerId = CustomerId::fromInteger(12345);
        $email = EmailAddress::fromString('<EMAIL>');
        $name = Name::fromString('Unit Test');
        $now = new DateTimeImmutable('2020-09-11 07:21');
        $password = new PasswordHash('abcdefgh124');
        $phoneNumber = '+44 *********';
        $credentials = new Credentials($email, $password, $name, $phoneNumber);
        $signUpIp = new IpAddress('*************');

        $accountFlagsRepository = Mockery::mock(AccountFlagsRepository::class);
        $accountSettingsRepository = Mockery::mock(AccountSettingsRepository::class);
        $customerCdnSettingsRepository = Mockery::mock(CustomerCdnSettingsRepository::class);
        $customerOriginSettingsRepository = Mockery::mock(CustomerOriginSettingsRepository::class);
        $customerPaymentSettingsRepository = Mockery::mock(CustomerPaymentSettingsRepository::class);
        $customerTrafficRepository = Mockery::mock(CustomerTrafficRepository::class);
        $customerRepository = Mockery::mock(CustomerRepository::class);
        $geoIpDatabaseReader = new GeoIpDatabaseReader(new NullLogger(), '', 'test');
        $invoiceCountryRepository = Mockery::mock(CountryRepository::class);
        $invoiceCustomerRepository = Mockery::mock(InvoiceCustomerRepository::class);

        $customerRepository->expects('add');
        $accountFlagsRepository->expects('add');
        $customerCdnSettingsRepository->expects('add');
        $customerOriginSettingsRepository->expects('add');
        $accountSettingsRepository->expects('add');
        $invoiceCustomerRepository->expects('add');
        $customerPaymentSettingsRepository->expects('add');
        $customerTrafficRepository->expects('add');

        $customerFactory = new CustomerFactory(
            $accountFlagsRepository,
            $accountSettingsRepository,
            $invoiceCountryRepository,
            $customerCdnSettingsRepository,
            $customerOriginSettingsRepository,
            $customerPaymentSettingsRepository,
            $customerRepository,
            $customerTrafficRepository,
            $geoIpDatabaseReader,
            $invoiceCustomerRepository,
        );

        $customer = $customerFactory->create(
            $customerId,
            $credentials,
            $now,
            $signUpIp,
        );

        self::assertTrue($customerId->equals($customer->getId()));
        self::assertSame($credentials, $customer->getCredentials());
        self::assertSame($now, $customer->getCreatedAt());
    }
}
