<?php

declare(strict_types=1);

namespace Cdn77\AraClient\Tests\Unit\FixedPlans\Server;

use Cdn77\AraClient\FixedPlans\Server\DeleteEndpoint;
use Cdn77\AraClient\HttpStatusCode;
use GuzzleHttp\Psr7\Response;
use PHPUnit\Framework\TestCase;

final class DeleteEndpointTest extends TestCase
{
    private const SERVER_ID = 666;
    private const FIXED_PLAN_ID = 999;

    public function test() : void
    {
        $endpoint = new DeleteEndpoint(self::FIXED_PLAN_ID, self::SERVER_ID);

        $this->assertSame('DELETE', $endpoint->getMethod());
        $this->assertSame('/fixed-plan/' . self::FIXED_PLAN_ID . '/server/' . self::SERVER_ID, $endpoint->getUri());
        $this->assertSame([], $endpoint->getQuery());
        $this->assertSame([], $endpoint->getPayload());

        $response = new Response(HttpStatusCode::OK);
        $this->assertSame(HttpStatusCode::OK, $endpoint->processResponse($response));
    }
}
