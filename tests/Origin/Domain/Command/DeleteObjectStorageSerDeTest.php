<?php

declare(strict_types=1);

namespace Cdn77\Api\Tests\Origin\Domain\Command;

use Cdn77\Api\Core\Domain\Entity\Origin\OriginId;
use Cdn77\Api\Origin\Domain\Command\DeleteObjectStorage;
use Cdn77\Api\Tests\TestCase;
use Cdn77\Api\Tests\Utils\WithSerializer;
use Exception;
use J<PERSON>\Serializer\Exception\RuntimeException;
use PHPUnit\Framework\Attributes\CoversClass;
use PHPUnit\Framework\Attributes\Group;

#[Group('integration')]
#[CoversClass(DeleteObjectStorage::class)]
final class DeleteObjectStorageSerDeTest extends TestCase
{
    use WithSerializer;

    /**
     * @throws Exception
     * @throws RuntimeException
     */
    public function testSerialize(): void
    {
        $originIdRaw = 'c92c87d2-4247-4cd3-9c87-96b46d249ba8';
        $originId = OriginId::fromString($originIdRaw);

        $serializer = $this->getSerializer();

        $command = new DeleteObjectStorage($originId);

        $serializedCommand = $serializer->serialize($command, 'json');

        self::assertJson($serializedCommand);
        self::assertJsonStringEqualsJsonString(
            <<<JSON
{
  "origin_id": {
    "value": "$originIdRaw"
  },
  "retry_attempt": 0
}
JSON,
            $serializedCommand,
        );
    }

    /**
     * @throws Exception
     * @throws RuntimeException
     */
    public function testDeserialize(): void
    {
        $originIdRaw = 'c4d6c988-e581-445e-86e0-e8008a549875';

        $serializer = $this->getSerializer();

        $deserializedCommand = $serializer->deserialize(
            <<<JSON
{
  "origin_id": {
    "value": "$originIdRaw"
  },
  "retry_attempt": 1
}
JSON,
            DeleteObjectStorage::class,
            'json',
        );

        self::assertEquals(
            new DeleteObjectStorage(OriginId::fromString($originIdRaw), 1),
            $deserializedCommand,
        );
    }
}
