<?php

declare(strict_types=1);

namespace Cdn77\Api\Tests\Origin\Domain\Command;

use Cdn77\Api\Core\Domain\Entity\Customer\CustomerId;
use Cdn77\Api\Core\Domain\Value\Customer\EmailAddress;
use Cdn77\Api\Origin\Domain\Command\SendWelcomeToCdn77ObjectStorage;
use Cdn77\Api\Tests\TestCase;
use Cdn77\Api\Tests\Utils\WithSerializer;
use JMS\Serializer\Exception\RuntimeException;
use PHPUnit\Framework\Attributes\CoversClass;
use PHPUnit\Framework\Attributes\Group;

#[Group('integration')]
#[CoversClass(SendWelcomeToCdn77ObjectStorage::class)]
final class SendWelcomeToCdn77ObjectStorageSerDeTest extends TestCase
{
    use WithSerializer;

    /** @throws RuntimeException */
    public function testSerialize(): void
    {
        $command = new SendWelcomeToCdn77ObjectStorage(
            CustomerId::fromInteger(1),
            EmailAddress::fromString('<EMAIL>'),
        );

        $serializer = $this->getSerializer();

        $serializedCommand = $serializer->serialize($command, 'json');

        self::assertJson($serializedCommand);
        self::assertJsonStringEqualsJsonString(
            <<<'JSON'
{
    "customer_id": {
        "value": 1
    },
    "send_to": {
        "email_address": "<EMAIL>"
    }
}
JSON,
            $serializedCommand,
        );
    }

    /** @throws RuntimeException */
    public function testDeserialize(): void
    {
        $customerId = 2;
        $sendTo = '<EMAIL>';

        $serializer = $this->getSerializer();

        $deserializedCommand = $serializer->deserialize(
            <<<JSON
{
    "customer_id": {
        "value": $customerId
    },
    "send_to": {
        "email_address": "$sendTo",
        "display_name": null
    }
}
JSON,
            SendWelcomeToCdn77ObjectStorage::class,
            'json',
        );

        self::assertEquals(
            new SendWelcomeToCdn77ObjectStorage(
                CustomerId::fromInteger($customerId),
                EmailAddress::fromString($sendTo),
            ),
            $deserializedCommand,
        );
    }
}
