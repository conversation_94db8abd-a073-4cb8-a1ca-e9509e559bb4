<?php

declare(strict_types=1);

namespace Cdn77\Api\Tests\Origin\Application\Controller;

use Cdn77\Api\Core\Application\Payload\ErrorsSchema;
use Cdn77\Api\Core\Application\Payload\FieldsErrorsSchema;
use Cdn77\Api\Core\Domain\Entity\Customer\Customer;
use Cdn77\Api\Origin\Application\Controller\CreateUrlOriginController;
use Cdn77\Api\Origin\Application\Payload\UrlOriginSchema;
use Cdn77\Api\Origin\Domain\Exception\OriginCouldNotBeCreated;
use Cdn77\Api\Tests\ORMFixtures\Api\PersonalTokenFactory;
use Cdn77\Api\Tests\ORMFixtures\Customer\AccountFlagsFactory;
use Cdn77\Api\Tests\ORMFixtures\Customer\CustomerFactory;
use Cdn77\Api\Tests\ORMFixtures\Origin\CustomerOriginSettingsFactory;
use Cdn77\Api\Tests\TestCase;
use Cdn77\Api\Tests\Utils\FlushAndClear;
use Cdn77\Api\Tests\Utils\TokenGenerator;
use Cdn77\Api\Tests\Utils\WithBrowser;
use Exception;
use PHPUnit\Framework\Attributes\CoversClass;
use PHPUnit\Framework\Attributes\Group;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Webmozart\Assert\Assert;

use function Psl\Json\encode;

#[CoversClass(CreateUrlOriginController::class)]
#[Group('integration')]
final class AddUrlOriginControllerTest extends TestCase
{
    use WithBrowser;

    /** @throws Exception */
    public function testAddOrigin(): void
    {
        ['testing_customer' => $customer] = $this->loadFixturesFromFile(__DIR__ . '/fixtures/common.yaml');

        Assert::isInstanceOf($customer, Customer::class);

        $requestData = encode([
            'base_dir' => '/dir/with/content',
            'host' => 'www.host.dev',
            UrlOriginSchema::FieldLabel => 'My new Origin',
            'port' => null,
            'scheme' => 'https',
        ]);

        $token = TokenGenerator::generateApplication($customer->getNewId());
        $response = $this->performHttpRequest(
            Request::METHOD_POST,
            '/v3/origin',
            $this->prepareAuthHeaders($token),
            [],
            [],
            $requestData,
        );

        self::assertHttpCode(Response::HTTP_CREATED, $response);
    }

    /** @throws Exception */
    public function testAddOriginWithBackslash(): void
    {
        ['testing_customer' => $customer] = $this->loadFixturesFromFile(__DIR__ . '/fixtures/common.yaml');

        Assert::isInstanceOf($customer, Customer::class);

        $requestData = encode([
            'base_dir' => '/dir/with/backslash\\',
            'host' => 'www.host.dev',
            UrlOriginSchema::FieldLabel => 'My new Origin',
            'port' => null,
            'scheme' => 'https',
        ]);

        $token = TokenGenerator::generateApplication($customer->getNewId());
        $response = $this->performHttpRequest(
            Request::METHOD_POST,
            '/v3/origin',
            $this->prepareAuthHeaders($token),
            [],
            [],
            $requestData,
        );

        self::assertHttpCode(Response::HTTP_UNPROCESSABLE_ENTITY, $response);
        $errorMessage = 'Path (base dir) should not end with "/"'
            . ' and should only contain alphanumeric characters or (.-_[]+%*/).';
        self::assertJsonResponse(
            [
                FieldsErrorsSchema::FieldErrors
                => ['Request field validation failed. Please refer to the API documentation'],
                FieldsErrorsSchema::FieldFields => ['base_dir' => [$errorMessage]],
            ],
            $response,
        );
    }

    /** @throws Exception */
    public function testAddOriginWithUnderscore(): void
    {
        ['testing_customer' => $customer] = $this->loadFixturesFromFile(__DIR__ . '/fixtures/common.yaml');

        Assert::isInstanceOf($customer, Customer::class);

        $requestData = encode([
            'host' => '_static.foo.bar',
            UrlOriginSchema::FieldLabel => 'Underscore',
            'port' => null,
            'scheme' => 'https',
        ]);

        $token = TokenGenerator::generateApplication($customer->getNewId());

        $response = $this->performHttpRequest(
            Request::METHOD_POST,
            '/v3/origin',
            $this->prepareAuthHeaders($token),
            [],
            [],
            $requestData,
        );

        self::assertHttpCode(Response::HTTP_CREATED, $response);
    }

    /** @throws Exception */
    public function testAddOriginQuotaLimit(): void
    {
        $maxOrigins = 0;
        $entityManager = $this->entityManager();
        $customer = CustomerFactory::new($entityManager)->create();
        $entityManager->persist($customer);
        $customerOriginSettings = CustomerOriginSettingsFactory::new($entityManager)
            ->create($customer->getNewId(), $maxOrigins);
        $entityManager->persist($customerOriginSettings);
        $token = PersonalTokenFactory::new($entityManager)->create($customer->getId());
        $entityManager->persist($token);
        $accountFlags = AccountFlagsFactory::new($entityManager)->create($customer->getId());
        $entityManager->persist($accountFlags);
        FlushAndClear::do($entityManager);

        $requestData = encode([
            UrlOriginSchema::FieldHost => '_static.foo.bar',
            UrlOriginSchema::FieldLabel => 'Underscore',
            UrlOriginSchema::FieldPort => null,
            UrlOriginSchema::FieldScheme => 'https',
        ]);

        $token = TokenGenerator::generate($token->getId(), PersonalTokenFactory::TestTokenSecret);
        $response = $this->performHttpRequest(
            Request::METHOD_POST,
            '/v3/origin',
            $this->prepareAuthHeaders($token),
            [],
            [],
            $requestData,
        );

        $expectedError = OriginCouldNotBeCreated::maximumOriginsPerCustomerExceeds($maxOrigins);
        self::assertHttpCode($expectedError->getResponseCode(), $response);
        self::assertJsonResponse([ErrorsSchema::FieldErrors => [$expectedError->getMessage()]], $response);
    }
}
