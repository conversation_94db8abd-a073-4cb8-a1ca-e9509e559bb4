<?php

declare(strict_types=1);

namespace Cdn77\Api\Tests\Origin\Application\Payload\ObjectStorage;

use Cdn77\Api\Core\Domain\Entity\ObjectStorage\AccessKeyId;
use Cdn77\Api\Origin\Application\Payload\ObjectStorage\EditObjectStorageOriginSchema;
use Cdn77\Api\Origin\Domain\Dto\ObjectStorageOriginConfiguration;
use Cdn77\Api\Tests\TestCase;
use Cdn77\Api\Tests\Utils\WithSerializer;
use PHPUnit\Framework\Attributes\CoversClass;
use PHPUnit\Framework\Attributes\Group;

#[CoversClass(EditObjectStorageOriginSchema::class)]
#[Group('integration')]
final class EditObjectStorageOriginSchemaTest extends TestCase
{
    use WithSerializer;

    public function testDeserialize(): void
    {
        $payload = <<<'JSON'
{
  "access_keys": [
    {
      "access_key_id": "5cdb1bc9-fdfb-470b-a9b1-f86aa3157355",
      "type": "full_access"
    }
  ]
}
JSON;
        /** @var ObjectStorageOriginConfiguration $config */
        $config = $this->getSerializer()
            ->deserialize($payload, EditObjectStorageOriginSchema::class, 'json')
            ->toDto();

        self::assertNull($config->note);
        self::assertNull($config->fallbackConfiguration);
        self::assertNull($config->baseDirectory);
        self::assertNotNull($config->accessKeys);
        self::assertCount(1, $config->accessKeys);
        self::assertSame(
            'full_access',
            $config->accessKeys->get(AccessKeyId::fromString('5cdb1bc9-fdfb-470b-a9b1-f86aa3157355'))->value,
        );
    }
}
