<?php

declare(strict_types=1);

namespace Cdn77\Api\Tests\Origin\Infrastructure\Quota;

use Cdn77\Api\Core\Domain\Entity\Customer\Customer;
use Cdn77\Api\Core\Domain\Entity\Customer\CustomerOriginSettings;
use Cdn77\Api\Core\Domain\Entity\Customer\CustomerUuid;
use Cdn77\Api\Customer\Domain\Repository\CustomerOriginSettingsRepository;
use Cdn77\Api\Origin\Domain\Exception\OriginCouldNotBeCreated;
use Cdn77\Api\Origin\Domain\Finder\OriginCountFinder;
use Cdn77\Api\Origin\Infrastructure\Quota\DatabaseCreatedOriginQuotaChecker;
use Cdn77\Api\Tests\TestCase;
use Cdn77\TestUtils\Stub;
use Exception;
use Generator;
use Mockery;
use PHPUnit\Framework\Attributes\CoversClass;
use PHPUnit\Framework\Attributes\DataProvider;
use PHPUnit\Framework\Attributes\Group;

#[Group('unit')]
#[CoversClass(DatabaseCreatedOriginQuotaChecker::class)]
final class DatabaseCreatedOriginQuotaCheckerTest extends TestCase
{
    /** @throws Exception */
    #[DataProvider('providerQuotaPassed')]
    public function testQuotaPassed(int $originCount, int $maxOriginCountLimit): void
    {
        $customer = Stub::create(Customer::class, [
            Customer::FieldNewId => CustomerUuid::new()->get(),
        ]);
        $customerOriginSettings = Stub::create(
            CustomerOriginSettings::class,
            [CustomerOriginSettings::FieldMaxOrigins => $maxOriginCountLimit],
        );

        $customerOriginSettingsRepository = Mockery::mock(CustomerOriginSettingsRepository::class);
        $customerOriginSettingsRepository->expects('getForCustomer')
            ->with($customer->getNewId())
            ->andReturn($customerOriginSettings);
        $finder = Mockery::mock(OriginCountFinder::class);
        $finder->expects('findActiveForCustomer')
            ->with($customer->getNewId())
            ->andReturn($originCount);

        $checker = new DatabaseCreatedOriginQuotaChecker($customerOriginSettingsRepository, $finder);
        $checker->checkForCustomer($customer);
    }

    /** @return Generator<string, array{int, int}> */
    public static function providerQuotaPassed(): Generator
    {
        yield 'Can add when having O origin and quota is 1' => [0, 1];
        yield 'Can add when having 0 origin and quota is 10' => [0, 10];
        yield 'Can add when having 3 origin and quota is 10' => [3, 10];
        yield 'Can add when having 9 origin and quota is 10' => [9, 10];
    }

    /** @throws Exception */
    #[DataProvider('providerQuotaExceeds')]
    public function testQuotaExceeds(int $originCount, int $maxOriginCountLimit): void
    {
        $customer = Stub::create(Customer::class, [
            Customer::FieldNewId => CustomerUuid::new()->get(),
        ]);
        $customerOriginSettings = Stub::create(
            CustomerOriginSettings::class,
            [CustomerOriginSettings::FieldMaxOrigins => $maxOriginCountLimit],
        );

        $customerOriginSettingsRepository = Mockery::mock(CustomerOriginSettingsRepository::class);
        $customerOriginSettingsRepository->expects('getForCustomer')
            ->with($customer->getNewId())
            ->andReturn($customerOriginSettings);
        $originCountFinder = Mockery::mock(OriginCountFinder::class);
        $originCountFinder->expects('findActiveForCustomer')
            ->with($customer->getNewId())
            ->andReturn($originCount);

        $checker = new DatabaseCreatedOriginQuotaChecker($customerOriginSettingsRepository, $originCountFinder);
        $this->expectExceptionObject(OriginCouldNotBeCreated::maximumOriginsPerCustomerExceeds($maxOriginCountLimit));
        $checker->checkForCustomer($customer);
    }

    /** @return Generator<string, array{int, int}> */
    public static function providerQuotaExceeds(): Generator
    {
        yield 'Cannot add when having 1 origin and quota is 0' => [1, 0];
        yield 'Cannot add when having 1 origin and quota is 1' => [1, 1];
        yield 'Cannot add when having 4 origin and quota is 1' => [4, 2];
        yield 'Cannot add when having 4 origin and quota is 4' => [4, 4];
        yield 'Cannot add when having 7 origin and quota is 4' => [7, 4];
    }
}
