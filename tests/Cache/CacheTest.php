<?php

declare(strict_types=1);

namespace Cdn77\ValueObject\Tests\Cache;

use Cdn77\ValueObject\Cache\Cache;
use Cdn77\ValueObject\Tests\Cache\Fixture\SerializableFixture;
use Cdn77\ValueObject\Tests\Cache\Fixture\VoWithCacheManyArgs;
use Cdn77\ValueObject\Tests\Cache\Fixture\VoWithCacheSingleArg;
use Cdn77\ValueObject\Tests\Cache\Fixture\VoWithCacheSingleArgNotSaved;
use Cdn77\ValueObject\Tests\Cache\Fixture\VoWithCacheTwoArgs;
use Cdn77\ValueObject\Tests\Cache\Fixture\VoWithoutCache;
use DateTimeImmutable;
use JsonSerializable;
use PHPUnit\Framework\Attributes\CoversTrait;
use PHPUnit\Framework\Attributes\Group;
use PHPUnit\Framework\TestCase;
use stdClass;

#[CoversTrait(Cache::class)]
#[Group('unit')]
final class CacheTest extends TestCase
{
    public function testVoWithCacheIsReusedForNewInstance(): void
    {
        $jsonSerializable = new class () implements JsonSerializable {
            public function jsonSerialize(): string
            {
                return '';
            }
        };

        self::assertSame(
            VoWithCacheManyArgs::new(
                new DateTimeImmutable('2021-07-28 06:26:28.377123'),
                1,
                VoWithCacheSingleArg::new(1),
                new stdClass(),
                new SerializableFixture(1),
                $jsonSerializable,
            ),
            VoWithCacheManyArgs::new(
                new DateTimeImmutable('2021-07-28 06:26:28.377123'),
                1,
                VoWithCacheSingleArg::new(1),
                new stdClass(),
                new SerializableFixture(1),
                $jsonSerializable,
            ),
        );

        self::assertSame(
            VoWithCacheTwoArgs::new(1, '1'),
            VoWithCacheTwoArgs::new(1, '1'),
        );

        self::assertNotSame(
            VoWithCacheSingleArg::new(new VoWithoutCache()),
            VoWithCacheSingleArg::new(new VoWithoutCache()),
        );
    }

    public function testVoWithCacheIsNotReusedWhenArgsDiffer(): void
    {
        self::assertNotSame(
            VoWithCacheTwoArgs::new(1, 1),
            VoWithCacheTwoArgs::new(2, 1),
        );

        self::assertNotSame(
            VoWithCacheSingleArg::new(new SerializableFixture(1)),
            VoWithCacheSingleArg::new(new SerializableFixture(2)),
        );

        self::assertNotSame(
            VoWithCacheSingleArg::new(1),
            VoWithCacheSingleArg::new('1'),
        );

        self::assertSame(
            VoWithCacheSingleArgNotSaved::new(new VoWithoutCache()),
            VoWithCacheSingleArgNotSaved::new(new VoWithoutCache()),
        );
    }
}
