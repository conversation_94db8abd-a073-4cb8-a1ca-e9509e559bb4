mutation AddRealTimeLog($input: AddRealTimeLogInput!) {
    addRealTimeLog(input: $input) {
        realTimeLog {
            id
            scope
            outputFormat
            activeUntil
            isActive
            originId
            bucketName
            ttlConfig {
                days
            }
            cdns {
                id
                label
            }
            logPath {
                root
                directory
            }
            loggingFields {
                name
                outputName
            }
        }
    }
}
