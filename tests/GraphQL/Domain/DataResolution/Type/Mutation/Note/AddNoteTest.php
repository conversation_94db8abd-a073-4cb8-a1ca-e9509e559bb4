<?php

declare(strict_types=1);

namespace Cdn77\Api\Tests\GraphQL\Domain\DataResolution\Type\Mutation\Note;

use Cdn77\Api\Customer\Domain\Command\AddNote;
use Cdn77\Api\Customer\Domain\Command\AddNoteHandler;
use Cdn77\Api\GraphQL\Application\Runtime\Server;
use Cdn77\Api\GraphQL\Domain\DataResolution\Type\Mutation\Note\AddNote as AddNoteMutation;
use Cdn77\Api\GraphQL\Domain\DataResolution\Type\Mutation\Note\AddNoteInput;
use Cdn77\Api\GraphQL\Domain\DataResolution\Type\Mutation\Note\NotePayloadDto;
use Cdn77\Api\GraphQL\Domain\DataResolution\Type\Mutation\Note\NotePayloadField;
use Cdn77\Api\GraphQL\Domain\DataResolution\Type\Note\NoteDataLoaderFactory;
use Cdn77\Api\Notes\Domain\Query\FindNotes\Input\Filter;
use Cdn77\Api\Notes\Domain\Query\FindNotes\Input\SelectionField;
use Cdn77\Api\Notes\Infrastructure\Finder\DbalNotesFinder;
use Cdn77\Api\Tests\ORMFixtures\Customer\CustomerFactory;
use Cdn77\Api\Tests\TestCase;
use Cdn77\Api\Tests\Utils\FlushAndClear;
use Cdn77\Api\Tests\Utils\GraphQLAssertion;
use Cdn77\Api\Tests\Utils\WithKernel;
use Cdn77\Api\Tests\Utils\WithPostgres;
use GraphQL\Executor\ExecutionResult;
use GraphQL\Server\OperationParams;
use PHPUnit\Framework\Attributes\CoversClass;
use PHPUnit\Framework\Attributes\Group;

use function Safe\file_get_contents;

#[CoversClass(AddNote::class)]
#[CoversClass(AddNoteHandler::class)]
#[CoversClass(AddNoteMutation::class)]
#[CoversClass(NoteDataLoaderFactory::class)]
#[CoversClass(AddNoteInput::class)]
#[CoversClass(NotePayloadField::class)]
#[CoversClass(NotePayloadDto::class)]
#[CoversClass(Filter::class)]
#[CoversClass(DbalNotesFinder::class)]
#[Group('integration')]
final class AddNoteTest extends TestCase
{
    use WithKernel;
    use WithPostgres;

    public function testAddNote(): void
    {
        $entityManager = $this->entityManager();

        $customer = CustomerFactory::new($entityManager)->create();
        $entityManager->persist($customer);

        FlushAndClear::do($entityManager);

        $server = $this->getContainerService(Server::class);

        $result = $server->executeRequest(
            OperationParams::create([
                'query' => file_get_contents(__DIR__ . '/addNote.graphql'),
                'variables' => [
                    AddNoteMutation::ArgumentInput => [
                        AddNoteInput::FieldText => 'test',
                        AddNoteInput::FieldIsPinned => false,
                        AddNoteInput::FieldCustomerId => $customer->getNewId()->toString(),
                    ],
                ],
            ]),
        );

        self::assertInstanceOf(ExecutionResult::class, $result);
        GraphQLAssertion::noErrorsOccurred($result);

        $data = $result->data[AddNoteMutation::Name] ?? [];
        self::assertIsArray($data);

        $noteData = $data[NotePayloadField::Note->value];
        self::assertIsArray($noteData);

        $id = $noteData[SelectionField::Id->value] ?? null;
        self::assertNotNull($id);
    }
}
