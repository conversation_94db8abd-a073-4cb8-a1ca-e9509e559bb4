<?php

declare(strict_types=1);

namespace Cdn77\Api\Tests\Datacenter\Domain\Command;

use Cdn77\Api\Core\Domain\Entity\Datacenter\Location;
use Cdn77\Api\Core\Domain\Entity\Datacenter\ServerId;
use Cdn77\Api\Core\Domain\Entity\Datacenter\Status;
use Cdn77\Api\Core\Domain\Entity\Invoice\Country;
use Cdn77\Api\Core\Domain\MC\MCApi;
use Cdn77\Api\Datacenter\Domain\Command\UpdateLocationStatus;
use Cdn77\Api\Datacenter\Domain\Command\UpdateLocationStatusHandler;
use Cdn77\Api\Datacenter\Domain\Enum\StatusCode;
use Cdn77\Api\Datacenter\Domain\Repository\LocationRepository;
use Cdn77\Api\Datacenter\Domain\Repository\StatusRepository;
use Cdn77\Api\Datacenter\Domain\Updater\StatusUpdater;
use Cdn77\Api\Datacenter\Domain\Value\CityCode;
use Cdn77\Api\Server\Domain\Dto\Pop;
use Cdn77\Api\Server\Domain\Dto\Server;
use Cdn77\Api\Server\Domain\Value\LocationId;
use Cdn77\Api\Server\Domain\Value\PopId;
use Cdn77\Api\Server\Domain\Value\ServerType;
use Cdn77\Api\Tests\TestCase;
use Cdn77\TestUtils\Stub;
use DateTimeImmutable;
use Ds\Map;
use Ds\Pair;
use Lcobucci\Clock\FrozenClock;
use Mockery;
use PHPUnit\Framework\Attributes\CoversClass;
use PHPUnit\Framework\Attributes\Group;
use Psl\Range\Exception\InvalidRangeException;
use Psr\Log\LoggerInterface;
use Symfony\Component\Uid\Uuid;

use function Cdn77\Functions\mapFromIterable;
use function Psl\Range\between;

#[Group('unit')]
#[CoversClass(UpdateLocationStatusHandler::class)]
#[CoversClass(UpdateLocationStatus::class)]
final class UpdateLocationStatusHandlerTest extends TestCase
{
    private const string CityCodeA = 'TOR';
    private const string CityCodeB = 'PRG';
    private const string CityCodeC = 'FRA';

    public function testUpdate(): void
    {
        $clock = new FrozenClock(new DateTimeImmutable('2022-08-31 00:00:00'));

        $logger = Mockery::mock(LoggerInterface::class);
        $locationRepository = Mockery::mock(LocationRepository::class);
        $mcApi = Mockery::mock(MCApi::class);
        $statusRepository = Mockery::mock(StatusRepository::class);
        $statusUpdater = Mockery::mock(StatusUpdater::class);

        $currentStatusesData = [
            self::CityCodeA => [
                Status::FieldLocationUuid => Uuid::v4(),
                Status::FieldLocation => Stub::create(
                    Location::class,
                    [Location::FieldCityCode => CityCode::fromString(self::CityCodeA)],
                ),
                Status::FieldStatusCode => StatusCode::Online,
            ],
            self::CityCodeB => [
                Status::FieldLocationUuid => Uuid::v4(),
                Status::FieldLocation => Stub::create(
                    Location::class,
                    [Location::FieldCityCode => CityCode::fromString(self::CityCodeB)],
                ),
                Status::FieldStatusCode => StatusCode::Online,
            ],
            self::CityCodeC => [
                Status::FieldLocationUuid => Uuid::v4(),
                Status::FieldLocation => Stub::create(
                    Location::class,
                    [Location::FieldCityCode => CityCode::fromString(self::CityCodeC)],
                ),
                Status::FieldStatusCode => StatusCode::Maintenance,
            ],
        ];

        $currentStatuses = mapFromIterable(
            $currentStatusesData,
            static fn (string $cityCode, array $data) => new Pair(
                CityCode::fromString($cityCode),
                Stub::create(Status::class, $data),
            ),
        );

        $serversData = [
            0 => [
                Server::KeyUid => 1234,
                Server::KeyPopId => 111,
                Server::KeyIPs => [],
                Server::KeyStatus => [Server::KeyPaused => false, Server::KeyUp => true],
            ],
            1 => [
                Server::KeyUid => 1235,
                Server::KeyPopId => 112,
                Server::KeyIPs => [],
                Server::KeyStatus => [Server::KeyPaused => false, Server::KeyUp => true],
            ],
            2 => [
                Server::KeyUid => 1236,
                Server::KeyPopId => 113,
                Server::KeyIPs => [],
                Server::KeyStatus => [Server::KeyPaused => false, Server::KeyUp => true],
            ],
        ];

        $servers = mapFromIterable(
            $serversData,
            static fn (int $_, array $data) => new Pair(
                ServerId::fromInteger($data[Server::KeyUid]),
                Server::fromArray($data),
            ),
        );

        $popsData = [
            0 => [
                Pop::KeyId => 111,
                Pop::KeyType => 'STORAGE',
                Pop::KeyLocationId => 'torontoCAON',
                Pop::KeyCityCode => self::CityCodeA,
            ],
            1 => [
                Pop::KeyId => 112,
                Pop::KeyType => 'LB',
                Pop::KeyLocationId => 'pragueCZ',
                Pop::KeyCityCode => self::CityCodeB,
            ],
            2 => [
                Pop::KeyId => 113,
                Pop::KeyType => '',
                Pop::KeyLocationId => 'frankfurtDE',
                Pop::KeyCityCode => self::CityCodeC,
            ],
        ];

        $pops = mapFromIterable(
            $popsData,
            static fn (int $_, array $data) => new Pair(
                PopId::fromInteger($data[Pop::KeyId]),
                Pop::fromArray($data),
            ),
        );

        $statusRepository->shouldReceive('findAll')
            ->once()
            ->andReturn($currentStatuses);
        $mcApi->shouldReceive('getServers')
            ->once()
            ->andReturn($servers);
        $mcApi->shouldReceive('getPops')
            ->once()
            ->andReturn($pops);
        $statusUpdater->shouldReceive('bulkUpdate')
            ->twice();

        $commandHandler = new UpdateLocationStatusHandler(
            $clock,
            $locationRepository,
            $logger,
            $mcApi,
            $statusRepository,
            $statusUpdater,
        );

        $commandHandler->handle(new UpdateLocationStatus());
    }

    /** @throws InvalidRangeException */
    public function testRemove(): void
    {
        $clock = new FrozenClock(new DateTimeImmutable('2022-08-31 00:00:00'));

        $logger = Mockery::mock(LoggerInterface::class);
        $locationRepository = Mockery::mock(LocationRepository::class);
        $mcApi = Mockery::mock(MCApi::class);
        $statusRepository = Mockery::mock(StatusRepository::class);
        $statusUpdater = Mockery::mock(StatusUpdater::class);

        $currentStatusesData = [
            self::CityCodeA => [
                Status::FieldLocationUuid => Uuid::v4(),
                Status::FieldLocation => Stub::create(
                    Location::class,
                    [Location::FieldCityCode => CityCode::fromString(self::CityCodeA)],
                ),
                Status::FieldStatusCode => StatusCode::Online,
            ],
            self::CityCodeB => [
                Status::FieldLocationUuid => Uuid::v4(),
                Status::FieldLocation => Stub::create(
                    Location::class,
                    [Location::FieldCityCode => CityCode::fromString(self::CityCodeB)],
                ),
                Status::FieldStatusCode => StatusCode::Online,
            ],
        ];

        $currentStatuses = mapFromIterable(
            $currentStatusesData,
            static fn (string $cityCode, array $data) => new Pair(
                CityCode::fromString($cityCode),
                Stub::create(Status::class, $data),
            ),
        );
        $servers = mapFromIterable(
            between(0, 1900),
            static fn (mixed $_, int $uid) => new Pair(
                ServerId::fromInteger($uid),
                new Server(
                    ServerId::fromInteger($uid),
                    PopId::fromInteger(111),
                    StatusCode::Online,
                ),
            ),
        );
        $pops = new Map();
        $pops->put(
            PopId::fromInteger(111),
            new Pop(
                PopId::fromInteger(111),
                CityCode::fromString(self::CityCodeA),
                LocationId::fromString('torontoCAON'),
                ServerType::Edge,
            ),
        );

        $statusRepository
            ->expects('findAll')
            ->andReturn($currentStatuses);
        $mcApi
            ->expects('getServers')
            ->andReturn($servers);
        $mcApi
            ->expects('getPops')
            ->andReturn($pops);
        $statusRepository->expects('remove');
        $statusUpdater->expects('bulkUpdate');

        $commandHandler = new UpdateLocationStatusHandler(
            $clock,
            $locationRepository,
            $logger,
            $mcApi,
            $statusRepository,
            $statusUpdater,
        );

        $commandHandler->handle(new UpdateLocationStatus());
    }

    public function testAdd(): void
    {
        $clock = new FrozenClock(new DateTimeImmutable('2022-08-31 00:00:00'));

        $logger = Mockery::mock(LoggerInterface::class);
        $locationRepository = Mockery::mock(LocationRepository::class);
        $mcApi = Mockery::mock(MCApi::class);
        $statusRepository = Mockery::mock(StatusRepository::class);
        $statusUpdater = Mockery::mock(StatusUpdater::class);

        $currentStatusesData = [
            self::CityCodeA => [
                Status::FieldLocationUuid => Uuid::v4(),
                Status::FieldLocation => Stub::create(
                    Location::class,
                    [Location::FieldCityCode => CityCode::fromString(self::CityCodeA)],
                ),
                Status::FieldStatusCode => StatusCode::Online,
            ],
        ];

        $currentStatuses = mapFromIterable(
            $currentStatusesData,
            static fn (string $cityCode, array $data) => new Pair(
                CityCode::fromString($cityCode),
                Stub::create(Status::class, $data),
            ),
        );

        $serversData = [
            0 => [
                Server::KeyUid => 1234,
                Server::KeyPopId => 111,
                Server::KeyIPs => [],
                Server::KeyStatus => [Server::KeyPaused => false, Server::KeyUp => true],
            ],
            1 => [
                Server::KeyUid => 1235,
                Server::KeyPopId => 112,
                Server::KeyIPs => [],
                Server::KeyStatus => [Server::KeyPaused => false, Server::KeyUp => true],
            ],
        ];

        $servers = mapFromIterable(
            $serversData,
            static fn (int $_, array $data) => new Pair(
                ServerId::fromInteger($data[Server::KeyUid]),
                Server::fromArray($data),
            ),
        );

        $popsData = [
            0 => [
                Pop::KeyId => 111,
                Pop::KeyType => 'LB',
                Pop::KeyLocationId => 'torontoCAON',
                Pop::KeyCityCode => self::CityCodeA,
            ],
            1 => [
                Pop::KeyId => 112,
                Pop::KeyType => '',
                Pop::KeyLocationId => 'pragueCZ',
                Pop::KeyCityCode => self::CityCodeB,
            ],
        ];

        $pops = mapFromIterable(
            $popsData,
            static fn (int $_, array $data) => new Pair(
                PopId::fromInteger($data[Pop::KeyId]),
                Pop::fromArray($data),
            ),
        );

        $location = Stub::create(Location::class, [
            Location::FieldUuid => Uuid::v4(),
            Location::FieldCityCode => CityCode::fromString(self::CityCodeB),
            Location::FieldCountry => Stub::create(Country::class, [Country::FieldIso => 'CZ']),
        ]);

        $statusRepository->shouldReceive('findAll')
            ->once()
            ->andReturn($currentStatuses);
        $mcApi->shouldReceive('getServers')
            ->once()
            ->andReturn($servers);
        $mcApi->shouldReceive('getPops')
            ->once()
            ->andReturn($pops);
        $locationRepository->shouldReceive('getForCityCode')
            ->once()
            ->andReturn($location);
        $statusRepository->shouldReceive('add')
            ->once();
        $statusUpdater->shouldReceive('bulkUpdate')
            ->once();

        $commandHandler = new UpdateLocationStatusHandler(
            $clock,
            $locationRepository,
            $logger,
            $mcApi,
            $statusRepository,
            $statusUpdater,
        );

        $commandHandler->handle(new UpdateLocationStatus());
    }

    public function testAddAndRemove(): void
    {
        $clock = new FrozenClock(new DateTimeImmutable('2022-08-31 00:00:00'));

        $logger = Mockery::mock(LoggerInterface::class);
        $locationRepository = Mockery::mock(LocationRepository::class);
        $mcApi = Mockery::mock(MCApi::class);
        $statusRepository = Mockery::mock(StatusRepository::class);
        $statusUpdater = Mockery::mock(StatusUpdater::class);

        $currentStatusesData = [
            self::CityCodeA => [
                Status::FieldLocationUuid => Uuid::v4(),
                Status::FieldLocation => Stub::create(
                    Location::class,
                    [Location::FieldCityCode => CityCode::fromString(self::CityCodeA)],
                ),
                Status::FieldStatusCode => StatusCode::Online,
            ],
        ];

        $currentStatuses = mapFromIterable(
            $currentStatusesData,
            static fn (string $cityCode, array $data) => new Pair(
                CityCode::fromString($cityCode),
                Stub::create(Status::class, $data),
            ),
        );

        $serversData = [
            0 => [
                Server::KeyUid => 1235,
                Server::KeyPopId => 112,
                Server::KeyIPs => [],
                Server::KeyStatus => [Server::KeyPaused => false, Server::KeyUp => true],
            ],
        ];

        $servers = mapFromIterable(
            $serversData,
            static fn (int $_, array $data) => new Pair(
                ServerId::fromInteger($data[Server::KeyUid]),
                Server::fromArray($data),
            ),
        );

        $popsData = [
            0 => [
                Pop::KeyId => 112,
                Pop::KeyType => 'STORAGE',
                Pop::KeyLocationId => 'pragueCZ',
                Pop::KeyCityCode => self::CityCodeB,
            ],
        ];

        $pops = mapFromIterable(
            $popsData,
            static fn (int $_, array $data) => new Pair(
                PopId::fromInteger($data[Pop::KeyId]),
                Pop::fromArray($data),
            ),
        );

        $location = Stub::create(Location::class, [
            Location::FieldUuid => Uuid::v4(),
            Location::FieldCountry => Stub::create(Country::class, [Country::FieldIso => 'CZ']),
        ]);

        $statusRepository->shouldReceive('findAll')
            ->once()
            ->andReturn($currentStatuses);
        $mcApi->shouldReceive('getServers')
            ->once()
            ->andReturn($servers);
        $mcApi->shouldReceive('getPops')
            ->once()
            ->andReturn($pops);
        $locationRepository->shouldReceive('getForCityCode')
            ->once()
            ->andReturn($location);
        $statusRepository->shouldReceive('add')
            ->once();
        $statusRepository->shouldReceive('remove')
            ->once();

        $commandHandler = new UpdateLocationStatusHandler(
            $clock,
            $locationRepository,
            $logger,
            $mcApi,
            $statusRepository,
            $statusUpdater,
        );

        $commandHandler->handle(new UpdateLocationStatus());
    }
}
