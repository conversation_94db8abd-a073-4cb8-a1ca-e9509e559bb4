<?php

declare(strict_types=1);

namespace Cdn77\Api\Tests\RevokeToken\Application\Controller;

use Cdn77\Api\Core\Domain\Entity\Customer\CustomerUuid;
use Cdn77\Api\RevokeToken\Domain\Command\DeleteToken;
use Cdn77\Api\RevokeToken\Domain\Command\DeleteTokenHandler;
use Cdn77\Api\RevokeToken\Domain\Repository\RevokeTokenRepository;
use Cdn77\Api\RevokeToken\Domain\Value\ProductId;
use Cdn77\Api\Tests\ORMFixtures\Api\PersonalTokenFactory;
use Cdn77\Api\Tests\ORMFixtures\Customer\CustomerFactory;
use Cdn77\Api\Tests\ORMFixtures\Disney\TokenFactory;
use Cdn77\Api\Tests\TestCase;
use Cdn77\Api\Tests\Utils\FlushAndClear;
use Cdn77\Api\Tests\Utils\TokenGenerator;
use Cdn77\Api\Tests\Utils\WithBrowser;
use Nette\Utils\Random;
use PHPUnit\Framework\Attributes\CoversClass;
use PHPUnit\Framework\Attributes\Group;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

use function sprintf;

#[Group('integration')]
#[CoversClass(DeleteControllerTest::class)]
#[CoversClass(DeleteToken::class)]
#[CoversClass(DeleteTokenHandler::class)]
#[CoversClass(RevokeTokenRepository::class)]
final class DeleteControllerTest extends TestCase
{
    use WithBrowser;

    public function testRemove(): void
    {
        $customerId = CustomerUuid::disney();
        $productId = ProductId::fromString(ProductId::EspnLive);
        $key = Random::generate(256);

        $entityManager = $this->entityManager();

        $customer = CustomerFactory::new($entityManager)->create(
            customerUuid: $customerId,
        );
        $entityManager->persist($customer);

        $token = TokenFactory::new($entityManager)->create($key, $productId, $customerId);
        $entityManager->persist($token);

        $personalToken = PersonalTokenFactory::new($entityManager)->create($customer->getId());
        $entityManager->persist($personalToken);

        FlushAndClear::do($entityManager);

        $bearerToken = TokenGenerator::generate($personalToken->getId(), 'validClapApiBearerTokenIs32*Char');

        $response = $this->performHttpRequest(
            Request::METHOD_DELETE,
            sprintf('/v3/revoked-token/%s/items/%s', $productId->toString(), $key),
            $this->prepareAuthHeaders($bearerToken),
        );

        self::assertHttpCode(Response::HTTP_OK, $response);
    }
}
