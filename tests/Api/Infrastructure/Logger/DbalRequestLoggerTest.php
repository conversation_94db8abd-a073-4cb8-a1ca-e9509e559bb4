<?php

declare(strict_types=1);

namespace Cdn77\Api\Tests\Api\Infrastructure\Logger;

use Cdn77\Api\Api\Infrastructure\Logger\DbalRequestLogger;
use Cdn77\Api\Core\Domain\Entity\Customer\CustomerId;
use Cdn77\Api\Tests\TestCase;
use Cdn77\Api\Tests\Utils\AuthenticationType;
use Cdn77\Api\Tests\Utils\WithBrowser;
use Generator;
use PHPUnit\Framework\Attributes\CoversClass;
use PHPUnit\Framework\Attributes\DataProvider;
use PHPUnit\Framework\Attributes\Group;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

use function Safe\gethostname;

#[CoversClass(DbalRequestLogger::class)]
#[Group('integration')]
final class DbalRequestLoggerTest extends TestCase
{
    use WithBrowser;

    #[DataProvider('requestProvider')]
    public function testSaveRequestLogs(
        string $method,
        string $endpoint,
        AuthenticationType $authType,
        string|null $tokenId,
        int $expectedResponseCode,
        CustomerId|null $expectedCustomerId,
        CustomerId|null $expectedAffectedCustomerId,
    ): void {
        $response = $this->performHttpRequest(
            $method,
            $endpoint,
            $this->getAuthHeadersForAuthType($authType),
        );

        $doctrineDbal = $this->getDatabaseConnection();
        $dbRequest = $doctrineDbal->fetchAssociative(
            <<<'PSQL'
SELECT response_code, endpoint, method, token_id, host, account_id, affected_customer_id
FROM api.request 
PSQL,
        );

        if ($endpoint === '/v3/ping') {
            self::assertFalse($dbRequest);

            return;
        }

        self::assertIsArray($dbRequest);
        self::assertIsInt($dbRequest[DbalRequestLogger::ColumnResponseCode]);
        self::assertHttpCode($expectedResponseCode, $response);
        self::assertSame($expectedResponseCode, $dbRequest[DbalRequestLogger::ColumnResponseCode]);
        self::assertSame($endpoint, $dbRequest[DbalRequestLogger::ColumnEndpoint]);
        self::assertSame($method, $dbRequest[DbalRequestLogger::ColumnMethod]);
        self::assertSame($tokenId, $dbRequest[DbalRequestLogger::ColumnTokenId]);
        self::assertSame(gethostname(), $dbRequest[DbalRequestLogger::ColumnHost]);
        self::assertSame($expectedCustomerId?->toInt(), $dbRequest[DbalRequestLogger::ColumnAccountId]);
        self::assertSame(
            $expectedAffectedCustomerId?->toInt(),
            $dbRequest[DbalRequestLogger::ColumnAffectedCustomerId],
        );
    }

    /** @return Generator<string, array{
     *     string,
     *     string,
     *     AuthenticationType,
     *     string|null,
     *     int,
     *     CustomerId|null,
     *     CustomerId|null
     * }>
     */
    public static function requestProvider(): Generator
    {
        $internalCustomerId = CustomerId::fromInteger(11001);
        $customerId = CustomerId::fromInteger(10001);
        $teamMemberId = CustomerId::fromInteger(15687);

        yield 'internal auth' => [
            Request::METHOD_GET,
            '/v3/internal/ping',
            AuthenticationType::InternalToken,
            'fdca862e-c0fc-4cb8-85a0-a51e195efc3e',
            Response::HTTP_NO_CONTENT,
            $internalCustomerId,
            $internalCustomerId,
        ];

        yield 'internal non-internal-auth' => [
            Request::METHOD_GET,
            '/v3/internal/ping',
            AuthenticationType::PersonalToken,
            null,
            Response::HTTP_FORBIDDEN,
            null,
            null,
        ];

        yield 'personal token auth' => [
            Request::METHOD_GET,
            '/v3/cdn',
            AuthenticationType::PersonalToken,
            '4bce8b8b-d8c2-4357-809c-622e72f221a4',
            Response::HTTP_OK,
            $customerId,
            $customerId,
        ];

        yield 'team member personal token auth' => [
            Request::METHOD_GET,
            '/v3/cdn',
            AuthenticationType::PersonalTokenTeamMember,
            '11dbce84-f925-437b-b370-ebec46438444',
            Response::HTTP_OK,
            $teamMemberId,
            $customerId,
        ];

        yield 'personal token with public id auth' => [
            Request::METHOD_GET,
            '/v3/cdn',
            AuthenticationType::PersonalTokenWithPublicId,
            '4bce8b8b-d8c2-4357-809c-622e72f221a4',
            Response::HTTP_OK,
            $customerId,
            $customerId,
        ];

        yield 'token non-auth' => [
            Request::METHOD_GET,
            '/v3/cdn',
            AuthenticationType::None,
            null,
            Response::HTTP_UNAUTHORIZED,
            null,
            null,
        ];

        yield 'public non-auth' => [
            Request::METHOD_GET,
            '/v3/ping',
            AuthenticationType::None,
            null,
            Response::HTTP_NO_CONTENT,
            null,
            null,
        ];
    }
}
