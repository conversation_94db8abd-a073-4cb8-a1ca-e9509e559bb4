<?php

declare(strict_types=1);

namespace Cdn77\Api\Tests\Ssl\Domain\Command;

use Cdn77\Api\Core\Domain\Authorization\CustomerContextStore;
use Cdn77\Api\Core\Domain\Customer\CustomerContext;
use Cdn77\Api\Core\Domain\Entity\Customer\AccountFlags;
use Cdn77\Api\Core\Domain\Entity\Customer\Customer;
use Cdn77\Api\Core\Domain\Entity\Customer\CustomerUuid;
use Cdn77\Api\Core\Domain\Repository\Customer\AccountFlagsRepository;
use Cdn77\Api\Core\Domain\Repository\TeamMember\TeamMemberAccessConfigurationRepository;
use Cdn77\Api\Core\Domain\TeamMember\TeamMemberAccessConfigurator;
use Cdn77\Api\Core\Infrastructure\NxgApi\FakeNxgApi;
use Cdn77\Api\Customer\Domain\Exception\AccountFlagsNotFound;
use Cdn77\Api\Ssl\Domain\Command\CreateSsl;
use Cdn77\Api\Ssl\Domain\Command\CreateSslHandler;
use Cdn77\Api\Ssl\Domain\Enum\SslType;
use Cdn77\Api\Ssl\Domain\Exception\InvalidCertificate;
use Cdn77\Api\Ssl\Domain\Exception\SslAlreadyExists;
use Cdn77\Api\Ssl\Domain\Factory\SslFactory;
use Cdn77\Api\Ssl\Domain\Finder\SslFinder;
use Cdn77\Api\Ssl\Domain\Repository\SslDomainRepository;
use Cdn77\Api\Ssl\Domain\Repository\SslRepository;
use Cdn77\Api\Ssl\Domain\Value\Certificate;
use Cdn77\Api\Ssl\Domain\Value\PrivateKey;
use Cdn77\Api\Tests\TestCase;
use Cdn77\TestUtils\Stub;
use DateTimeImmutable;
use Lcobucci\Clock\FrozenClock;
use Mockery;
use PHPUnit\Framework\Attributes\CoversClass;
use PHPUnit\Framework\Attributes\Group;
use Psr\Log\LoggerInterface;

#[CoversClass(CreateSslHandler::class)]
#[CoversClass(CreateSsl::class)]
#[Group('unit')]
final class CreateSslHandlerTest extends TestCase
{
    /**
     * @throws InvalidCertificate
     * @throws AccountFlagsNotFound
     */
    public function testCreateSsl(): void
    {
        $now = new DateTimeImmutable('2017-12-12 12:00');
        $certificate = self::getParsableCertificate();
        $customerUuid = CustomerUuid::new();
        $customer = Stub::create(
            Customer::class,
            [
                Customer::FieldId => 12345,
                Customer::FieldNewId => $customerUuid->toUid(),
            ],
        );
        $accountFlags = Stub::create(AccountFlags::class, [AccountFlags::FieldReseller => false]);
        $privateKey = self::getMatchingPrivateKey();
        $sslType = SslType::Sni;

        $clock = new FrozenClock($now);
        $customerContextStore = new CustomerContextStore();
        $accountFlagsRepository = Mockery::mock(AccountFlagsRepository::class);
        $logger = Mockery::mock(LoggerInterface::class);
        $sslFinder = Mockery::mock(SslFinder::class);
        $sslDomainRepository = Mockery::mock(SslDomainRepository::class);
        $sslRepository = Mockery::mock(SslRepository::class);
        $sslFactory = new SslFactory($sslDomainRepository, $sslRepository);
        $teamMemberAccessConfigurationRepository = Mockery::mock(TeamMemberAccessConfigurationRepository::class);
        $teamMemberAccessConfigurator = new TeamMemberAccessConfigurator($teamMemberAccessConfigurationRepository);

        $customerContextStore->store(new CustomerContext($customer));

        $accountFlagsRepository
            ->expects('getForCustomer')
            ->with($customer->getId())
            ->andReturn($accountFlags);
        $sslDomainRepository->shouldReceive('add')
            ->twice();
        $sslFinder->shouldReceive('existsForCustomer')
            ->once()
            ->with($certificate, $customer->getId())
            ->andReturn(false);
        $sslRepository->shouldReceive('add')
            ->once();

        $command = CreateSsl::create($customerUuid, $certificate, $privateKey, $sslType);
        $commandHandler = new CreateSslHandler(
            $accountFlagsRepository,
            $clock,
            $customerContextStore,
            new FakeNxgApi(),
            $logger,
            $sslFactory,
            $sslFinder,
            $teamMemberAccessConfigurator,
        );

        $commandHandler->handle($command);
    }

    /**
     * @throws InvalidCertificate
     * @throws AccountFlagsNotFound
     */
    public function testCreateDuplicateSsl(): void
    {
        $now = new DateTimeImmutable('2019-12-12 12:00');
        $certificate = self::getParsableCertificate();
        $customerUuid = CustomerUuid::new();
        $customer = Stub::create(
            Customer::class,
            [
                Customer::FieldId => 12345,
                Customer::FieldNewId => $customerUuid->toUid(),
            ],
        );
        $accountFlags = Stub::create(AccountFlags::class);
        $privateKey = self::getMatchingPrivateKey();
        $sslType = SslType::Sni;

        $clock = new FrozenClock($now);
        $customerContextStore = new CustomerContextStore();
        $accountFlagsRepository = Mockery::mock(AccountFlagsRepository::class);
        $logger = Mockery::mock(LoggerInterface::class);
        $sslFinder = Mockery::mock(SslFinder::class);
        $sslDomainRepository = Mockery::mock(SslDomainRepository::class);
        $sslRepository = Mockery::mock(SslRepository::class);
        $sslFactory = new SslFactory($sslDomainRepository, $sslRepository);
        $teamMemberAccessConfigurationRepository = Mockery::mock(TeamMemberAccessConfigurationRepository::class);
        $teamMemberAccessConfigurator = new TeamMemberAccessConfigurator($teamMemberAccessConfigurationRepository);

        $customerContextStore->store(new CustomerContext($customer));

        $accountFlagsRepository
            ->expects('getForCustomer')
            ->with($customer->getId())
            ->andReturn($accountFlags);

        $sslFinder->shouldReceive('existsForCustomer')
            ->once()
            ->with($certificate, $customer->getId())
            ->andReturn(true);

        $command = CreateSsl::create($customerUuid, $certificate, $privateKey, $sslType);
        $commandHandler = new CreateSslHandler(
            $accountFlagsRepository,
            $clock,
            $customerContextStore,
            new FakeNxgApi(),
            $logger,
            $sslFactory,
            $sslFinder,
            $teamMemberAccessConfigurator,
        );

        $this->expectException(SslAlreadyExists::class);

        $commandHandler->handle($command);
    }

    private static function getParsableCertificate(): Certificate
    {
        return Certificate::fromString(
            <<<'CERT'
            -----BEGIN CERTIFICATE-----
            MIIFWDCCBECgAwIBAgIRALSD3eSn26Cc6AtsE7e4ZdEwDQYJKoZIhvcNAQELBQAw
            gZAxCzAJBgNVBAYTAkdCMRswGQYDVQQIExJHcmVhdGVyIE1hbmNoZXN0ZXIxEDAO
            BgNVBAcTB1NhbGZvcmQxGjAYBgNVBAoTEUNPTU9ETyBDQSBMaW1pdGVkMTYwNAYD
            VQQDEy1DT01PRE8gUlNBIERvbWFpbiBWYWxpZGF0aW9uIFNlY3VyZSBTZXJ2ZXIg
            Q0EwHhcNMTcxMDA5MDAwMDAwWhcNMTgxMDA5MjM1OTU5WjBVMSEwHwYDVQQLExhE
            b21haW4gQ29udHJvbCBWYWxpZGF0ZWQxFDASBgNVBAsTC1Bvc2l0aXZlU1NMMRow
            GAYDVQQDExF2cGFpZC5ydGIyd2ViLmNvbTCCASIwDQYJKoZIhvcNAQEBBQADggEP
            ADCCAQoCggEBAL9lMxJVnJmtkrQhcCq++qLukQiWY7Tra+5or6CO36DwaBcXNQwD
            91f01D1URIYNYS6inr6FWl/DeYkc5lxA7ajK9wEQWz99ZxyOUI8qsu86KAWVtOYU
            tfjhBTYdy5pUiualQxGWM++4gRxODKk6L2xhnXIbYFFlsSAvBEDQyhFYeE/7PTIY
            y1xhVl4Y2SQnmtuTpfmTqS2BxsHnUIrYos0AvmS+PIDaRXnQnsPe5JbU6bKXoNO6
            K6q40fnfJT/EiQcIHZaSETsdqNFSYSCckHRvFDRd1Bh5tYBgj5L+lO9rN/hPptJ/
            KDvoVpgd10l7ZEBcLFHMmdF1fvp/glMYKfkCAwEAAaOCAeUwggHhMB8GA1UdIwQY
            MBaAFJCvajqUWgvYkOoSVnPfQ7Q6KNrnMB0GA1UdDgQWBBQwIPFHz1S9OfuxuguQ
            iUXdQEjwODAOBgNVHQ8BAf8EBAMCBaAwDAYDVR0TAQH/BAIwADAdBgNVHSUEFjAU
            BggrBgEFBQcDAQYIKwYBBQUHAwIwTwYDVR0gBEgwRjA6BgsrBgEEAbIxAQICBzAr
            MCkGCCsGAQUFBwIBFh1odHRwczovL3NlY3VyZS5jb21vZG8uY29tL0NQUzAIBgZn
            gQwBAgEwVAYDVR0fBE0wSzBJoEegRYZDaHR0cDovL2NybC5jb21vZG9jYS5jb20v
            Q09NT0RPUlNBRG9tYWluVmFsaWRhdGlvblNlY3VyZVNlcnZlckNBLmNybDCBhQYI
            KwYBBQUHAQEEeTB3ME8GCCsGAQUFBzAChkNodHRwOi8vY3J0LmNvbW9kb2NhLmNv
            bS9DT01PRE9SU0FEb21haW5WYWxpZGF0aW9uU2VjdXJlU2VydmVyQ0EuY3J0MCQG
            CCsGAQUFBzABhhhodHRwOi8vb2NzcC5jb21vZG9jYS5jb20wMwYDVR0RBCwwKoIR
            dnBhaWQucnRiMndlYi5jb22CFXd3dy52cGFpZC5ydGIyd2ViLmNvbTANBgkqhkiG
            9w0BAQsFAAOCAQEAGeQBJuLUVNFJS/a4rV5Bkb7RoPCaKazoQQXuQ66D5+BDT881
            MzD4n4yMe9yPlkPL56sRGvccb3B5izmhVzYM1+02inQeNAvg43Lb7/ceTk3L8RUh
            +VGybdE3fngJY9Itlv+Mxt5YK672YXwyZwxKypd1Z5TwYxd5Niiy9DS8R9Y9SUwM
            lrg++xxJzTVvDmptBxNYdRo1uF+XiRxlPaqsw207oENtMHbiIuk6rTx+0ul4r2Qg
            axRAOYVBw199EpWdtBNaFYY8B5Djt9TI5u3wJCq4XwLw7zKYqo0H9OJORIiLdl7v
            /Xt/2pUUwNmQrPvb5yJnv8RnLUAUbxmgaQWjLg==
            -----END CERTIFICATE-----
            CERT,
        );
    }

    private static function getMatchingPrivateKey(): PrivateKey
    {
        return PrivateKey::fromString(
            <<<'KEY'
***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
            KEY,
        );
    }
}
