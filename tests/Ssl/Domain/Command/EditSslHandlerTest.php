<?php

declare(strict_types=1);

namespace Cdn77\Api\Tests\Ssl\Domain\Command;

use Cdn77\Api\Core\Domain\Entity\AddOn\AddOn;
use Cdn77\Api\Core\Domain\Entity\Cdn\Cdn;
use Cdn77\Api\Core\Domain\Entity\Customer\Customer;
use Cdn77\Api\Core\Domain\Entity\Customer\CustomerId;
use Cdn77\Api\Core\Domain\Entity\Ssl\Ssl;
use Cdn77\Api\Core\Domain\Entity\Ssl\SslDomain;
use Cdn77\Api\Core\Domain\Exception\Customer\CustomerNotFound;
use Cdn77\Api\Core\Domain\NxgApi\NxgApi;
use Cdn77\Api\Core\Domain\Repository\CustomerRepository;
use Cdn77\Api\Core\Domain\Repository\SslRepository;
use Cdn77\Api\Core\Domain\Resolver\AffectedCustomerResolver;
use Cdn77\Api\Core\Domain\Value\AccessRestriction\AccessType;
use Cdn77\Api\Ssl\Domain\Command\EditSsl;
use Cdn77\Api\Ssl\Domain\Command\EditSslHandler;
use Cdn77\Api\Ssl\Domain\Contract\SslId;
use Cdn77\Api\Ssl\Domain\Dto\EditedSsl;
use Cdn77\Api\Ssl\Domain\Enum\SslType;
use Cdn77\Api\Ssl\Domain\Exception\FailedToUpdateSsl;
use Cdn77\Api\Ssl\Domain\Exception\InvalidCertificate;
use Cdn77\Api\Ssl\Domain\Exception\SslAlreadyExists;
use Cdn77\Api\Ssl\Domain\Exception\SslNotFound;
use Cdn77\Api\Ssl\Domain\Finder\SslFinder;
use Cdn77\Api\Ssl\Domain\Repository\CdnRepository;
use Cdn77\Api\Ssl\Domain\Repository\SslDomainRepository;
use Cdn77\Api\Ssl\Domain\SetupSslDomains;
use Cdn77\Api\Ssl\Domain\Validation\SslAccessValidator;
use Cdn77\Api\Ssl\Domain\Value\Certificate;
use Cdn77\Api\Ssl\Domain\Value\PrivateKey;
use Cdn77\Api\Ssl\Domain\Value\SslCname;
use Cdn77\Api\Tests\TestCase;
use Cdn77\TestUtils\Stub;
use DateTimeImmutable;
use InvalidArgumentException;
use Lcobucci\Clock\FrozenClock;
use Mockery;
use PHPUnit\Framework\Attributes\CoversClass;
use PHPUnit\Framework\Attributes\Group;
use Psr\Log\LoggerInterface;
use Psr\Log\NullLogger;
use Symfony\Component\Uid\Uuid;

use function BenTools\IterableFunctions\iterable_map;
use function BenTools\IterableFunctions\iterable_to_array;

#[CoversClass(EditSslHandler::class)]
#[CoversClass(EditSsl::class)]
#[Group('unit')]
final class EditSslHandlerTest extends TestCase
{
    /**
     * @throws CustomerNotFound
     * @throws FailedToUpdateSsl
     * @throws InvalidCertificate
     * @throws SslAlreadyExists
     * @throws SslNotFound
     * @throws InvalidArgumentException
     */
    public function testEditSsl(): void
    {
        $now = new DateTimeImmutable('2017-12-12 12:00');
        $sslIdString = '2d285645-e22e-4af9-be28-d5c00d052d57';
        $sslId = SslId::fromString($sslIdString);
        $assignedCdnA = Stub::create(Cdn::class, [Cdn::FieldId => 123456]);
        $assignedCdnB = Stub::create(Cdn::class, [Cdn::FieldId => 654321]);
        $customerId = CustomerId::fromInteger(12345);
        $customer = Stub::create(Customer::class, [Customer::FieldId => $customerId->toInt()]);
        $ssl = Stub::create(
            Ssl::class,
            [
                Ssl::FieldAddOn => Stub::create(AddOn::class, [AddOn::FieldCustomer => $customer]),
                Ssl::FieldId => Uuid::fromString($sslIdString),
                Ssl::FieldLegacyId => 123456,
                Ssl::FieldCertificate => 'cert',
                Ssl::FieldPrivateKey => 'key',
                Ssl::FieldType => SslType::Sni,
            ],
        );
        $oldSslDomainA = Stub::create(SslDomain::class, ['domain' => 'example.org', 'ssl' => $ssl]);
        $oldSslDomainB = Stub::create(SslDomain::class, ['domain' => 'vpaid.rtb2web.com', 'ssl' => $ssl]);
        $certificate = self::getParsableCertificate();
        $customer = Stub::create(Customer::class, [Customer::FieldId => 12345]);
        $privateKey = self::getMatchingPrivateKey();
        $sslType = SslType::Sni;

        $clock = new FrozenClock($now);
        $cdnRepository = Mockery::mock(CdnRepository::class);
        $nxgApi = Mockery::mock(NxgApi::class);
        $customerRepository = Mockery::mock(CustomerRepository::class);
        $affectedCustomerResolver = new AffectedCustomerResolver($customerRepository);
        $logger = Mockery::mock(LoggerInterface::class);
        $sslAccessValidator = Mockery::mock(SslAccessValidator::class);
        $sslFinder = Mockery::mock(SslFinder::class);
        $sslDomainRepository = Mockery::mock(SslDomainRepository::class);
        $setupSslDomains = new SetupSslDomains($sslDomainRepository);
        $sslRepository = Mockery::mock(SslRepository::class);

        $customerRepository->shouldReceive('get')
            ->once()
            ->with($customerId)
            ->andReturn($customer);

        $sslRepository->shouldReceive('getForType')
            ->once()
            ->with($sslId, $sslType)
            ->andReturn($ssl);

        $sslAccessValidator->shouldReceive('isAllowed')
            ->with($ssl, $customerId, AccessType::SslEdit)
            ->once()
            ->andReturn(true);

        $sslFinder->shouldReceive('existsForCustomer')
            ->once()
            ->with($certificate, $customer->getId())
            ->andReturn(false);

        $sslDomainRepository->shouldReceive('findForSsl')
            ->once()
            ->andYield($oldSslDomainA, $oldSslDomainB);

        $sslDomainRepository->shouldReceive('remove')
            ->once()
            ->with($oldSslDomainA);

        $sslDomainRepository->shouldReceive('add')
            ->once();

        $cdnRepository->shouldReceive('findForSsl')
            ->once()
            ->andYield($assignedCdnA, $assignedCdnB);

        $nxgApi->shouldReceive('changeCertificateForMultipleCdns')
            ->once()
            ->with([$assignedCdnA->getId(), $assignedCdnB->getId()], $certificate, $privateKey);
        $nxgApi->shouldReceive('storePrivateKey')
            ->once()
            ->with($customerId, $sslId, $certificate, $privateKey);

        $command = EditSsl::forCustomerAndType(
            $customerId,
            new EditedSsl($certificate, $privateKey),
            $ssl->id(),
            $sslType,
        );
        $commandHandler = new EditSslHandler(
            $affectedCustomerResolver,
            $cdnRepository,
            $customerRepository,
            $clock,
            $logger,
            $nxgApi,
            $setupSslDomains,
            $sslAccessValidator,
            $sslFinder,
            $sslRepository,
        );

        $sslDetail = $commandHandler->handle($command);

        self::assertSame($certificate->get(), $sslDetail->certificate->get());
        self::assertSame(
            ['vpaid.rtb2web.com', 'www.vpaid.rtb2web.com'],
            $sslDetail->cnames->map(static fn (SslCname $cname): string => $cname->value)->toArray(),
        );
        self::assertSame(
            [$assignedCdnA->getId()->toInt(), $assignedCdnB->getId()->toInt()],
            iterable_to_array(
                iterable_map(
                    $sslDetail->assignedCdns,
                    static fn (Cdn $assignedCdn): int => $assignedCdn->getId()->toInt(),
                ),
            ),
        );
        self::assertDateEquals(new DateTimeImmutable('2018-10-09T23:59:59+00:00'), $sslDetail->expiresAt);
    }

    /**
     * @throws CustomerNotFound
     * @throws FailedToUpdateSsl
     * @throws InvalidCertificate
     * @throws SslAlreadyExists
     * @throws SslNotFound
     * @throws InvalidArgumentException
     */
    public function testEditWithDuplicateCertificate(): void
    {
        $now = new DateTimeImmutable('2017-12-12 12:00');
        $sslIdString = '2d285645-e22e-4af9-be28-d5c00d052d57';
        $sslId = SslId::fromString($sslIdString);
        $customerId = CustomerId::fromInteger(12345);
        $customer = Stub::create(Customer::class, [Customer::FieldId => $customerId->toInt()]);
        $ssl = Stub::create(
            Ssl::class,
            [
                Ssl::FieldAddOn => Stub::create(AddOn::class, [AddOn::FieldCustomer => $customer]),
                Ssl::FieldId => Uuid::fromString($sslIdString),
                Ssl::FieldLegacyId => 123456,
                Ssl::FieldCertificate => 'cert',
                Ssl::FieldPrivateKey => 'key',
            ],
        );
        $certificate = self::getParsableCertificate();
        $customer = Stub::create(Customer::class, [Customer::FieldId => 12345]);
        $privateKey = self::getMatchingPrivateKey();
        $sslType = SslType::Sni;

        $clock = new FrozenClock($now);
        $cdnRepository = Mockery::mock(CdnRepository::class);
        $nxgApi = Mockery::mock(NxgApi::class);
        $customerRepository = Mockery::mock(CustomerRepository::class);
        $affectedCustomerResolver = new AffectedCustomerResolver($customerRepository);
        $logger = new NullLogger();
        $sslAccessValidator = Mockery::mock(SslAccessValidator::class);
        $sslFinder = Mockery::mock(SslFinder::class);
        $sslDomainRepository = Mockery::mock(SslDomainRepository::class);
        $setupSslDomains = new SetupSslDomains($sslDomainRepository);
        $sslRepository = Mockery::mock(SslRepository::class);

        $customerRepository->shouldReceive('get')
            ->once()
            ->with($customerId)
            ->andReturn($customer);

        $sslRepository->shouldReceive('getForType')
            ->once()
            ->with($sslId, $sslType)
            ->andReturn($ssl);

        $sslAccessValidator->shouldReceive('isAllowed')
            ->with($ssl, $customerId, AccessType::SslEdit)
            ->once()
            ->andReturn(true);

        $sslFinder->shouldReceive('existsForCustomer')
            ->once()
            ->with($certificate, $customer->getId())
            ->andReturn(true);

        $command = EditSsl::forCustomerAndType(
            $customerId,
            new EditedSsl($certificate, $privateKey),
            $ssl->id(),
            $sslType,
        );
        $commandHandler = new EditSslHandler(
            $affectedCustomerResolver,
            $cdnRepository,
            $customerRepository,
            $clock,
            $logger,
            $nxgApi,
            $setupSslDomains,
            $sslAccessValidator,
            $sslFinder,
            $sslRepository,
        );

        $this->expectException(SslAlreadyExists::class);
        $commandHandler->handle($command);
    }

    private static function getParsableCertificate(): Certificate
    {
        return Certificate::fromString(
            <<<'CERT'
            -----BEGIN CERTIFICATE-----
            MIIFWDCCBECgAwIBAgIRALSD3eSn26Cc6AtsE7e4ZdEwDQYJKoZIhvcNAQELBQAw
            gZAxCzAJBgNVBAYTAkdCMRswGQYDVQQIExJHcmVhdGVyIE1hbmNoZXN0ZXIxEDAO
            BgNVBAcTB1NhbGZvcmQxGjAYBgNVBAoTEUNPTU9ETyBDQSBMaW1pdGVkMTYwNAYD
            VQQDEy1DT01PRE8gUlNBIERvbWFpbiBWYWxpZGF0aW9uIFNlY3VyZSBTZXJ2ZXIg
            Q0EwHhcNMTcxMDA5MDAwMDAwWhcNMTgxMDA5MjM1OTU5WjBVMSEwHwYDVQQLExhE
            b21haW4gQ29udHJvbCBWYWxpZGF0ZWQxFDASBgNVBAsTC1Bvc2l0aXZlU1NMMRow
            GAYDVQQDExF2cGFpZC5ydGIyd2ViLmNvbTCCASIwDQYJKoZIhvcNAQEBBQADggEP
            ADCCAQoCggEBAL9lMxJVnJmtkrQhcCq++qLukQiWY7Tra+5or6CO36DwaBcXNQwD
            91f01D1URIYNYS6inr6FWl/DeYkc5lxA7ajK9wEQWz99ZxyOUI8qsu86KAWVtOYU
            tfjhBTYdy5pUiualQxGWM++4gRxODKk6L2xhnXIbYFFlsSAvBEDQyhFYeE/7PTIY
            y1xhVl4Y2SQnmtuTpfmTqS2BxsHnUIrYos0AvmS+PIDaRXnQnsPe5JbU6bKXoNO6
            K6q40fnfJT/EiQcIHZaSETsdqNFSYSCckHRvFDRd1Bh5tYBgj5L+lO9rN/hPptJ/
            KDvoVpgd10l7ZEBcLFHMmdF1fvp/glMYKfkCAwEAAaOCAeUwggHhMB8GA1UdIwQY
            MBaAFJCvajqUWgvYkOoSVnPfQ7Q6KNrnMB0GA1UdDgQWBBQwIPFHz1S9OfuxuguQ
            iUXdQEjwODAOBgNVHQ8BAf8EBAMCBaAwDAYDVR0TAQH/BAIwADAdBgNVHSUEFjAU
            BggrBgEFBQcDAQYIKwYBBQUHAwIwTwYDVR0gBEgwRjA6BgsrBgEEAbIxAQICBzAr
            MCkGCCsGAQUFBwIBFh1odHRwczovL3NlY3VyZS5jb21vZG8uY29tL0NQUzAIBgZn
            gQwBAgEwVAYDVR0fBE0wSzBJoEegRYZDaHR0cDovL2NybC5jb21vZG9jYS5jb20v
            Q09NT0RPUlNBRG9tYWluVmFsaWRhdGlvblNlY3VyZVNlcnZlckNBLmNybDCBhQYI
            KwYBBQUHAQEEeTB3ME8GCCsGAQUFBzAChkNodHRwOi8vY3J0LmNvbW9kb2NhLmNv
            bS9DT01PRE9SU0FEb21haW5WYWxpZGF0aW9uU2VjdXJlU2VydmVyQ0EuY3J0MCQG
            CCsGAQUFBzABhhhodHRwOi8vb2NzcC5jb21vZG9jYS5jb20wMwYDVR0RBCwwKoIR
            dnBhaWQucnRiMndlYi5jb22CFXd3dy52cGFpZC5ydGIyd2ViLmNvbTANBgkqhkiG
            9w0BAQsFAAOCAQEAGeQBJuLUVNFJS/a4rV5Bkb7RoPCaKazoQQXuQ66D5+BDT881
            MzD4n4yMe9yPlkPL56sRGvccb3B5izmhVzYM1+02inQeNAvg43Lb7/ceTk3L8RUh
            +VGybdE3fngJY9Itlv+Mxt5YK672YXwyZwxKypd1Z5TwYxd5Niiy9DS8R9Y9SUwM
            lrg++xxJzTVvDmptBxNYdRo1uF+XiRxlPaqsw207oENtMHbiIuk6rTx+0ul4r2Qg
            axRAOYVBw199EpWdtBNaFYY8B5Djt9TI5u3wJCq4XwLw7zKYqo0H9OJORIiLdl7v
            /Xt/2pUUwNmQrPvb5yJnv8RnLUAUbxmgaQWjLg==
            -----END CERTIFICATE-----
            CERT,
        );
    }

    private static function getMatchingPrivateKey(): PrivateKey
    {
        return PrivateKey::fromString(
            <<<'KEY'
***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
            KEY,
        );
    }
}
