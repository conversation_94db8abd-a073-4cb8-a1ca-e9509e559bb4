<?php

declare(strict_types=1);

namespace Cdn77\Api\Tests\Ssl\Domain\Command;

use Cdn77\Api\Core\Domain\Entity\Cdn\CdnId;
use Cdn77\Api\Core\Domain\Entity\Customer\Customer;
use Cdn77\Api\Core\Domain\Entity\Customer\CustomerId;
use Cdn77\Api\Core\Domain\Entity\Ssl\Ssl;
use Cdn77\Api\Core\Domain\Repository\CustomerRepository;
use Cdn77\Api\Core\Domain\Repository\SslRepository;
use Cdn77\Api\Core\Domain\Resolver\AffectedCustomerResolver;
use Cdn77\Api\Core\Infrastructure\NxgApi\FakeNxgApi;
use Cdn77\Api\Ssl\Domain\Command\DeleteSsl;
use Cdn77\Api\Ssl\Domain\Command\DeleteSslHandler;
use Cdn77\Api\Ssl\Domain\Contract\SslId;
use Cdn77\Api\Ssl\Domain\Enum\SslType;
use Cdn77\Api\Ssl\Domain\Exception\SslNotFound;
use Cdn77\Api\Ssl\Domain\Exception\SslRemovalFailed;
use Cdn77\Api\Ssl\Domain\Finder\SslAssignedCdnIdsFinder;
use Cdn77\Api\Ssl\Domain\Validation\SslAccessValidator;
use Cdn77\Api\Tests\TestCase;
use Cdn77\TestUtils\Stub;
use DateTimeImmutable;
use Lcobucci\Clock\FrozenClock;
use Mockery;
use PHPUnit\Framework\Attributes\CoversClass;
use PHPUnit\Framework\Attributes\Group;
use Psr\Log\LoggerInterface;

#[CoversClass(DeleteSslHandler::class)]
#[CoversClass(DeleteSsl::class)]
#[Group('unit')]
final class DeleteSslHandlerTest extends TestCase
{
    /**
     * @throws SslNotFound
     * @throws SslRemovalFailed
     */
    public function testHandle(): void
    {
        $now = new DateTimeImmutable('2021-02-23 17:00');
        $customerRepository = Mockery::mock(CustomerRepository::class);
        $affectedCustomerResolver = new AffectedCustomerResolver($customerRepository);
        $logger = Mockery::mock(LoggerInterface::class);
        $sslAccessValidator = Mockery::mock(SslAccessValidator::class);
        $sslAssignedCdnIdsFinder = Mockery::mock(SslAssignedCdnIdsFinder::class);
        $sslRepository = Mockery::mock(SslRepository::class);
        $clock = new FrozenClock($now);

        $customerId = CustomerId::fromInteger(1);
        $sslId = SslId::fromString('835f35e1-355d-4aca-ac1d-ecf729d58d21');
        $deleteSsl = new DeleteSsl($customerId, $sslId, SslType::Sni);
        $ssl = Stub::create(Ssl::class);

        $customer = Stub::create(Customer::class, [
            Customer::FieldId => $customerId->toInt(),
        ]);

        $customerRepository
            ->shouldReceive('get')
            ->once()
            ->with($customerId)
            ->andReturn($customer);

        $sslRepository
            ->shouldReceive('getForType')
            ->once()
            ->with($deleteSsl->sslId, $deleteSsl->sslType)
            ->andReturn($ssl);

        $sslAccessValidator
            ->shouldReceive('isAllowed')
            ->once()
            ->andReturn(true);

        $sslAssignedCdnIdsFinder
            ->shouldReceive('findForSslId')
            ->once()
            ->andReturn(CdnId::fromIntegers([]));

        $deleteSslHandler = new DeleteSslHandler(
            $affectedCustomerResolver,
            $clock,
            $customerRepository,
            $logger,
            new FakeNxgApi(),
            $sslAccessValidator,
            $sslAssignedCdnIdsFinder,
            $sslRepository,
        );
        $deleteSslHandler->handle($deleteSsl);

        self::assertEquals($ssl->deletedAt(), $now);
    }

    /**
     * @throws SslNotFound
     * @throws SslRemovalFailed
     */
    public function testHandleWithSslInUseException(): void
    {
        $now = new DateTimeImmutable('2021-02-23 17:00');
        $customerRepository = Mockery::mock(CustomerRepository::class);
        $affectedCustomerResolver = new AffectedCustomerResolver($customerRepository);
        $logger = Mockery::mock(LoggerInterface::class);
        $sslAccessValidator = Mockery::mock(SslAccessValidator::class);
        $sslAssignedCdnIdsFinder = Mockery::mock(SslAssignedCdnIdsFinder::class);
        $sslRepository = Mockery::mock(SslRepository::class);
        $clock = new FrozenClock($now);

        $customerId = CustomerId::fromInteger(1);
        $sslId = SslId::fromString('835f35e1-355d-4aca-ac1d-ecf729d58d21');
        $deleteSsl = new DeleteSsl($customerId, $sslId, SslType::Sni);
        $ssl = Stub::create(Ssl::class);

        $sslRepository
            ->shouldReceive('getForType')
            ->once()
            ->with($deleteSsl->sslId, $deleteSsl->sslType)
            ->andReturn($ssl);

        $sslAccessValidator
            ->shouldReceive('isAllowed')
            ->once()
            ->andReturn(true);

        $sslAssignedCdnIdsFinder
            ->shouldReceive('findForSslId')
            ->once()
            ->andReturn(CdnId::fromIntegers([123456]));

        $this->expectException(SslRemovalFailed::class);
        $this->expectExceptionMessage(
            'Certificate with id "835f35e1-355d-4aca-ac1d-ecf729d58d21" is being used by CDN resources (123456).',
        );

        $deleteSslHandler = new DeleteSslHandler(
            $affectedCustomerResolver,
            $clock,
            $customerRepository,
            $logger,
            new FakeNxgApi(),
            $sslAccessValidator,
            $sslAssignedCdnIdsFinder,
            $sslRepository,
        );
        $deleteSslHandler->handle($deleteSsl);
    }

    /**
     * @throws SslNotFound
     * @throws SslRemovalFailed
     */
    public function testHandleWithAccessDeniedException(): void
    {
        $now = new DateTimeImmutable('2021-02-23 17:00');
        $customerRepository = Mockery::mock(CustomerRepository::class);
        $affectedCustomerResolver = new AffectedCustomerResolver($customerRepository);
        $logger = Mockery::mock(LoggerInterface::class);
        $sslAccessValidator = Mockery::mock(SslAccessValidator::class);
        $sslAssignedCdnIdsFinder = Mockery::mock(SslAssignedCdnIdsFinder::class);
        $sslRepository = Mockery::mock(SslRepository::class);
        $clock = new FrozenClock($now);

        $customerId = CustomerId::fromInteger(1);
        $sslId = SslId::fromString('835f35e1-355d-4aca-ac1d-ecf729d58d21');
        $deleteSsl = new DeleteSsl($customerId, $sslId, SslType::Sni);
        $ssl = Stub::create(Ssl::class);

        $sslRepository
            ->shouldReceive('getForType')
            ->once()
            ->with($deleteSsl->sslId, $deleteSsl->sslType)
            ->andReturn($ssl);

        $sslAccessValidator
            ->shouldReceive('isAllowed')
            ->once()
            ->andReturn(false);

        $this->expectException(SslNotFound::class);
        $this->expectExceptionMessage(
            'SNI certificate with id "835f35e1-355d-4aca-ac1d-ecf729d58d21" was not found.',
        );

        $deleteSslHandler = new DeleteSslHandler(
            $affectedCustomerResolver,
            $clock,
            $customerRepository,
            $logger,
            new FakeNxgApi(),
            $sslAccessValidator,
            $sslAssignedCdnIdsFinder,
            $sslRepository,
        );
        $deleteSslHandler->handle($deleteSsl);
    }
}
