<?php

declare(strict_types=1);

namespace Cdn77\Api\Tests\Ssl\Application\Controller;

use Cdn77\Api\Core\Application\Payload\CdnReferenceSchema;
use Cdn77\Api\Core\Domain\Entity\Customer\Customer;
use Cdn77\Api\Ssl\Application\Controller\SniListController;
use Cdn77\Api\Ssl\Application\Payload\SslSchema;
use Cdn77\Api\Tests\TestCase;
use Cdn77\Api\Tests\Utils\TokenGenerator;
use Cdn77\Api\Tests\Utils\WithBrowser;
use PHPUnit\Framework\Attributes\CoversClass;
use PHPUnit\Framework\Attributes\Group;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Webmozart\Assert\Assert;

#[CoversClass(SniListController::class)]
#[Group('integration')]
final class SniListControllerTest extends TestCase
{
    use WithBrowser;

    public function testGetExistingSnis(): void
    {
        ['testing_customer' => $customer] = $this->loadFixtures();

        Assert::isInstanceOf($customer, Customer::class);

        $token = TokenGenerator::generateApplication($customer->getNewId());
        $response = $this->performHttpRequest(
            Request::METHOD_GET,
            '/v3/ssl/sni',
            $this->prepareAuthHeaders($token),
        );

        self::assertHttpCode(Response::HTTP_OK, $response);

        Assert::string($response->getContent());

        self::assertJsonResponse([
            [
                SslSchema::FieldId => '523d4567-f019-4e99-a13a-0e1e8a97ef6c',
                SslSchema::FieldCertificate =>
                    '---BEGIN CERTIFICATE---MIIEvwIBADANBgkqhkiG9w0BAQEFAAS---END CERTIFICATE---',
                SslSchema::FieldCnames => [
                    'example1.com',
                    'example2.com',
                ],
                SslSchema::FieldExpiresAt => '2022-04-04T14:53:22+00:00',
                SslSchema::FieldAssignedResources => [
                    [
                        CdnReferenceSchema::FieldId => 123456,
                        CdnReferenceSchema::FieldLabel => 'test_label_a',
                    ],
                ],
            ],
            [
                SslSchema::FieldId => 'aaaabbcc-f019-4e99-a13a-0e1e8a97ef6c',
                SslSchema::FieldCertificate =>
                    '---BEGIN CERTIFICATE---MIIEvwIBADANBgkqhkiG9w0BAQEFAAS---END CERTIFICATE---',
                SslSchema::FieldCnames => [
                    'example1.org',
                    'example2.org',
                ],
                SslSchema::FieldExpiresAt => '2022-04-04T14:53:22+00:00',
                SslSchema::FieldAssignedResources => [
                    [
                        CdnReferenceSchema::FieldId => 123456789,
                        CdnReferenceSchema::FieldLabel => 'test_label_b',
                    ],
                ],
            ],
        ], $response);
    }

    public function testGetExistingSan(): void
    {
        ['testing_customer' => $customer] = $this->loadFixtures();

        Assert::isInstanceOf($customer, Customer::class);

        $token = TokenGenerator::generateApplication($customer->getNewId());
        $response = $this->performHttpRequest(
            Request::METHOD_GET,
            '/v3/ssl/sni',
            $this->prepareAuthHeaders($token),
        );

        self::assertHttpCode(Response::HTTP_OK, $response);
        self::assertJsonResponse([], $response);
    }

    public function testNonExistingSni(): void
    {
        ['testing_customer' => $customer] =
            $this->loadFixturesFromFile(__DIR__ . '/fixtures/common.yaml');

        Assert::isInstanceOf($customer, Customer::class);

        $token = TokenGenerator::generateApplication($customer->getNewId());
        $response = $this->performHttpRequest(
            Request::METHOD_GET,
            '/v3/ssl/sni',
            $this->prepareAuthHeaders($token),
        );

        self::assertHttpCode(Response::HTTP_OK, $response);
        self::assertJsonResponse([], $response);
    }
}
