<?php

declare(strict_types=1);

namespace Cdn77\Api\Tests\Ssl\Application\Controller;

use Cdn77\Api\Core\Application\Payload\ErrorsSchema;
use Cdn77\Api\Core\Domain\Entity\Cdn\Cdn;
use Cdn77\Api\Core\Domain\Entity\Customer\Customer;
use Cdn77\Api\Core\Domain\Entity\Ssl\Ssl;
use Cdn77\Api\Ssl\Application\Controller\SniDeleteController;
use Cdn77\Api\Ssl\Domain\Value\SslErrorType;
use Cdn77\Api\Tests\TestCase;
use Cdn77\Api\Tests\Utils\TokenGenerator;
use Cdn77\Api\Tests\Utils\WithBrowser;
use PHPUnit\Framework\Attributes\CoversClass;
use PHPUnit\Framework\Attributes\Group;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Webmozart\Assert\Assert;

use function sprintf;

#[CoversClass(SniDeleteController::class)]
#[Group('integration')]
final class SniDeleteControllerTest extends TestCase
{
    use WithBrowser;

    public function testDeleteSniWithoutCdn(): void
    {
        [
            'testing_customer' => $customer,
            'test_sni_certificate_without_cdn' => $sniCertificate,
        ] = $this->loadFixtures();

        Assert::isInstanceOf($customer, Customer::class);
        Assert::isInstanceOf($sniCertificate, Ssl::class);

        $token = TokenGenerator::generateApplication($customer->getNewId());
        $response = $this->performHttpRequest(
            Request::METHOD_DELETE,
            '/v3/ssl/sni/' . $sniCertificate->id()->toString(),
            $this->prepareAuthHeaders($token),
        );

        self::assertHttpCode(Response::HTTP_NO_CONTENT, $response);
    }

    public function testDeleteSniWithDeletedCdn(): void
    {
        [
            'testing_customer' => $customer,
            'test_sni_certificate_with_deleted_cdn' => $sniCertificate,
        ] = $this->loadFixtures();

        Assert::isInstanceOf($customer, Customer::class);
        Assert::isInstanceOf($sniCertificate, Ssl::class);

        $token = TokenGenerator::generateApplication($customer->getNewId());
        $response = $this->performHttpRequest(
            Request::METHOD_DELETE,
            '/v3/ssl/sni/' . $sniCertificate->id()->toString(),
            $this->prepareAuthHeaders($token),
        );

        self::assertHttpCode(Response::HTTP_NO_CONTENT, $response);
    }

    public function testDeleteSniInUse(): void
    {
        [
            'testing_customer' => $customer,
            'test_sni_certificate' => $sniCertificate,
            'test_cdn' => $cdn,
        ] = $this->loadFixtures();

        Assert::isInstanceOf($customer, Customer::class);
        Assert::isInstanceOf($sniCertificate, Ssl::class);
        Assert::isInstanceOf($cdn, Cdn::class);

        $token = TokenGenerator::generateApplication($customer->getNewId());
        $response = $this->performHttpRequest(
            Request::METHOD_DELETE,
            '/v3/ssl/sni/' . $sniCertificate->id()->toString(),
            $this->prepareAuthHeaders($token),
        );

        self::assertHttpCode(Response::HTTP_UNPROCESSABLE_ENTITY, $response);
        self::assertJsonResponse([
            ErrorsSchema::FieldErrors => [
                sprintf(
                    'Certificate with id "%s" is being used by CDN resources (%d).',
                    $sniCertificate->id()->toString(),
                    $cdn->getId()->toString(),
                ),
            ],
            ErrorsSchema::FieldType => SslErrorType::SslIsInUseByCdn,
        ], $response);
    }

    public function testDeleteDeletedSni(): void
    {
        [
            'testing_customer' => $customer,
            'test_sni_deleted_certificate' => $sniCertificate,
        ] = $this->loadFixtures();

        Assert::isInstanceOf($customer, Customer::class);
        Assert::isInstanceOf($sniCertificate, Ssl::class);

        $token = TokenGenerator::generateApplication($customer->getNewId());
        $response = $this->performHttpRequest(
            Request::METHOD_DELETE,
            '/v3/ssl/sni/' . $sniCertificate->id()->toString(),
            $this->prepareAuthHeaders($token),
        );

        self::assertHttpCode(Response::HTTP_NOT_FOUND, $response);
        self::assertJsonResponse(
            [
                ErrorsSchema::FieldErrors => [
                    sprintf('SNI certificate with id "%s" was not found.', $sniCertificate->id()->toString()),
                ],
            ],
            $response,
        );
    }

    public function testDeleteNonExistingSni(): void
    {
        ['testing_customer' => $customer] = $this->loadFixturesFromFile(__DIR__ . '/fixtures/common.yaml');

        Assert::isInstanceOf($customer, Customer::class);

        $nonExistingId = '044f1e5b-4119-4e25-9728-d6f3a8329833';
        $token = TokenGenerator::generateApplication($customer->getNewId());
        $response = $this->performHttpRequest(
            Request::METHOD_DELETE,
            '/v3/ssl/sni/' . $nonExistingId,
            $this->prepareAuthHeaders($token),
        );

        self::assertHttpCode(Response::HTTP_NOT_FOUND, $response);
        self::assertJsonResponse([
            ErrorsSchema::FieldErrors => [sprintf('SNI certificate with id "%s" was not found.', $nonExistingId)],
        ], $response);
    }
}
