<?php

declare(strict_types=1);

namespace Cdn77\Api\Tests\Server\Domain\Dto;

use Cdn77\Api\Server\Domain\Dto\Pop;
use Cdn77\Api\Server\Domain\Value\ServerType;
use Cdn77\Api\Tests\TestCase;
use Generator;
use PHPUnit\Framework\Attributes\CoversClass;
use PHPUnit\Framework\Attributes\DataProvider;
use PHPUnit\Framework\Attributes\Group;

#[CoversClass(Pop::class)]
#[Group('unit')]
final class PopTest extends TestCase
{
    #[DataProvider('dataProvider')]
    public function testType(ServerType $expectedServerType, Pop $pop): void
    {
        self::assertSame($expectedServerType, $pop->serverType);
    }

    /** @return Generator<array<ServerType|Pop>> */
    public static function dataProvider(): Generator
    {
        yield [
            ServerType::Edge,
            Pop::fromArray(
                [
                    Pop::KeyCityCode => 'TPE',
                    Pop::KeyType => 'LB',
                    Pop::KeyLocationId => 'bangkok_5_TH',
                    Pop::KeyId => 12345,
                ],
            ),
        ];

        yield [
            ServerType::Edge,
            Pop::fromArray(
                [
                    Pop::KeyCityCode => 'CZE',
                    Pop::KeyType => '',
                    Pop::KeyLocationId => 'sydneyAU',
                    Pop::KeyId => 9873,
                ],
            ),
        ];

        yield [
            ServerType::Storage,
            Pop::fromArray(
                [
                    Pop::KeyCityCode => 'TYO',
                    Pop::KeyType => 'STORAGE',
                    Pop::KeyLocationId => 'denverUSCO',
                    Pop::KeyId => 34573,
                ],
            ),
        ];

        yield [
            ServerType::Storage,
            Pop::fromArray(
                [
                    Pop::KeyCityCode => 'OSM',
                    Pop::KeyType => 'Storage',
                    Pop::KeyLocationId => 'pragueCZ',
                    Pop::KeyId => 4343,
                ],
            ),
        ];

        yield [
            ServerType::Storage,
            Pop::fromArray(
                [
                    Pop::KeyCityCode => 'TLV',
                    Pop::KeyType => 'storage',
                    Pop::KeyLocationId => 'frankfurtDE',
                    Pop::KeyId => 3424,
                ],
            ),
        ];
    }
}
