include:
    - common.yaml

Cdn77\Api\Core\Domain\Entity\Customer\Credentials:
    other_testing_customer_credentials:
        __construct: false
        email: '<email()>'
        password: '<(@testing_customer_hashed_password->toString())>'
        fullName: Testing User
        phoneNumber: '<e164PhoneNumber()>'

Cdn77\Api\Core\Domain\Entity\Customer\Customer:
    other_testing_customer:
        __construct: false
        id: 20787
        newId: <identity(Symfony\Component\Uid\Uuid::fromString('225638fd-3241-4329-acf9-a5c742eb74d5'))>
        publicId: <identity(Symfony\Component\Uid\Uuid::fromString('31e02a27-3eb7-4cc3-bb7d-c9f286b67f3a'))>
        credentials: '@other_testing_customer_credentials'
        role: '<(Cdn77\Api\Core\Domain\Value\Customer\UserRole::User)>'
        createdAt: <identity(new DateTimeImmutable('2016-10-16'))>

Cdn77\Api\Core\Domain\Entity\Api\PersonalToken:
    other_testing_customer_api_personal_token:
        __construct: false
        customerId: '<(@other_testing_customer->getId()->toInt())>'
        uuid: <identity(Symfony\Component\Uid\Uuid::fromString('139787b1-eee8-423f-ac73-ed9f8bfb6af9'))>
        type: <identity(Cdn77\Api\Authentication\Domain\Value\PersonalTokenType::Default)>

Cdn77\Api\Core\Domain\Entity\Storage\Storage:
    testing_storage_a:
        __construct: false
        id: <identity(Symfony\Component\Uid\Uuid::fromString('db51ad25-dfe6-4c5c-8537-aff1cd74f9e9'))>
        secret: 'testingStorageAbc'
        server: '@testing_server'
        owner: '@testing_customer'
        name: assets 1
        userName: a
        password: p_a
        wwwUrl: 'testingStorageAbc.testing.location.xyz'
        creationTime: <identity(new DateTimeImmutable('2017-09-16 01:00:00'))>
    testing_storage_b:
        __construct: false
        id: <identity(Symfony\Component\Uid\Uuid::fromString('f6be8028-607e-4abb-aaae-00e5ad171b5e'))>
        secret: 'testingStorageDef'
        server: '@testing_server'
        owner: '@testing_customer'
        name: assets 2
        userName: b
        password: p_b
        wwwUrl: 'testingStorageAbc.testing.location.xyz'
        creationTime: <identity(new DateTimeImmutable('2017-09-16 00:00:00'))>
    testing_storage_c:
        __construct: false
        id: <identity(Symfony\Component\Uid\Uuid::fromString('f2ee1631-4006-4da4-b93a-c0dcab866d05'))>
        secret: 'testingStorageGhi'
        server: '@testing_server'
        owner: '@other_testing_customer'
        name: assets 3
        userName: c
        wwwUrl: 'testingStorageAbc.testing.location.xyz'
        creationTime: <identity(new DateTimeImmutable('2017-09-16'))>
    testing_storage_d:
        __construct: false
        id: <identity(Symfony\Component\Uid\Uuid::fromString('c20b6789-dbf9-4feb-8b4c-f080d5c47a3c'))>
        secret: 'testingStorageJkl'
        server: '@testing_server'
        owner: '@testing_customer'
        name: assets 4
        userName: d
        wwwUrl: 'testingStorageAbc.testing.location.xyz'
        creationTime: <identity(new DateTimeImmutable('2017-09-16'))>
        deletionTime: <identity(new DateTimeImmutable('2017-09-20'))>
