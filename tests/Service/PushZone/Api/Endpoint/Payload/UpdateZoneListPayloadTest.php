<?php

declare(strict_types=1);

namespace Cdn77\Api\Tests\Service\PushZone\Api\Endpoint\Payload;

use Cdn77\Api\Service\PushZone\Api\Endpoint\Payload\UpdateZoneListPayload;
use Cdn77\Api\Service\PushZone\Api\Endpoint\Payload\UpdateZoneListPayloadEntry;
use Cdn77\Api\Tests\TestCase;
use PHPUnit\Framework\Attributes\CoversClass;
use PHPUnit\Framework\Attributes\Group;

use function Psl\Json\decode;
use function Psl\Json\encode;

#[Group('unit')]
#[CoversClass(UpdateZoneListPayload::class)]
final class UpdateZoneListPayloadTest extends TestCase
{
    public function testSerializePayload(): void
    {
        self::assertSame(
            $this->createExpectation(),
            decode(encode($this->createPayload())),
        );
    }

    /** @return mixed[][] */
    protected function createExpectation(): array
    {
        return [
            'list' => [
                ['id' => 'id1'],
                ['id' => 'id2'],
            ],
        ];
    }

    protected function createPayload(): UpdateZoneListPayload
    {
        return new UpdateZoneListPayload([
            UpdateZoneListPayloadEntry::fromId('id1'),
            UpdateZoneListPayloadEntry::fromId('id2'),
        ]);
    }
}
