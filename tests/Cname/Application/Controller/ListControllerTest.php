<?php

declare(strict_types=1);

namespace Cdn77\Api\Tests\Cname\Application\Controller;

use Cdn77\Api\Cname\Application\Controller\ListController;
use Cdn77\Api\Core\Domain\Entity\Cname\Cname;
use Cdn77\Api\Core\Domain\Entity\Customer\Customer;
use Cdn77\Api\Tests\TestCase;
use Cdn77\Api\Tests\Utils\TokenGenerator;
use Cdn77\Api\Tests\Utils\WithBrowser;
use PHPUnit\Framework\Attributes\CoversClass;
use PHPUnit\Framework\Attributes\Group;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Webmozart\Assert\Assert;

use function Psl\Json\decode;
use function strcmp;
use function usort;

#[CoversClass(ListController::class)]
#[Group('integration')]
final class ListControllerTest extends TestCase
{
    use WithBrowser;

    public function testNoCnames(): void
    {
        ['testing_customer' => $customer] = $this->loadFixtures();

        Assert::isInstanceOf($customer, Customer::class);

        $token = TokenGenerator::generateApplication($customer->getNewId());
        $response = $this->performHttpRequest(
            Request::METHOD_GET,
            '/v3/cdn/123456789/cname',
            $this->prepareAuthHeaders($token),
        );

        self::assertHttpCode(Response::HTTP_OK, $response);
        self::assertIsString($response->getContent());
        self::assertSame([], decode($response->getContent()));
    }

    public function testMultipleCnames(): void
    {
        [
            'testing_customer' => $customer,
            'testing_cname_a' => $cnameA,
            'testing_cname_b' => $cnameB,
        ] = $this->loadFixtures();

        Assert::isInstanceOf($customer, Customer::class);
        Assert::isInstanceOf($cnameA, Cname::class);
        Assert::isInstanceOf($cnameB, Cname::class);

        $token = TokenGenerator::generateApplication($customer->getNewId());
        $response = $this->performHttpRequest(
            Request::METHOD_GET,
            '/v3/cdn/123456789/cname',
            $this->prepareAuthHeaders($token),
        );

        self::assertHttpCode(Response::HTTP_OK, $response);
        self::assertIsString($response->getContent());

        $payload = decode($response->getContent());

        Assert::isArray($payload);
        Assert::allIsArray($payload);

        usort($payload, static fn (array $a, array $b): int => strcmp($a['cname'], $b['cname']));

        self::assertCount(2, $payload);
        self::assertSame(
            [
                [
                    'id' => $cnameA->id()->toString(),
                    'cname' => $cnameA->getCname(),
                ],
                [
                    'id' => $cnameB->id()->toString(),
                    'cname' => $cnameB->getCname(),
                ],
            ],
            $payload,
        );
    }
}
