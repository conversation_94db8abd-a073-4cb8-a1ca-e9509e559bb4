<?php

declare(strict_types=1);

namespace Cdn77\Api\Tests\Cname\Domain\Validation;

use Cdn77\Api\Cname\Domain\Exception\CnameIsForbidden;
use Cdn77\Api\Cname\Domain\Exception\CnameLimitReached;
use Cdn77\Api\Cname\Domain\Exception\CnamesAreInUse;
use Cdn77\Api\Cname\Domain\Finder\CnameCountFinder;
use Cdn77\Api\Cname\Domain\Validation\CnameValidator;
use Cdn77\Api\Core\Domain\Dto\Cname\Cname as CnameDto;
use Cdn77\Api\Core\Domain\Dto\Cname\Cnames;
use Cdn77\Api\Core\Domain\Entity\Cdn\CdnId;
use Cdn77\Api\Core\Domain\Entity\Cname\Cname;
use Cdn77\Api\Core\Domain\Entity\Customer\CustomerCdnSettings;
use Cdn77\Api\Core\Domain\Entity\Customer\CustomerId;
use Cdn77\Api\Core\Domain\Exception\ClapDomainException;
use Cdn77\Api\Core\Domain\Finder\Cname\CnameFinder;
use Cdn77\Api\Core\Domain\Repository\Customer\CustomerCdnSettingsRepository;
use Cdn77\Api\Tests\TestCase;
use Cdn77\TestUtils\Stub;
use Generator;
use Mockery;
use PHPUnit\Framework\Attributes\CoversClass;
use PHPUnit\Framework\Attributes\DataProvider;
use PHPUnit\Framework\Attributes\Group;

#[Group('unit')]
#[CoversClass(CnameValidator::class)]
final class CnameValidatorTest extends TestCase
{
    /**
     * @param list<Cname> $usedCnames
     * @param class-string<ClapDomainException>|null $expectedException
     */
    #[DataProvider('validateListProvider')]
    public function testValidateList(
        array $usedCnames,
        Cnames $cnames,
        CustomerCdnSettings $customerCdnSettings,
        string|null $expectedException = null,
        CustomerId|null $customerId = null,
    ): void {
        $customerId ??= CustomerId::fromInteger(1);
        $cdnId = null;
        $customerCdnSettingsRepository = Mockery::mock(CustomerCdnSettingsRepository::class);
        $cnameCountFinder = Mockery::mock(CnameCountFinder::class);
        $cnameFinder = Mockery::mock(CnameFinder::class);
        $validator = new CnameValidator($cnameCountFinder, $cnameFinder, $customerCdnSettingsRepository);

        $customerCdnSettingsRepository
            ->shouldReceive('getForCustomer')
            ->andReturn($customerCdnSettings);
        $cnameFinder
            ->shouldReceive('findForCnames')
            ->zeroOrMoreTimes()
            ->with($cnames->toStrings())
            ->andReturn($usedCnames);

        if ($expectedException !== null) {
            $this->expectException($expectedException);
        }

        $validator->validateList($customerId, $cnames, $cdnId);
    }

    /** @return Generator<string, array<mixed>> */
    public static function validateListProvider(): Generator
    {
        yield 'cnames over limit' => [
            [],
            Cnames::fromStrings(['cname1', 'cname2']),
            Stub::create(CustomerCdnSettings::class, [CustomerCdnSettings::FieldMaxCnames => 1]),
            CnameLimitReached::class,
        ];

        yield 'forbidden cname' => [
            [],
            Cnames::fromStrings(['m.loyalbet88.live.cdn77.eu']),
            Stub::create(CustomerCdnSettings::class, [CustomerCdnSettings::FieldMaxCnames => 1]),
            CnameIsForbidden::class,
        ];

        yield 'forbidden cname is allowed for datacamp account' => [
            [],
            Cnames::fromStrings(['m.loyalbet88.live.cdn77.eu']),
            Stub::create(CustomerCdnSettings::class, [CustomerCdnSettings::FieldMaxCnames => 1]),
            null,
            CustomerId::fromInteger(85),
        ];

        yield 'cnames are in use' => [
            [Stub::create(Cname::class, [Cname::FieldCname => 'cname1'])],
            Cnames::fromStrings(['cname1', 'cname2']),
            Stub::create(CustomerCdnSettings::class, [CustomerCdnSettings::FieldMaxCnames => 5]),
            CnamesAreInUse::class,
        ];
    }

    /**
     * @param list<Cname> $usedCnames
     * @param class-string<ClapDomainException>|null $expectedException
     */
    #[DataProvider('validateAdditionProvider')]
    public function testValidateAddition(
        array $usedCnames,
        CnameDto $cname,
        CustomerCdnSettings $customerCdnSettings,
        int $cdnCnamesCount,
        string|null $expectedException = null,
        CustomerId|null $customerId = null,
    ): void {
        $customerId ??= CustomerId::fromInteger(1);
        $cdnId = CdnId::fromInteger(1);
        $customerCdnSettingsRepository = Mockery::mock(CustomerCdnSettingsRepository::class);
        $cnameCountFinder = Mockery::mock(CnameCountFinder::class);
        $cnameFinder = Mockery::mock(CnameFinder::class);
        $validator = new CnameValidator($cnameCountFinder, $cnameFinder, $customerCdnSettingsRepository);

        $customerCdnSettingsRepository
            ->shouldReceive('getForCustomer')
            ->andReturn($customerCdnSettings);
        $cnameCountFinder
            ->shouldReceive('findForCdn')
            ->with($cdnId)
            ->andReturn($cdnCnamesCount);
        $cnameFinder->shouldReceive('findForCnames')
            ->zeroOrMoreTimes()
            ->andReturn($usedCnames);

        if ($expectedException !== null) {
            $this->expectException($expectedException);
        }

        $validator->validateAddition($customerId, $cname, $cdnId);
    }

    /** @return Generator<string, array<mixed>> */
    public static function validateAdditionProvider(): Generator
    {
        yield 'cnames over limit' => [
            [],
            CnameDto::fromString('cname1'),
            Stub::create(CustomerCdnSettings::class, [CustomerCdnSettings::FieldMaxCnames => 1]),
            1,
            CnameLimitReached::class,
        ];

        yield 'forbidden cname' => [
            [],
            CnameDto::fromString('m.loyalbet88.live.cdn77.eu'),
            Stub::create(CustomerCdnSettings::class, [CustomerCdnSettings::FieldMaxCnames => 5]),
            1,
            CnameIsForbidden::class,
        ];

        yield 'forbidden cname is allowed for datacamp account' => [
            [],
            CnameDto::fromString('m.loyalbet88.live.cdn77.eu'),
            Stub::create(CustomerCdnSettings::class, [CustomerCdnSettings::FieldMaxCnames => 5]),
            1,
            null,
            CustomerId::fromInteger(85),
        ];

        yield 'cname is in use' => [
            [Stub::create(Cname::class, [Cname::FieldCname => 'cname1'])],
            CnameDto::fromString('cname1'),
            Stub::create(CustomerCdnSettings::class, [CustomerCdnSettings::FieldMaxCnames => 5]),
            1,
            CnamesAreInUse::class,
        ];
    }
}
