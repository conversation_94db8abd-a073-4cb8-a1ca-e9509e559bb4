<?php

declare(strict_types=1);

namespace Cdn77\Api\Tests\ORMFixtures\RawLog;

use Cdn77\Api\Core\Domain\Entity\Cdn\Cdn;
use Cdn77\Api\Core\Domain\Entity\RawLogs\RawLog;
use Cdn77\Api\Tests\ORMFixtures\Cdn\CdnFactory;
use Cdn77\Api\Tests\ORMFixtures\EntityFixtureFactory;
use DateTimeImmutable;

final class RawLogFactory extends EntityFixtureFactory
{
    private Cdn|null $dummyCdn = null;

    public function free(
        Cdn|null $cdn = null,
        DateTimeImmutable|null $activeFrom = null,
        DateTimeImmutable|null $activeTo = null,
    ): RawLog {
        $activeFrom ??= new DateTimeImmutable('2023-03-01 00:00:00');
        $activeTo ??= new DateTimeImmutable('2023-03-31 23:59:59');

        $this->dummyCdn ??= CdnFactory::new($this->entityManager)->create();
        $cdn ??= $this->dummyCdn;
        $this->entityManager->persist($cdn);

        return RawLog::free(
            $cdn,
            $activeFrom,
            $activeTo,
        );
    }

    public function debug(
        Cdn|null $cdn = null,
        DateTimeImmutable|null $activeFrom = null,
        DateTimeImmutable|null $activeTo = null,
    ): RawLog {
        $activeFrom ??= new DateTimeImmutable('2023-03-01 00:00:00');
        $activeTo ??= new DateTimeImmutable('2023-03-02 23:59:59');

        $this->dummyCdn ??= CdnFactory::new($this->entityManager)->create();
        $cdn ??= $this->dummyCdn;
        $this->entityManager->persist($cdn);

        return RawLog::debug(
            $cdn,
            $activeFrom,
            $activeTo,
        );
    }
}
