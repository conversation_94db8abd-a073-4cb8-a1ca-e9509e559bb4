<?php

declare(strict_types=1);

namespace Cdn77\Api\Tests\ORMFixtures\Cdn;

use Cdn77\Api\Core\Domain\Entity\RealTimeLog\RealTimeLogFieldCategory;
use Cdn77\Api\Core\Domain\Entity\RealTimeLog\RealTimeLogFieldCategoryId;
use Cdn77\Api\Tests\ORMFixtures\EntityFixtureFactory;

final class RealTimeLogFieldCategoryFactory extends EntityFixtureFactory
{
    public function create(
        string $name,
        int $sortOrder,
    ): RealTimeLogFieldCategory {
        return new RealTimeLogFieldCategory(
            RealTimeLogFieldCategoryId::new(),
            $name,
            $sortOrder,
        );
    }
}
