<?php

declare(strict_types=1);

namespace Cdn77\Api\Tests\ORMFixtures\Cdn;

use Cdn77\Api\Core\Domain\Entity\Cdn\CdnHttpProtectionId;
use Cdn77\Api\Core\Domain\Entity\Cdn\IpProtectionAddress;
use Cdn77\Api\Core\Domain\Value\Cidr;
use Cdn77\Api\Tests\ORMFixtures\EntityFixtureFactory;

final class IpProtectionAddressFactory extends EntityFixtureFactory
{
    public function create(
        CdnHttpProtectionId $cdnHttpProtectionId,
        Cidr|null $cidr = null,
    ): IpProtectionAddress {
        return new IpProtectionAddress(
            $cdnHttpProtectionId,
            $cidr ?? Cidr::fromString('*******/32'),
        );
    }
}
