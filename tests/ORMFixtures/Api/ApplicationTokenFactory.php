<?php

declare(strict_types=1);

namespace Cdn77\Api\Tests\ORMFixtures\Api;

use Cdn77\Api\Authentication\Domain\Value\TokenHash;
use Cdn77\Api\Core\Domain\Entity\Api\ApplicationToken;
use Cdn77\Api\Tests\ORMFixtures\EntityFixtureFactory;
use DateTimeImmutable;

final class ApplicationTokenFactory extends EntityFixtureFactory
{
    public function create(
        TokenHash $tokenHash,
        string $applicationName = 'Integration Tests',
    ): ApplicationToken {
        return new ApplicationToken(
            new DateTimeImmutable('2024-10-28 12:23:00'),
            $tokenHash,
            $applicationName,
        );
    }
}
