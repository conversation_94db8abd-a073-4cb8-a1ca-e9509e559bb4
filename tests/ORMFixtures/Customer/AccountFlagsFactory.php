<?php

declare(strict_types=1);

namespace Cdn77\Api\Tests\ORMFixtures\Customer;

use Cdn77\Api\Core\Domain\Entity\Customer\AccountFlags;
use Cdn77\Api\Core\Domain\Entity\Customer\CustomerId;
use Cdn77\Api\Customer\Domain\Enum\Rating;
use Cdn77\Api\Tests\ORMFixtures\EntityFixtureFactory;
use Cdn77\TestUtils\Stub;

final class AccountFlagsFactory extends EntityFixtureFactory
{
    public function create(
        CustomerId $customerId,
        bool $isReseller = false,
        Rating $rating = Rating::Default,
    ): AccountFlags {
        $flags = new AccountFlags($customerId);

        return Stub::extend($flags, [
            AccountFlags::FieldReseller => $isReseller,
            AccountFlags::FieldRating => $rating,
        ]);
    }
}
