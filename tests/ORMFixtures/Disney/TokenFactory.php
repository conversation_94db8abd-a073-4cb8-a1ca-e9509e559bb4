<?php

declare(strict_types=1);

namespace Cdn77\Api\Tests\ORMFixtures\Disney;

use Cdn77\Api\Core\Domain\Entity\Customer\CustomerUuid;
use Cdn77\Api\Core\Domain\Entity\RevokeToken\RevokeToken;
use Cdn77\Api\Core\Domain\Entity\RevokeToken\RevokeTokenId;
use Cdn77\Api\RevokeToken\Domain\Value\ProductId;
use Cdn77\Api\Tests\ORMFixtures\EntityFixtureFactory;
use DateTimeImmutable;

final class TokenFactory extends EntityFixtureFactory
{
    public function create(
        string $token,
        ProductId $productId,
        CustomerUuid $customerId,
        DateTimeImmutable|null $createdAt = null,
        DateTimeImmutable|null $expiresAt = null,
    ): RevokeToken {
        return new RevokeToken(
            RevokeTokenId::new(),
            $token,
            $createdAt ?? new DateTimeImmutable('2025-01-01 00:00:00'),
            $expiresAt ?? new DateTimeImmutable('midnight tomorrow'),
            $productId,
            $customerId,
        );
    }
}
