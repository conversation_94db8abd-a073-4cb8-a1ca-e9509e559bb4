.PHONY: test-unit
test-unit:
	vendor/bin/phpunit --group unit --coverage-text --coverage-cobertura php-tests-unit-cobertura.xml --colors=never

.PHONY: test-integration
test-integration:
	bin/console doctrine:migrations:migrate -n -q
	vendor/bin/phpunit --group integration

.PHONY: validate-entity-mapping
validate-entity-mapping: vendor
	bin/console doctrine:schema:validate --skip-sync

.PHONY: yarn-install
yarn-install:
	yarn install --immutable

.PHONY: analyze-dependencies
analyze-dependencies:
	vendor/bin/deptrac

.PHONY: rector
rector:
	vendor/bin/rector --no-progress-bar --clear-cache --dry-run

.PHONY: rector-fix
rector-fix:
	vendor/bin/rector --no-progress-bar --clear-cache

.PHONY: cs
cs:
	vendor/bin/phpcs

.PHONY: fix
fix:
	vendor/bin/phpcbf

.PHONY: phpstan
phpstan:
	vendor/bin/phpstan analyse
