<?php

declare(strict_types=1);

namespace Cdn77\Api\Currency\Domain\Command;

use Cdn77\Api\Core\Domain\Entity\Currency\ExchangeRate;
use Cdn77\Api\Core\Domain\Messaging\CommandHandler;
use Cdn77\Api\CoreLibrary\Money\CurrencyCode;
use Cdn77\Api\Currency\Domain\Api\CurrencyApi;
use Cdn77\Api\Currency\Domain\Exception\FailedToFetchExchangeRate;
use Cdn77\Api\Currency\Domain\Repository\ExchangeRateRepository;
use Ds\Set;
use OutOfBoundsException;
use Psr\Clock\ClockInterface;

final readonly class FetchExchangeRatesHandler implements CommandHandler
{
    public function __construct(
        private ClockInterface $clock,
        private CurrencyApi $currencyApi,
        private ExchangeRateRepository $exchangeRateRepository,
    ) {
    }

    /**
     * @throws FailedToFetchExchangeRate
     * @throws OutOfBoundsException
     */
    public function handle(FetchExchangeRates $command): void
    {
        $now = $this->clock->now();
        $baseCurrency = CurrencyCode::USD;
        $rates = $this->currencyApi->getExchangeRates(
            $baseCurrency,
            new Set([CurrencyCode::CZK, CurrencyCode::EUR, CurrencyCode::GBP, CurrencyCode::BRL]),
        );

        $this->exchangeRateRepository->add(
            new ExchangeRate(
                $rates->get(CurrencyCode::EUR),
                $rates->get(CurrencyCode::GBP),
                $rates->get(CurrencyCode::BRL),
                $rates->get(CurrencyCode::CZK),
                $now,
            ),
        );
    }
}
