<?php

declare(strict_types=1);

namespace Cdn77\Api\StorageLocation\Domain\Repository;

use Cdn77\Api\Core\Domain\Entity\Storage\Server;
use Cdn77\Api\Core\Domain\Entity\Storage\StorageServerId;

interface StorageServerRepository
{
    public function get(StorageServerId $id): Server;

    public function getPublic(StorageServerId $id): Server;

    /** @return list<Server> */
    public function findAll(): array;
}
