<?php

declare(strict_types=1);

namespace Cdn77\Api\StorageLocation\Domain\Exception;

use Cdn77\Api\Core\Domain\Exception\ClapDomainException;
use Cdn77\Api\Core\Domain\Exception\SupportsHttpResponse;
use DomainException;
use Symfony\Component\HttpFoundation\Response;

final class InvalidStorageServer extends DomainException implements ClapDomainException, SupportsHttpResponse
{
    public static function notPublic(): self
    {
        return new self('Invalid storage server.');
    }

    public function getResponseCode(): int
    {
        return Response::HTTP_UNPROCESSABLE_ENTITY;
    }
}
