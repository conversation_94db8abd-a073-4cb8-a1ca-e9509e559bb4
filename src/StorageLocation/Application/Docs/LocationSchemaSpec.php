<?php

declare(strict_types=1);

namespace Cdn77\Api\StorageLocation\Application\Docs;

use Cdn77\Api\Core\Application\Payload\ComponentSchema;
use Cdn77\Api\Core\Application\Payload\OASchema;
use cebe\openapi\spec\Schema;
use cebe\openapi\spec\Type;

final readonly class LocationSchemaSpec implements ComponentSchema, OASchema
{
    public const string Name = 'storageLocationSchema';

    public static function name(): string
    {
        return self::Name;
    }

    public static function getSchemaSpec(): Schema
    {
        return new Schema([
            'type' => Type::OBJECT,
            'properties' => [
                'id' => new Schema(['type' => Type::STRING, 'example' => 'push-XX.cdn77.com']),
                'location' => new Schema(['type' => Type::STRING, 'example' => 'Europe - Frankfurt']),
            ],
            'description' => 'Location of the CDN77 Storage server.',
        ]);
    }
}
