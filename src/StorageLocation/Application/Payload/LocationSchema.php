<?php

declare(strict_types=1);

namespace Cdn77\Api\StorageLocation\Application\Payload;

use Cdn77\Api\Core\Application\OpenApi\Model\ComponentReference;
use Cdn77\Api\Core\Application\Payload\HasReference;
use Cdn77\Api\Core\Application\Payload\QueryBusResultSchema;
use Cdn77\Api\Core\Domain\Entity\Storage\Server;
use Cdn77\Api\StorageLocation\Application\Docs\LocationSchemaSpec;
use cebe\openapi\exceptions\TypeErrorException;
use cebe\openapi\spec\Reference;
use Webmozart\Assert\Assert;

final readonly class LocationSchema implements QueryBusResultSchema, HasReference
{
    public function __construct(private string $id, private string $location)
    {
    }

    public static function fromQueryBusResult(mixed $result): self
    {
        Assert::isInstanceOf($result, Server::class);

        return self::fromServer($result);
    }

    public static function fromServer(Server $server): self
    {
        return new self($server->getHostName(), $server->getLocationLabel());
    }

    /** @throws TypeErrorException */
    public static function reference(): Reference
    {
        return ComponentReference::forSchema(LocationSchemaSpec::class);
    }

    public function getId(): string
    {
        return $this->id;
    }

    public function getLocation(): string
    {
        return $this->location;
    }
}
