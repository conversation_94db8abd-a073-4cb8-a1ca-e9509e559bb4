<?php

declare(strict_types=1);

namespace Cdn77\LoggingIntegrationBundle;

use Cdn77\LoggingIntegrationBundle\DependencyInjection\SentryOverridingCompilerPass;
use Cdn77\LoggingIntegrationBundle\Tracy\TracyInit;
use Cdn77\TracyBlueScreenBundle\DependencyInjection\TracyBlueScreenExtension;
use Sentry\SentryBundle\DependencyInjection\SentryExtension;
use Symfony\Bundle\MonologBundle\DependencyInjection\MonologExtension;
use Symfony\Component\DependencyInjection\ContainerBuilder;
use Symfony\Component\HttpKernel\Bundle\Bundle;

final class LoggingIntegrationBundle extends Bundle
{
    public function boot(): void
    {
        parent::boot();

        TracyInit::run();
    }

    public function build(ContainerBuilder $container): void
    {
        parent::build($container);

        $container->registerExtension(new MonologExtension());
        $container->registerExtension(new SentryExtension());
        $container->registerExtension(new TracyBlueScreenExtension());

        $container->addCompilerPass(new SentryOverridingCompilerPass());
    }
}
