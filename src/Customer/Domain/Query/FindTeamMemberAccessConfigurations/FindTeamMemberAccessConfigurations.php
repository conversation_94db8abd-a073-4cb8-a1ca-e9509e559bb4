<?php

declare(strict_types=1);

namespace Cdn77\Api\Customer\Domain\Query\FindTeamMemberAccessConfigurations;

use Cdn77\Api\Core\Domain\Entity\Customer\CustomerUuid;
use Cdn77\Api\Core\Domain\Messaging\Query;
use Cdn77\Api\Customer\Domain\Query\FindTeamMemberAccessConfigurations\Input\Filter;
use Cdn77\Api\Customer\Domain\Query\FindTeamMemberAccessConfigurations\Input\SelectionField;
use Cdn77\Api\Customer\Domain\Query\FindTeamMemberAccessConfigurations\Output\AccessConfigurations;
use Ds\Set;

/** @implements Query<AccessConfigurations, FindTeamMemberAccessConfigurationsHandler> */
final readonly class FindTeamMemberAccessConfigurations implements Query
{
    /** @param Set<covariant SelectionField> $selectionFields */
    public function __construct(
        public Set $selectionFields,
        public Filter $filter,
        public CustomerUuid $customerId,
        public CustomerUuid $teamMemberId,
    ) {
    }
}
