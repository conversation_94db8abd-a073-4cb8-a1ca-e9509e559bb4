<?php

declare(strict_types=1);

namespace Cdn77\Api\Customer\Domain\Query\FindTeamMemberAccessConfigurations;

use Cdn77\Api\Core\Domain\Exception\Customer\CustomerNotFound;
use Cdn77\Api\Core\Domain\Messaging\QueryHandler;
use Cdn77\Api\Core\Domain\Repository\CustomerRepository;
use Cdn77\Api\Customer\Domain\Exception\TeamMemberAccessConfigurationNotFound;
use Cdn77\Api\Customer\Domain\Finder\TeamMemberAccessConfigurationFinder;
use Cdn77\Api\Customer\Domain\Query\FindTeamMemberAccessConfigurations\Output\AccessConfigurations;
use Cdn77\Api\Customer\Domain\Validation\AccountAccessValidator;

final readonly class FindTeamMemberAccessConfigurationsHandler implements QueryHandler
{
    public function __construct(
        private AccountAccessValidator $accountAccessValidator,
        private CustomerRepository $customerRepository,
        private TeamMemberAccessConfigurationFinder $teamMemberAccessConfigurationFinder,
    ) {
    }

    /**
     * @throws CustomerNotFound
     * @throws TeamMemberAccessConfigurationNotFound
     */
    public function handle(FindTeamMemberAccessConfigurations $query): AccessConfigurations
    {
        $customer = $this->customerRepository->getForId($query->customerId);
        if (
            ! $this->accountAccessValidator->canEdit($customer)
            && ! $query->customerId->equals($query->teamMemberId)
        ) {
            throw TeamMemberAccessConfigurationNotFound::notAllowed();
        }

        return $this->teamMemberAccessConfigurationFinder->find(
            $query->selectionFields,
            $query->filter,
        );
    }
}
