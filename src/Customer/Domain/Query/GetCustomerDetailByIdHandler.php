<?php

declare(strict_types=1);

namespace Cdn77\Api\Customer\Domain\Query;

use Cdn77\Api\Core\Domain\Messaging\QueryHandler;
use Cdn77\Api\Core\Domain\Repository\CustomerRepository;
use Cdn77\Api\Customer\Domain\Dto\CustomerDetail;

final readonly class GetCustomerDetailByIdHandler implements QueryHandler
{
    public function __construct(private CustomerRepository $customerRepository)
    {
    }

    public function handle(GetCustomerDetailById $query): CustomerDetail
    {
        $customer = $this->customerRepository->getForId($query->customerId);

        return new CustomerDetail($customer->getCredentials(), $customer->getId());
    }
}
