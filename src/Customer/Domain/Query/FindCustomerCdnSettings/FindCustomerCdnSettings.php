<?php

declare(strict_types=1);

namespace Cdn77\Api\Customer\Domain\Query\FindCustomerCdnSettings;

use Cdn77\Api\Core\Domain\Messaging\Query;
use Cdn77\Api\Customer\Domain\Query\FindCustomerCdnSettings\Input\Filter;
use Cdn77\Api\Customer\Domain\Query\FindCustomerCdnSettings\Input\SelectionField;
use Cdn77\Api\Customer\Domain\Query\FindCustomerCdnSettings\Output\CustomerCdnSettingsCollection;
use Ds\Set;

/** @implements Query<CustomerCdnSettingsCollection, FindCustomerCdnSettingsHandler> */
final readonly class FindCustomerCdnSettings implements Query
{
    /** @param Set<SelectionField> $selectionFields */
    public function __construct(
        public Set $selectionFields,
        public Filter $filter,
    ) {
    }
}
