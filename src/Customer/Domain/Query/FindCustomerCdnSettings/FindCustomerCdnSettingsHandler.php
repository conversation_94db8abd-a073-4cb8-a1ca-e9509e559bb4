<?php

declare(strict_types=1);

namespace Cdn77\Api\Customer\Domain\Query\FindCustomerCdnSettings;

use Cdn77\Api\Core\Domain\Messaging\QueryHandler;
use Cdn77\Api\Customer\Domain\Finder\CustomerCdnSettingsFinder;
use Cdn77\Api\Customer\Domain\Query\FindCustomerCdnSettings\Output\CustomerCdnSettingsCollection;

final readonly class FindCustomerCdnSettingsHandler implements QueryHandler
{
    public function __construct(
        private CustomerCdnSettingsFinder $customerCdnSettingsFinder,
    ) {
    }

    public function handle(FindCustomerCdnSettings $query): CustomerCdnSettingsCollection
    {
        return $this->customerCdnSettingsFinder->find(
            $query->selectionFields,
            $query->filter,
        );
    }
}
