<?php

declare(strict_types=1);

namespace Paygate\Processor\Stripe;

use DateTimeImmutable;
use Paygate\Processor\Processor;
use Paygate\Types\Payment\Payment;
use Paygate\Types\PaymentMethod\CardInfo;
use Paygate\Types\PaymentMethod\PaymentMethodInfo;

use function assert;

class Card implements Processor
{
    public const Id = 'STRIPE_CARD';

    public function getPaymentMethodInfo(Payment $payment): PaymentMethodInfo
    {
        assert($payment->recipe !== null, 'Cannot get payment method info for expected payment');

        return new CardInfo(
            $payment->recipe->id,
            $payment->recipe->processorData['card']['brand'],
            $payment->recipe->processorData['card']['last4'],
            new DateTimeImmutable($payment->recipe->processorData['card']['expires']),
        );
    }
}
