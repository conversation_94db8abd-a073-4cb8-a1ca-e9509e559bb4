<?php

declare(strict_types=1);

namespace Cdn77\LoggingIntegrationBundle\Monolog;

use Monolog\Handler\AbstractProcessingHandler;
use Monolog\Level;
use Monolog\LogRecord;
use Throwable;

use function is_a;

final class IgnoreExceptionsHandler extends AbstractProcessingHandler
{
    /** @param list<class-string<Throwable>> $ignoreExceptions */
    public function __construct(
        Level $level = Level::Debug,
        bool $bubble = true,
        private readonly array $ignoreExceptions = [],
    ) {
        parent::__construct($level, $bubble);
    }

    public function handle(LogRecord $record): bool
    {
        $exception = $record->context['exception'] ?? null;

        if (! $exception instanceof Throwable) {
            return false;
        }

        foreach ($this->ignoreExceptions as $ignoredException) {
            if (is_a($exception, $ignoredException)) {
                return true;
            }
        }

        return false;
    }

    protected function write(LogRecord $record): void
    {
    }
}
