<?php

declare(strict_types=1);

namespace Cdn77\AraClient\FixedPlans\Cdn;

use Cdn77\AraClient\Endpoint;
use Cdn77\AraClient\FixedPlans\Cdn\VO\Cdn;
use Cdn77\AraClient\FixedPlans\EndpointUri;
use Cdn77\AraClient\HttpMethod;
use Psr\Http\Message\ResponseInterface;

use function sprintf;

final class PostEndpoint implements Endpoint
{
    private const URL = EndpointUri::BASE . '/%d/cdn';

    /** @var int */
    private $fixedPlanId;

    /** @var Cdn */
    private $cdn;

    public function __construct(int $fixedPlanId, Cdn $cdn)
    {
        $this->fixedPlanId = $fixedPlanId;
        $this->cdn = $cdn;
    }

    public function getMethod() : string
    {
        return HttpMethod::POST;
    }

    public function getUri() : string
    {
        return sprintf(self::URL, $this->fixedPlanId);
    }

    /** @return mixed[] */
    public function getQuery() : array
    {
        return [];
    }

    /** @return mixed[] */
    public function getPayload() : array
    {
        return [
            'cdnId' => $this->cdn->getCdnId(),
        ];
    }

    public function processResponse(ResponseInterface $response) : int
    {
        return $response->getStatusCode();
    }
}
