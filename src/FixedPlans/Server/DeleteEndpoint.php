<?php

declare(strict_types=1);

namespace Cdn77\AraClient\FixedPlans\Server;

use Cdn77\AraClient\Endpoint;
use Cdn77\AraClient\FixedPlans\EndpointUri;
use Cdn77\AraClient\HttpMethod;
use Psr\Http\Message\ResponseInterface;

use function sprintf;

final class DeleteEndpoint implements Endpoint
{
    private const URL = EndpointUri::BASE . '/%d/server/%d';

    /** @var int */
    private $fixedPlanId;

    /** @var int */
    private $serverId;

    public function __construct(int $fixedPlanId, int $serverId)
    {
        $this->fixedPlanId = $fixedPlanId;
        $this->serverId = $serverId;
    }

    public function getMethod() : string
    {
        return HttpMethod::DELETE;
    }

    public function getUri() : string
    {
        return sprintf(self::URL, $this->fixedPlanId, $this->serverId);
    }

    /** @return mixed[] */
    public function getQuery() : array
    {
        return [];
    }

    /** @return mixed[] */
    public function getPayload() : array
    {
        return [];
    }

    public function processResponse(ResponseInterface $response) : int
    {
        return $response->getStatusCode();
    }
}
