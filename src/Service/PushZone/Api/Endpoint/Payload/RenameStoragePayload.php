<?php

declare(strict_types=1);

namespace Cdn77\Api\Service\PushZone\Api\Endpoint\Payload;

use JsonSerializable;

final readonly class RenameStoragePayload implements JsonSerializable
{
    public function __construct(private string $name, private string $username)
    {
    }

    /** @return array<string, string> */
    public function jsonSerialize(): array
    {
        return [
            'zone_name' => $this->name,
            'username' => $this->username,
        ];
    }
}
