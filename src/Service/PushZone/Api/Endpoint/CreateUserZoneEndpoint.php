<?php

declare(strict_types=1);

namespace Cdn77\Api\Service\PushZone\Api\Endpoint;

use Cdn77\Api\Service\PushZone\Api\Endpoint\Payload\NewZonePayload;
use Cdn77\Api\Service\PushZone\Api\Endpoint\Payload\NewZoneResponsePayload;
use Cdn77\Api\Service\PushZone\Api\EndpointInterface;
use Psl\Json\Exception\DecodeException;
use Psr\Http\Message\ResponseInterface;

use function Psl\Json\typed;
use function Psl\Type\bool;
use function Psl\Type\nullable;
use function Psl\Type\optional;
use function Psl\Type\shape;
use function Psl\Type\string;

final readonly class CreateUserZoneEndpoint implements EndpointInterface
{
    private const string Name = 'createUserZone';

    public function __construct(private NewZonePayload $payload, private string $server)
    {
    }

    public function getName(): string
    {
        return self::Name;
    }

    public function getPayload(): NewZonePayload
    {
        return $this->payload;
    }

    public function getServer(): string
    {
        return $this->server;
    }

    /** @throws DecodeException */
    public function convertResponse(ResponseInterface $response, mixed $payload): object
    {
        $responseData = typed($response->getBody()->__toString(), shape([
            'result' => optional(nullable(bool())),
            'error' => optional(nullable(string())),
        ]));

        return NewZoneResponsePayload::fromArray($responseData);
    }
}
