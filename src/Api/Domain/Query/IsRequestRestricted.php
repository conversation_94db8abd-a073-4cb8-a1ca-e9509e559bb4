<?php

declare(strict_types=1);

namespace Cdn77\Api\Api\Domain\Query;

use Cdn77\Api\Core\Domain\Messaging\Query;
use Symfony\Component\HttpFoundation\Request;

/** @implements Query<bool, IsRequestRestrictedHandler> */
final readonly class IsRequestRestricted implements Query
{
    public function __construct(private Request $request)
    {
    }

    public function getRequest(): Request
    {
        return $this->request;
    }
}
