<?php

declare(strict_types=1);

namespace Cdn77\Api\Api\Domain\Resolver;

use Cdn77\Api\Api\Domain\Dto\RequestLog;
use Cdn77\Api\Api\Domain\Value\RequestAttribute;
use Cdn77\Api\Api\Infrastructure\Request\SensitiveInformationObfuscator;
use Cdn77\Api\Core\Application\Controller\IsExcludedFromRequestLog;
use Cdn77\Api\Core\Application\Symfony\LoggedAccountProvider;
use Cdn77\Api\Core\Domain\Value\IpAddress;
use Cdn77\Api\CoreLibrary\Utils\StringTruncator;
use DateTimeImmutable;
use Psr\Clock\ClockInterface;
use ReflectionException;
use ReflectionMethod;
use Safe\Exceptions\ArrayException;
use Safe\Exceptions\JsonException;
use Safe\Exceptions\NetworkException;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Event\ResponseEvent;
use Symfony\Component\Security\Core\Authentication\Token\Storage\TokenStorageInterface;
use Webmozart\Assert\Assert;

use function Cdn77\Functions\absurd;
use function Safe\gethostname;

final readonly class RequestLogFactory
{
    private const int MaxEndpointLength = 250;
    private const int MaxResponseLength = 100;

    public function __construct(
        private ClockInterface $clock,
        private LoggedAccountProvider $loggedAccountProvider,
        private TokenStorageInterface $tokenStorage,
    ) {
    }

    /**
     * @throws ArrayException
     * @throws JsonException
     * @throws NetworkException
     */
    public function createFromResponseEvent(ResponseEvent $event): RequestLog|null
    {
        $request = $event->getRequest();

        if (self::isExcludedFromRequestLog($request)) {
            return null;
        }

        $requestStart = $request->attributes->get(RequestAttribute::StartDatetimeImmutable);
        Assert::nullOrIsInstanceOf($requestStart, DateTimeImmutable::class);
        $response = $event->getResponse();
        $requestSource = $request->attributes->get(RequestAttribute::SourceName);
        Assert::nullOrString($requestSource);

        $ipAddress = $request->getClientIp();
        $isRequestAnonymous = $this->tokenStorage->getToken() === null;

        return new RequestLog(
            $isRequestAnonymous ? null : $this->loggedAccountProvider->legacyId(),
            $this->clock->now(),
            StringTruncator::truncate($request->getPathInfo(), self::MaxEndpointLength),
            IpAddress::fromNullableString($ipAddress),
            $request->getMethod(),
            $request->getQueryString(),
            SensitiveInformationObfuscator::obfuscateFromHttpObject($request),
            $this->responseBodyForLog($response),
            $response->getStatusCode(),
            $requestSource,
            $requestStart,
            $this->loggedAccountProvider->tokenId(),
            gethostname(),
            $isRequestAnonymous ? null : $this->loggedAccountProvider->affectedCustomerId(),
        );
    }

    /**
     * @throws ArrayException
     * @throws JsonException
     */
    private function responseBodyForLog(Response $response): string|null
    {
        if ($response->isClientError() || $response->isServerError()) {
            return SensitiveInformationObfuscator::obfuscateFromHttpObject($response) ?? '';
        }

        if ($response->headers->get('Content-Type') === 'application/json') {
            return StringTruncator::truncate(
                SensitiveInformationObfuscator::obfuscateFromHttpObject($response) ?? '',
                self::MaxResponseLength,
            );
        }

        return null;
    }

    private static function isExcludedFromRequestLog(Request $request): bool
    {
        $controller = $request->attributes->get(RequestAttribute::Controller);
        Assert::nullOrString($controller);

        if ($controller === null) {
            return false;
        }

        try {
            $controllerClass = ReflectionMethod::createFromMethodName($controller)->getDeclaringClass();
        } catch (ReflectionException) {
            absurd();
        }

        return $controllerClass->implementsInterface(IsExcludedFromRequestLog::class);
    }
}
