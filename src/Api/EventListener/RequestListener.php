<?php

declare(strict_types=1);

namespace Cdn77\Api\Api\EventListener;

use Cdn77\Api\Api\Domain\Query\IsRequestRestricted;
use Cdn77\Api\Api\Domain\Value\RequestAttribute;
use Cdn77\Api\Core\Application\Payload\ErrorsSchema;
use Cdn77\Api\Core\Domain\Messaging\QueryBus;
use Cdn77\Api\Core\Domain\RateLimit\RateLimitChecker;
use InvalidArgumentException;
use JMS\Serializer\SerializerInterface;
use Psr\Clock\ClockInterface;
use Psr\Log\LoggerInterface;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Event\RequestEvent;
use Symfony\Component\HttpKernel\KernelEvents;

use function sprintf;

use const PHP_INT_MAX;

final readonly class RequestListener implements EventSubscriberInterface
{
    private const int CheckRateLimitPriority = 20;

    public function __construct(
        private ClockInterface $clock,
        private LoggerInterface $logger,
        private QueryBus $queryBus,
        private RateLimitChecker $rateLimitChecker,
        private SerializerInterface $serializer,
    ) {
    }

    public static function getSubscribedEvents(): array
    {
        return [
            KernelEvents::REQUEST => [
                ['checkRateLimit', self::CheckRateLimitPriority],
                ['checkRestriction'],
                ['setRequestStartTime', PHP_INT_MAX],
            ],
        ];
    }

    public function setRequestStartTime(RequestEvent $event): void
    {
        $event->getRequest()->attributes->set(RequestAttribute::StartDatetimeImmutable, $this->clock->now());
    }

    public function checkRestriction(RequestEvent $event): void
    {
        if (! $event->isMainRequest()) {
            return;
        }

        $isRestrictedRequest = $this->queryBus->handle(new IsRequestRestricted($event->getRequest()));

        if (! $isRestrictedRequest) {
            return;
        }

        $event->setResponse(JsonResponse::fromJsonString(
            $this->serializer->serialize(
                new ErrorsSchema(
                    ['Your request was blocked. Please contact support or refer to the API documentation.'],
                ),
                'json',
            ),
            Response::HTTP_FORBIDDEN,
        ));
    }

    /** @throws InvalidArgumentException */
    public function checkRateLimit(RequestEvent $event): void
    {
        if (! $event->isMainRequest()) {
            return;
        }

        $request = $event->getRequest();
        $result = $this->rateLimitChecker->check($request);
        $rateLimitResult = $result->rateLimitResult;

        $request->attributes->set(RequestAttribute::RateLimitResult, $rateLimitResult);

        if ($rateLimitResult->isAccepted || ! $result->shouldRateLimit) {
            return;
        }

        $this->logger->error(
            sprintf(
                'Rate limit exceeded for path "%s" and rate limit id "%s"',
                $request->getPathInfo(),
                $rateLimitResult->limitId->info(),
            ),
            [
                'request' => $request,
                'rate_limit_result' => $rateLimitResult,
            ],
        );

        $event->setResponse(new Response(null, Response::HTTP_TOO_MANY_REQUESTS, $rateLimitResult->headers()));
    }
}
