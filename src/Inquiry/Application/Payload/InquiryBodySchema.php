<?php

declare(strict_types=1);

namespace Cdn77\Api\Inquiry\Application\Payload;

use Cdn77\Api\Core\Application\OpenApi\Schema\ArraySchema;
use Cdn77\Api\Core\Application\OpenApi\Schema\ObjectSchema;
use Cdn77\Api\Core\Application\OpenApi\Schema\StringSchema;
use Cdn77\Api\Core\Application\Payload\OASchema;
use Cdn77\Api\Core\Application\Payload\SchemaPropertyResolver;
use Cdn77\Api\Core\Application\Payload\SchemaPropertyValidator;
use Cdn77\Api\Core\Application\Payload\SupportsDtoDeserialization;
use Cdn77\Api\Inquiry\Domain\Value\InquiryBody;
use cebe\openapi\spec\Schema;
use JMS\Serializer\Annotation as Serializer;

/** @implements SupportsDtoDeserialization<InquiryBody> */
final class InquiryBodySchema implements OASchema, SupportsDtoDeserialization
{
    public const string FieldSubject = 'subject';
    public const string FieldMessage = 'message';
    public const string FieldName = 'name';
    public const string FieldEstimatedTraffic = 'estimated_traffic';
    public const string FieldProject = 'project';
    public const string FieldFormUrl = 'form_url';
    public const string FieldUtm = 'utm';

    public string|null $subject = null;
    public string|null $message = null;
    public string|null $name = null;
    public string|null $estimatedTraffic = null;
    public string|null $project = null;
    public string|null $formUrl = null;

    /** @var array<string, string>|null */
    #[Serializer\Type('array')]
    public array|null $utm = null;

    private function __construct()
    {
    }

    public static function getSchemaSpec(): Schema
    {
        return ObjectSchema::spec([
            self::FieldSubject => StringSchema::spec(),
            self::FieldMessage => StringSchema::spec(),
            self::FieldName => StringSchema::spec(),
            self::FieldEstimatedTraffic => StringSchema::spec(),
            self::FieldProject => StringSchema::spec(),
            self::FieldFormUrl => StringSchema::spec(),
            self::FieldUtm => ArraySchema::spec(StringSchema::spec()),
        ]);
    }

    public function validateSchemaProperties(): iterable
    {
        yield SchemaPropertyValidator::isNotNull($this->subject, self::FieldSubject);
        yield SchemaPropertyValidator::isNotNull($this->message, self::FieldMessage);
    }

    public function toDto(): InquiryBody
    {
        return new InquiryBody(
            SchemaPropertyResolver::requireNotNull($this->subject),
            SchemaPropertyResolver::requireNotNull($this->message),
            $this->name,
            $this->estimatedTraffic,
            $this->project,
            $this->formUrl,
            $this->utm,
        );
    }
}
