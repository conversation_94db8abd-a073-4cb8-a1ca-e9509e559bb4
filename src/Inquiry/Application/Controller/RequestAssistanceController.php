<?php

declare(strict_types=1);

namespace Cdn77\Api\Inquiry\Application\Controller;

use Cdn77\Api\Core\Application\Controller\HasInternalOpenApiPaths;
use Cdn77\Api\Core\Application\Controller\IsAllowedForSource;
use Cdn77\Api\Core\Application\OpenApi\Model\Tags;
use Cdn77\Api\Core\Application\OpenApi\PathGenerator;
use Cdn77\Api\Core\Application\Payload\ErrorsSchema;
use Cdn77\Api\Core\Application\Request\ControllerSchemaSerializer;
use Cdn77\Api\Core\Application\Response\ControllerCommandHandler;
use Cdn77\Api\Core\Application\Symfony\LoggedAccountProvider;
use Cdn77\Api\Core\Domain\Value\RequestSource;
use Cdn77\Api\Inquiry\Application\Payload\RequestAssistanceSchema;
use cebe\openapi\spec\MediaType;
use cebe\openapi\spec\Operation;
use cebe\openapi\spec\PathItem;
use cebe\openapi\spec\RequestBody;
use cebe\openapi\spec\Response as OpenApiResponse;
use cebe\openapi\spec\Responses;
use Ds\Set;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;

final readonly class RequestAssistanceController implements HasInternalOpenApiPaths, IsAllowedForSource
{
    public const string RouteName = 'inquiry.request-assistance';
    private const string RouteSummary = 'Request Assistance.';

    public function __construct(
        private ControllerCommandHandler $controllerCommandHandler,
        private ControllerSchemaSerializer $controllerSchemaSerializer,
        private LoggedAccountProvider $loggedAccountProvider,
        private PathGenerator $pathGenerator,
    ) {
    }

    #[Route(path: '/inquiry/request-assistance', name: self::RouteName, methods: [Request::METHOD_POST])]
    public function execute(Request $request): Response
    {
        $result = $this->controllerSchemaSerializer->deserializeToSchema($request, RequestAssistanceSchema::class);

        if ($result instanceof ErrorsSchema) {
            return $this->controllerSchemaSerializer->serializeToResponse($result, Response::HTTP_UNPROCESSABLE_ENTITY);
        }

        return $this->controllerCommandHandler->handle($result->toCommand(
            $this->loggedAccountProvider->customerId(),
        ));
    }

    public function getPathItems(): array
    {
        $post = new Operation([
            'operationId' => self::RouteName,
            'tags' => [Tags::Inquiry],
            'summary' => self::RouteSummary,
            'responses' => new Responses([
                Response::HTTP_NO_CONTENT => new OpenApiResponse(['description' => 'Inquiry was sent.']),
            ]),
            'requestBody' => new RequestBody([
                'required' => true,
                'content' => [
                    'application/json' => new MediaType([
                        'schema' => RequestAssistanceSchema::getSchemaSpec(),
                    ]),
                ],
            ]),
        ]);

        return [$this->pathGenerator->generate(self::RouteName) => new PathItem(['post' => $post])];
    }

    public static function sources(): Set
    {
        return new Set([RequestSource::Crop]);
    }
}
