<?php

declare(strict_types=1);

namespace Cdn77\Api\Inquiry\Domain\Value;

final readonly class InquiryBody
{
    /** @param array<string, mixed>|null $utm */
    public function __construct(
        public string $subject,
        public string $message,
        public string|null $name = null,
        public string|null $estimatedTraffic = null,
        public string|null $project = null,
        public string|null $formUrl = null,
        private array|null $utm = null,
    ) {
    }

    /** @return array<string, string|array<string, mixed>|null> */
    public function toArray(): array
    {
        return [
            'subject' => $this->subject,
            'message' => $this->message,
            'name' => $this->name,
            'estimated_traffic' => $this->estimatedTraffic,
            'project' => $this->project,
            'form_url' => $this->formUrl,
            'utm' => $this->utm,
        ];
    }
}
