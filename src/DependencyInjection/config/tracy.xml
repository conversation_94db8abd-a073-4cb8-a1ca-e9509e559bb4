<?xml version="1.0" encoding="UTF-8" ?>
<container xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
           xmlns="http://symfony.com/schema/dic/services"
           xsi:schemaLocation="http://symfony.com/schema/dic/services
        https://symfony.com/schema/dic/services/services-1.0.xsd">

    <services>
        <defaults autowire="true" autoconfigure="true" public="false" />

        <service id="Cdn77\LoggingIntegrationBundle\Tracy\RemoteStorageDriver\StacktraceHubRemoteStorageDriver">
            <argument key="$hubUrl">%cdn77.logging_integration.stacktrace_hub.url%</argument>
            <argument key="$projectId">%cdn77.logging_integration.stacktrace_hub.project_id%</argument>
            <argument key="$environment">%cdn77.logging_integration.stacktrace_hub.environment%</argument>
        </service>

        <service id="Mangoweb\MonologTracyHandler\RemoteStorageRequestSenders\ExecCurlRequestSender" />

        <service id="Mangoweb\MonologTracyHandler\TracyHandler">
            <argument key="$level" type="constant">Monolog\Level::Notice</argument>
            <argument key="$localBlueScreenDirectory">%kernel.logs_dir%</argument>
        </service>

        <service
            id="Mangoweb\MonologTracyHandler\RemoteStorageDriver"
            alias="Cdn77\LoggingIntegrationBundle\Tracy\RemoteStorageDriver\StacktraceHubRemoteStorageDriver"
        />

        <service
            id="Mangoweb\MonologTracyHandler\RemoteStorageRequestSender"
            alias="Mangoweb\MonologTracyHandler\RemoteStorageRequestSenders\ExecCurlRequestSender"
        />
    </services>
</container>
