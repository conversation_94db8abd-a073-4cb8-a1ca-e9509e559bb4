<?php

declare(strict_types=1);

namespace Cdn77\Api\Public\Application\Payload;

use Cdn77\Api\Core\Application\Payload\OASchema;
use Cdn77\Api\Core\Application\Payload\SchemaPropertyResolver;
use Cdn77\Api\Core\Application\Payload\SchemaPropertyValidator;
use Cdn77\Api\Core\Application\Payload\SupportsDeserialization;
use Cdn77\Api\Core\Domain\Value\Origin\Host;
use Cdn77\Api\Public\Domain\Query\GetTlsTestResults;
use cebe\openapi\spec\Schema;
use cebe\openapi\spec\Type;

final class TlsTestSchema implements OASchema, SupportsDeserialization
{
    public const string FieldHost = 'host';
    public const string FieldRefresh = 'refresh';

    public string|null $host = null;

    public bool|null $refresh = null;

    private function __construct()
    {
    }

    public static function getSchemaSpec(): Schema
    {
        return new Schema([
            'type' => Type::OBJECT,
            'properties' => [
                self::FieldHost => new Schema(['type' => Type::STRING, 'example' => 'example.com']),
                self::FieldRefresh => new Schema(['type' => Type::BOOLEAN, 'example' => true]),
            ],
        ]);
    }

    public function validateSchemaProperties(): iterable
    {
        yield SchemaPropertyValidator::isNotNull($this->host, self::FieldHost);

        if ($this->host === null) {
            return;
        }

        yield SchemaPropertyValidator::notRegex(
            $this->host,
            Host::PatternHostContainsPort,
            'Host cannot contain port number.',
            self::FieldHost,
        );

        $hostIsIpV4 = SchemaPropertyValidator::isIpv4(
            $this->host,
            'Host must represent a valid domain name or IP address.',
            self::FieldHost,
        ) === null;

        yield $hostIsIpV4
            ? SchemaPropertyValidator::isPublicIpv4(
                $this->host,
                'Host must represent a valid domain name or IP address.',
                self::FieldHost,
            )
            : SchemaPropertyValidator::regex(
                $this->host,
                Host::PatternHost,
                'Host must represent a valid domain name or IP address.',
                self::FieldHost,
            );
    }

    public function toQuery(): GetTlsTestResults
    {
        return new GetTlsTestResults(
            Host::fromString(SchemaPropertyResolver::requireNotNull($this->host)),
            $this->refresh ?? false,
        );
    }
}
