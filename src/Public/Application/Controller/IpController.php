<?php

declare(strict_types=1);

namespace Cdn77\Api\Public\Application\Controller;

use Cdn77\Api\Core\Application\Controller\HasInternalOpenApiPaths;
use Cdn77\Api\Core\Application\Controller\RequiresInternalTokenAuthentication;
use Cdn77\Api\Core\Application\OpenApi\Model\Tags;
use Cdn77\Api\Core\Application\OpenApi\PathGenerator;
use cebe\openapi\spec\MediaType;
use cebe\openapi\spec\Operation;
use cebe\openapi\spec\PathItem;
use cebe\openapi\spec\Response as OpenApiResponse;
use cebe\openapi\spec\Responses;
use cebe\openapi\spec\Type;
use InvalidArgumentException;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;

final readonly class IpController implements HasInternalOpenApiPaths, RequiresInternalTokenAuthentication
{
    public const string RouteName = 'public.user.ip';
    public const string RoutePath = '/public/user/ip';
    private const string RouteSummary = 'Get user IP address';

    public function __construct(
        private PathGenerator $pathGenerator,
    ) {
    }

    /** @throws InvalidArgumentException */
    #[Route(path: self::RoutePath, name: self::RouteName, methods: [Request::METHOD_GET])]
    public function execute(Request $request): JsonResponse
    {
        return new JsonResponse(['ip' => $request->getClientIp()]);
    }

    public function getPathItems(): array
    {
        $get = new Operation([
            'operationId' => self::RouteName,
            'tags' => [Tags::Other],
            'summary' => self::RouteSummary,
            'responses' => new Responses([
                Response::HTTP_OK => new OpenApiResponse([
                    'description' => 'User IP address returned.',
                    'content' => [
                        'application/json' => new MediaType([
                            'schema' => [
                                'type' => 'object',
                                'properties' => [
                                    'ip' => [
                                        'type' => Type::STRING,
                                        'description' => 'IP address of the user',
                                    ],
                                ],
                            ],
                        ]),
                    ],
                ]),
            ]),
        ]);

        return [$this->pathGenerator->generate(self::RouteName) => new PathItem(['get' => $get])];
    }
}
