<?php

declare(strict_types=1);

namespace Cdn77\Api\Public\Application\Controller;

use Cdn77\Api\Core\Application\Controller\RequiresInternalTokenAuthentication;
use Cdn77\Api\Core\Application\Payload\ErrorsSchema;
use Cdn77\Api\Core\Application\Request\ControllerSchemaSerializer;
use Cdn77\Api\Core\Application\Response\ControllerCommandHandler;
use Cdn77\Api\Public\Application\Payload\BuildSchema;
use Cdn77\Api\Public\Domain\Command\TriggerCdn77JobsBuild;
use InvalidArgumentException;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;

final readonly class BuildController implements RequiresInternalTokenAuthentication
{
    public const string RouteName = 'public.build.cdn77-jobs';

    public function __construct(
        private ControllerCommandHandler $controllerCommandHandler,
        private ControllerSchemaSerializer $controllerSchemaSerializer,
    ) {
    }

    /** @throws InvalidArgumentException */
    #[Route('/public/build/cdn77-jobs', name: self::RouteName, methods: [Request::METHOD_POST])]
    public function execute(Request $request): Response
    {
        $result = $this->controllerSchemaSerializer->deserialize($request, BuildSchema::class);

        if ($result instanceof ErrorsSchema) {
            return new Response(
                status: Response::HTTP_NO_CONTENT,
            );
        }

        return $this->controllerCommandHandler->handle(new TriggerCdn77JobsBuild());
    }
}
