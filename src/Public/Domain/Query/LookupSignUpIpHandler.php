<?php

declare(strict_types=1);

namespace Cdn77\Api\Public\Domain\Query;

use Cdn77\Api\Core\Domain\Messaging\QueryHandler;
use Cdn77\Api\Public\Domain\Dto\LookupResult;
use Ds\Set;

final readonly class LookupSignUpIpHandler implements QueryHandler
{
    /** @param list<string> $whitelistedIps */
    public function __construct(private array $whitelistedIps = [])
    {
    }

    public function handle(LookupSignUpIp $query): LookupResult
    {
        $whitelistedIps = new Set($this->whitelistedIps);

        return $whitelistedIps->contains($query->ipAddress->get())
            ? LookupResult::whitelisted()
            : LookupResult::notFound();
    }
}
