<?php

declare(strict_types=1);

namespace Cdn77\Api\Ticket\Infrastructure\Api;

use Cdn77\Api\Core\Domain\Exception\Absurd;
use Cdn77\Api\Core\Domain\Value\SortDirection;
use Cdn77\Api\Core\Infrastructure\HttpClient\Endpoint;
use Cdn77\Api\Core\Infrastructure\HttpClient\HasHeaders;
use Cdn77\Api\Core\Infrastructure\HttpClient\HasPayload;
use Cdn77\Api\Core\Infrastructure\HttpClient\HasQueryParameters;
use Cdn77\Api\Ticket\Domain\Enum\DepartmentId;
use Cdn77\Api\Ticket\Domain\Enum\SortField;
use Cdn77\Api\Ticket\Domain\Enum\StatusId;
use Ds\Set;
use Random\RandomException;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

use function base64_encode;
use function bin2hex;
use function hash_hmac;
use function http_build_query;
use function random_bytes;
use function sprintf;

final readonly class GetTicketsEndpoint implements Endpoint, HasQueryParameters, HasPayload, HasHeaders
{
    /**
     * @param Set<covariant DepartmentId> $departmentIds
     * @param Set<covariant StatusId> $statusIds
     */
    public function __construct(
        private string $apiKey,
        private string $apiSecret,
        private Set $departmentIds,
        private Set $statusIds,
        private int $limit,
        private int $offset,
        public SortField $sortField,
        public SortDirection $sortDirection,
    ) {
    }

    public function method(): string
    {
        return Request::METHOD_GET;
    }

    public function path(): string
    {
        return '';
    }

    public function expectedResponseCodes(): Set
    {
        return new Set([Response::HTTP_OK]);
    }

    public function payload(): string
    {
        try {
            $salt = bin2hex(random_bytes(5));
        } catch (RandomException $e) {
            throw Absurd::fromThrowable($e);
        }

        return http_build_query(
            [
                'apikey' => $this->apiKey,
                'salt' => $salt,
                'signature' => base64_encode(hash_hmac('sha256', $salt, $this->apiSecret, true)),
            ],
        );
    }

    public function queryParameters(): array
    {
        return [
            'e' => sprintf(
                '/Tickets/Ticket/ListAll/%s/%s/-1/-1/%d/%d/%s/%s',
                $this->departmentIds->map(static fn (DepartmentId $id): int => $id->value)->join(','),
                $this->statusIds->map(static fn (StatusId $id): int => $id->value)->join(','),
                $this->limit,
                $this->offset,
                $this->sortField->value,
                $this->sortDirection->value,
            ),
        ];
    }

    public function headers(): array
    {
        return ['Content-Type' => 'application/x-www-form-urlencoded'];
    }
}
