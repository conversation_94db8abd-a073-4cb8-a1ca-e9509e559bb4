<?php

declare(strict_types=1);

namespace Cdn77\Api\Ticket\Infrastructure\Api;

use Cdn77\Api\Core\Domain\Exception\Absurd;
use Cdn77\Api\Core\Domain\Value\HttpHeader;
use Cdn77\Api\Core\Infrastructure\HttpClient\Endpoint;
use Cdn77\Api\Core\Infrastructure\HttpClient\HasHeaders;
use Cdn77\Api\Core\Infrastructure\HttpClient\HasPayload;
use Cdn77\Api\Core\Infrastructure\HttpClient\HasQueryParameters;
use Cdn77\Api\Ticket\Domain\Enum\DepartmentId;
use Cdn77\Api\Ticket\Domain\Enum\StatusId;
use Cdn77\Api\Ticket\Domain\Enum\TicketPriority;
use Ds\Set;
use Random\RandomException;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

use function base64_encode;
use function bin2hex;
use function hash_hmac;
use function http_build_query;
use function random_bytes;

final readonly class CreateTicketEndpoint implements Endpoint, HasQueryParameters, HasPayload, HasHeaders
{
    private const string EndpointPath = '/Tickets/Ticket';
    private const int TicketTypeIssue = 1;

    public function __construct(
        private string $apiKey,
        private string $apiSecret,
        private string $subject,
        private string $fullName,
        private string $email,
        private string $contents,
        private DepartmentId $department,
        private StatusId $ticketStatus,
        private TicketPriority $ticketPriority,
    ) {
    }

    public function method(): string
    {
        return Request::METHOD_POST;
    }

    public function payload(): string
    {
        try {
            $salt = bin2hex(random_bytes(5));
        } catch (RandomException $e) {
            throw Absurd::fromThrowable($e);
        }

        return http_build_query([
            'subject' => $this->subject,
            'fullname' => $this->fullName,
            'email' => $this->email,
            'contents' => $this->contents,
            'departmentid' => $this->department->value,
            'ticketstatusid' => $this->ticketStatus->value,
            'ticketpriorityid' => $this->ticketPriority->value,
            'tickettypeid' => self::TicketTypeIssue,
            'ignoreautoresponder' => 1,
            'autouserid' => 1,
            'apikey' => $this->apiKey,
            'salt' => $salt,
            'signature' => base64_encode(hash_hmac('sha256', $salt, $this->apiSecret, true)),
        ]);
    }

    public function queryParameters(): array
    {
        return ['e' => self::EndpointPath];
    }

    public function path(): string
    {
        return '';
    }

    public function expectedResponseCodes(): Set
    {
        return new Set([Response::HTTP_OK]);
    }

    public function headers(): array
    {
        return [HttpHeader::ContentType->value => 'application/x-www-form-urlencoded'];
    }
}
