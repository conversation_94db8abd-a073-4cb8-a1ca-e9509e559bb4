<?php

declare(strict_types=1);

namespace Cdn77\Api\Ticket\Domain\Query\FindKayakoTickets;

use Cdn77\Api\Core\Domain\Messaging\Query;
use Cdn77\Api\Ticket\Domain\Query\FindKayakoTickets\Input\Filter;
use Cdn77\Api\Ticket\Domain\Query\FindKayakoTickets\Input\SelectionField;
use Cdn77\Api\Ticket\Domain\Query\FindKayakoTickets\Output\KayakoTickets;
use Ds\Set;

/** @implements Query<KayakoTickets, FindKayakoTicketsHandler> */
final readonly class FindKayakoTickets implements Query
{
    /** @param Set<SelectionField> $selectionFields */
    public function __construct(
        public Set $selectionFields,
        public Filter $filter,
    ) {
    }
}
