<?php

declare(strict_types=1);

namespace Cdn77\AraClient\Stats;

use Cdn77\AraClient\Endpoint;
use Cdn77\AraClient\HttpMethod;
use Cdn77\AraClient\Stats\VO\GetStats;
use Cdn77\AraClient\Stats\VO\Stats;
use JMS\Serializer\SerializerBuilder;
use Psr\Http\Message\ResponseInterface;

use function assert;

final class GetStatsEndpoint implements Endpoint
{
    private const ENDPOINT_URI = '/stats';

    /** @var GetStats */
    private $getStats;

    public function __construct(GetStats $getStats)
    {
        $this->getStats = $getStats;
    }

    public function getMethod() : string
    {
        return HttpMethod::POST;
    }

    public function getUri() : string
    {
        return self::ENDPOINT_URI;
    }

    /** @return mixed[] */
    public function getQuery() : array
    {
        return [];
    }

    /** @return mixed[] */
    public function getPayload() : array
    {
        return $this->getStats->toArray();
    }

    public function processResponse(ResponseInterface $response) : Stats
    {
        $content = $response->getBody()->getContents();
        $serializer = SerializerBuilder::create()->build();
        $result = $serializer->deserialize($content, Stats::class, 'json');
        assert($result instanceof Stats);

        return $result;
    }
}
