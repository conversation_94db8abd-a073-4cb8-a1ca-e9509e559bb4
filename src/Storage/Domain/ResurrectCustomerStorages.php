<?php

declare(strict_types=1);

namespace Cdn77\Api\Storage\Domain;

use Cdn77\Api\Core\Domain\Entity\Customer\CustomerId;
use Cdn77\Api\Storage\Domain\Repository\StorageRepository;

final readonly class ResurrectCustomerStorages
{
    public function __construct(private StorageRepository $storageRepository)
    {
    }

    public function __invoke(CustomerId $customerId): void
    {
        $storages = $this->storageRepository->findForSuspendedCdns($customerId);

        foreach ($storages as $storage) {
            $storage->resurrect();
        }
    }
}
