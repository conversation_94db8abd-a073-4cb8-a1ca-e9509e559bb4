<?php

declare(strict_types=1);

namespace Cdn77\Api\Storage\Domain\Query;

use Cdn77\Api\Core\Domain\Entity\Storage\Storage;
use Cdn77\Api\Core\Domain\Messaging\QueryHandler;
use Cdn77\Api\Core\Domain\Repository\CustomerRepository;
use Cdn77\Api\Core\Domain\Repository\Storage\StorageRepository;
use Cdn77\Api\Core\Domain\Validation\StorageAccessValidator;
use Cdn77\Api\Core\Domain\Value\AccessRestriction\AccessType;
use Cdn77\Api\Storage\Domain\Exception\StorageNotFound;

final readonly class GetStorageByIdForCustomerHandler implements QueryHandler
{
    public function __construct(
        private CustomerRepository $customerRepository,
        private StorageAccessValidator $storageAccessValidator,
        private StorageRepository $storageRepository,
    ) {
    }

    public function handle(GetStorageByIdForCustomer $query): Storage
    {
        $customer = $this->customerRepository->get($query->customerId);
        $storage = $this->storageRepository->get($query->storageId);

        if ($this->storageAccessValidator->isOwnerOrAdmin($storage, $customer, AccessType::OriginRead)) {
            return $storage;
        }

        throw StorageNotFound::fromId($query->storageId);
    }
}
