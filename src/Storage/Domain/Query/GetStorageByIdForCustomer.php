<?php

declare(strict_types=1);

namespace Cdn77\Api\Storage\Domain\Query;

use Cdn77\Api\Core\Domain\Entity\Customer\CustomerId;
use Cdn77\Api\Core\Domain\Entity\Storage\Storage;
use Cdn77\Api\Core\Domain\Entity\Storage\StorageIdentifier;
use Cdn77\Api\Core\Domain\Messaging\Query;

/** @implements Query<Storage, GetStorageByIdForCustomerHandler> */
final class GetStorageByIdForCustomer implements Query
{
    public function __construct(public CustomerId $customerId, public StorageIdentifier $storageId)
    {
    }
}
