<?php

declare(strict_types=1);

namespace Cdn77\Api\Storage\Domain\Query;

use Cdn77\Api\Core\Domain\Messaging\QueryHandler;
use Cdn77\Api\Core\Domain\PushZone\PushZoneApi;
use Cdn77\Api\Storage\Domain\Finder\StorageSecretFinder;
use Cdn77\Api\StorageLocation\Domain\Repository\StorageServerRepository;

final readonly class UpdateStoragesOnRemoteServerHandler implements QueryHandler
{
    public function __construct(
        private PushZoneApi $pushZoneApi,
        private StorageSecretFinder $storageSecretFinder,
        private StorageServerRepository $storageServerRepository,
    ) {
    }

    public function handle(UpdateStoragesOnRemoteServer $query): void
    {
        $server = $this->storageServerRepository->get($query->hostName);
        $storageSecrets = $this->storageSecretFinder->findForServer($server);

        $this->pushZoneApi->updateServer($server, $storageSecrets);
    }
}
