<?php

declare(strict_types=1);

namespace Cdn77\Api\Storage\Domain\Exception;

use Cdn77\Api\Core\Domain\Exception\ClapDomainException;
use DomainException;

use function array_map;
use function implode;
use function sprintf;

final class ServersUpdateFailed extends DomainException implements ClapDomainException
{
    /** @param array<ServerUpdateFailed> $exceptions */
    public static function fromExceptions(array $exceptions): self
    {
        return new self(
            sprintf(
                'Some servers failed to update: "%s"',
                implode(
                    '", "',
                    array_map(static fn (ServerUpdateFailed $e): string => $e->getMessage(), $exceptions),
                ),
            ),
            0,
            $exceptions[0],
        );
    }
}
