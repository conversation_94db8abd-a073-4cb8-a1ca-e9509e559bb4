<?php

declare(strict_types=1);

namespace Cdn77\Api\Storage\Domain\Command;

use Cdn77\Api\Core\Domain\Entity\Customer\CustomerUuid;
use Cdn77\Api\Core\Domain\Entity\Storage\StorageIdentifier;
use Cdn77\Api\Core\Domain\Messaging\Command;
use Cdn77\Api\Core\Domain\Value\Label;
use Cdn77\Api\Core\Domain\Value\Note;
use Cdn77\Api\Storage\Domain\Dto\EditedStorage;
use Cdn77\Api\Storage\Domain\Value\StoragePassword;

/** @implements Command<void, EditStorageHandler> */
final class EditStorage implements Command
{
    private function __construct(
        public CustomerUuid $customerId,
        public StorageIdentifier $storageIdentifier,
        public StoragePassword|null $storagePassword,
        public Label|null $label,
        public readonly Note|null $note,
    ) {
    }

    public static function fromDto(
        CustomerUuid $customerId,
        StorageIdentifier $storageIdentifier,
        EditedStorage $editedStorage,
    ): self {
        return new self(
            $customerId,
            $storageIdentifier,
            $editedStorage->password,
            $editedStorage->label,
            $editedStorage->note,
        );
    }
}
