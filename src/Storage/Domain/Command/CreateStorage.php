<?php

declare(strict_types=1);

namespace Cdn77\Api\Storage\Domain\Command;

use Cdn77\Api\Core\Domain\Entity\Customer\CustomerUuid;
use Cdn77\Api\Core\Domain\Entity\Storage\Storage;
use Cdn77\Api\Core\Domain\Entity\Storage\StorageServerId;
use Cdn77\Api\Core\Domain\Messaging\Command;
use Cdn77\Api\Core\Domain\Value\Label;
use Cdn77\Api\Core\Domain\Value\Note;
use Cdn77\Api\Storage\Domain\Dto\NewStorage;
use Cdn77\Api\Storage\Domain\Value\StoragePassword;

/** @implements Command<Storage, CreateStorageHandler> */
final class CreateStorage implements Command
{
    public function __construct(
        public CustomerUuid $customerId,
        public Label $label,
        public readonly Note $note,
        public StoragePassword|null $password,
        public StorageServerId $storageServerId,
    ) {
    }

    public static function fromDto(CustomerUuid $owner, NewStorage $storage): self
    {
        return new self($owner, $storage->label, $storage->note, $storage->password, $storage->storageServerId);
    }
}
