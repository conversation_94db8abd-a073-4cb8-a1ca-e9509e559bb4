<?php

declare(strict_types=1);

namespace Cdn77\Api\Storage\Domain\Command;

use Cdn77\Api\Cdn\Domain\Finder\CustomerCdnCountFinder;
use Cdn77\Api\Core\Domain\Messaging\CommandHandler;
use Cdn77\Api\Core\Domain\Repository\CustomerRepository;
use Cdn77\Api\Core\Domain\Repository\Storage\StorageRepository;
use Cdn77\Api\Core\Domain\Validation\StorageAccessValidator;
use Cdn77\Api\Core\Domain\Value\AccessRestriction\AccessType;
use Cdn77\Api\Storage\Domain\Exception\CouldNotDeleteStorage;
use Cdn77\Api\Storage\Domain\Exception\StorageNotFound;
use Psr\Clock\ClockInterface;

final readonly class DeleteStorageHandler implements CommandHandler
{
    public function __construct(
        private ClockInterface $clock,
        private CustomerCdnCountFinder $customerCdnCountFinder,
        private CustomerRepository $customerRepository,
        private StorageAccessValidator $storageAccessValidator,
        private StorageRepository $storageRepository,
    ) {
    }

    public function handle(DeleteStorage $command): void
    {
        $customer = $this->customerRepository->get($command->customerId);
        $storageId = $command->storageIdentifier;
        $storage = $this->storageRepository->get($storageId);

        if (! $this->storageAccessValidator->isOwnerOrAdmin($storage, $customer, AccessType::OriginDelete)) {
            throw StorageNotFound::fromId($storageId);
        }

        $cdnCount = $this->customerCdnCountFinder->findForStorage($storageId);

        if ($cdnCount > 0) {
            throw CouldNotDeleteStorage::isBeingUsed($storageId, $cdnCount);
        }

        $storage->markDeleted($this->clock->now());
    }
}
