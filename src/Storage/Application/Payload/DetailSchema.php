<?php

declare(strict_types=1);

namespace Cdn77\Api\Storage\Application\Payload;

use Cdn77\Api\Core\Application\OpenApi\Schema\UuidSchema;
use Cdn77\Api\Core\Application\Payload\CommandBusResultSchema;
use Cdn77\Api\Core\Application\Payload\OASchema;
use Cdn77\Api\Core\Application\Payload\QueryBusResultSchema;
use Cdn77\Api\Core\Domain\Entity\Storage\Storage;
use Cdn77\Api\Origin\Application\Payload\StorageOriginDetailSchema;
use cebe\openapi\spec\Schema;
use cebe\openapi\spec\Type;
use Symfony\Component\Uid\Uuid;
use Webmozart\Assert\Assert;

final readonly class DetailSchema implements CommandBusResultSchema, QueryBusResultSchema, OASchema
{
    public const string FieldId = 'id';
    public const string FieldLabel = 'label';
    public const string FieldServer = 'server';
    public const string FieldUserName = 'username';

    public function __construct(
        private Uuid $id,
        private string $label,
        public string|null $note,
        private ServerSchema $server,
        private string $username,
    ) {
    }

    public static function fromQueryBusResult(mixed $result): self
    {
        Assert::isInstanceOf($result, Storage::class);

        return self::fromStorage($result);
    }

    public static function fromCommandBusResult(mixed $result): self
    {
        Assert::isInstanceOf($result, Storage::class);

        return self::fromStorage($result);
    }

    public static function fromStorage(Storage $storage): self
    {
        return new self(
            $storage->getId()->toUid(),
            $storage->label()->value,
            $storage->note()->get(),
            ServerSchema::fromServer($storage->getServer()),
            $storage->getUserName(),
        );
    }

    public static function getSchemaSpec(): Schema
    {
        return new Schema([
            'type' => Type::OBJECT,
            'properties' => [
                'id' => UuidSchema::spec(),
                self::FieldLabel => new Schema(['type' => Type::STRING, 'example' => 'My storage']),
                'note' => StorageOriginDetailSchema::getNoteSchemaSpec(),
                'server' => ServerSchema::getSchemaSpec(),
                'username' => new Schema(['type' => Type::STRING, 'example' => 'user_1234']),
            ],
        ]);
    }

    public function getId(): Uuid
    {
        return $this->id;
    }

    public function getLabel(): string
    {
        return $this->label;
    }

    public function getServer(): ServerSchema
    {
        return $this->server;
    }

    public function getUsername(): string
    {
        return $this->username;
    }
}
