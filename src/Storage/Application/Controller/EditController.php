<?php

declare(strict_types=1);

namespace Cdn77\Api\Storage\Application\Controller;

use Cdn77\Api\Core\Application\Controller\HasOpenApiPaths;
use Cdn77\Api\Core\Application\OpenApi\ErrorsOpenApiResponse;
use Cdn77\Api\Core\Application\OpenApi\FieldsErrorsOpenApiResponse;
use Cdn77\Api\Core\Application\OpenApi\Model\Tags;
use Cdn77\Api\Core\Application\OpenApi\PathGenerator;
use Cdn77\Api\Core\Application\OpenApi\Schema\UuidSchema;
use Cdn77\Api\Core\Application\Payload\ErrorsSchema;
use Cdn77\Api\Core\Application\Request\ControllerSchemaSerializer;
use Cdn77\Api\Core\Application\Response\ControllerCommandHandler;
use Cdn77\Api\Core\Application\Symfony\LoggedAccountProvider;
use Cdn77\Api\Core\Domain\Entity\Storage\StorageIdentifier;
use Cdn77\Api\Storage\Application\Payload\EditStorageSchema;
use Cdn77\Api\Storage\Domain\Command\EditStorage;
use cebe\openapi\spec\MediaType;
use cebe\openapi\spec\Operation;
use cebe\openapi\spec\Parameter;
use cebe\openapi\spec\PathItem;
use cebe\openapi\spec\RequestBody;
use cebe\openapi\spec\Response as OpenApiResponse;
use cebe\openapi\spec\Responses;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;

final readonly class EditController implements HasOpenApiPaths
{
    public const string LegacyRouteName = 'storage.edit';
    public const string RouteName = 'origin.edit.storage';
    private const string RouteSummary = 'Edit CDN77 Storage';

    public function __construct(
        private ControllerCommandHandler $controllerCommandHandler,
        private ControllerSchemaSerializer $controllerSchemaSerializer,
        private LoggedAccountProvider $loggedAccountProvider,
        private PathGenerator $pathGenerator,
    ) {
    }

    #[Route(path: '/storage/{id}', name: self::LegacyRouteName, methods: [Request::METHOD_POST])]
    #[Route(path: '/origin/storage/{id}', name: self::RouteName, methods: [Request::METHOD_PATCH])]
    public function execute(Request $request, StorageIdentifier $id): Response
    {
        $result = $this->controllerSchemaSerializer->deserialize($request, EditStorageSchema::class);
        $customerId = $this->loggedAccountProvider->customerId();

        if ($result instanceof ErrorsSchema) {
            return $this->controllerSchemaSerializer->serializeToResponse($result, Response::HTTP_UNPROCESSABLE_ENTITY);
        }

        return $this->controllerCommandHandler->handle(
            EditStorage::fromDto($customerId, $id, $result),
        );
    }

    public function getPathItems(): array
    {
        $patch = new Operation([
            'operationId' => self::RouteName,
            'tags' => [Tags::Origin, Tags::OriginStorage],
            'summary' => self::RouteSummary,
            'parameters' => [
                new Parameter([
                    'name' => 'id',
                    'in' => 'path',
                    'required' => true,
                    'schema' => UuidSchema::spec(),
                ]),
            ],
            'responses' => new Responses([
                Response::HTTP_NO_CONTENT => new OpenApiResponse(
                    ['description' => 'Storage updated.'],
                ),
                Response::HTTP_UNPROCESSABLE_ENTITY => FieldsErrorsOpenApiResponse::spec(
                    'Unable to create CDN Storage with requested parameters.',
                ),
                Response::HTTP_NOT_FOUND => ErrorsOpenApiResponse::spec('Storage does not exist.'),
            ]),
            'requestBody' => new RequestBody(
                [
                    'required' => true,
                    'content' => [
                        'application/json' => new MediaType(
                            [
                                'schema' => EditStorageSchema::getSchemaSpec(),
                            ],
                        ),
                    ],
                ],
            ),
        ]);

        return [$this->pathGenerator->generate(self::RouteName) => new PathItem(['patch' => $patch])];
    }
}
