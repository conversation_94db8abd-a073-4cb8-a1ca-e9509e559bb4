<?php

declare(strict_types=1);

namespace Cdn77\Api\Storage\Application\Controller;

use Cdn77\Api\Core\Application\Controller\HasInternalOpenApiPaths;
use Cdn77\Api\Core\Application\OpenApi\ErrorsOpenApiResponse;
use Cdn77\Api\Core\Application\OpenApi\Model\Tags;
use Cdn77\Api\Core\Application\OpenApi\PathGenerator;
use Cdn77\Api\Core\Application\OpenApi\Schema\UuidSchema;
use Cdn77\Api\Core\Application\Response\ControllerQueryHandler;
use Cdn77\Api\Core\Application\Symfony\LoggedAccountProvider;
use Cdn77\Api\Core\Domain\Entity\Storage\StorageIdentifier;
use Cdn77\Api\Storage\Application\Payload\DetailSchema;
use Cdn77\Api\Storage\Domain\Query\GetStorageByIdForCustomer;
use cebe\openapi\spec\MediaType;
use cebe\openapi\spec\Operation;
use cebe\openapi\spec\Parameter;
use cebe\openapi\spec\PathItem;
use cebe\openapi\spec\Response as OpenApiResponse;
use cebe\openapi\spec\Responses;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;

final readonly class DetailController implements HasInternalOpenApiPaths
{
    public const string RouteName = 'storage.detail';
    private const string RouteSummary = 'Detail of CDN Storage';

    public function __construct(
        private ControllerQueryHandler $controllerQueryHandler,
        private LoggedAccountProvider $loggedAccountProvider,
        private PathGenerator $pathGenerator,
    ) {
    }

    #[Route(path: '/storage/{id}', name: self::RouteName, methods: [Request::METHOD_GET])]
    public function execute(StorageIdentifier $id): JsonResponse
    {
        $customerId = $this->loggedAccountProvider->legacyId();

        return $this->controllerQueryHandler->handle(
            new GetStorageByIdForCustomer($customerId, $id),
            DetailSchema::class,
            Response::HTTP_OK,
        );
    }

    public function getPathItems(): array
    {
        $get = new Operation([
            'operationId' => self::RouteName,
            'tags' => [Tags::Storage],
            'summary' => self::RouteSummary,
            'parameters' => [
                new Parameter([
                    'name' => 'id',
                    'in' => 'path',
                    'required' => true,
                    'schema' => UuidSchema::spec(),
                ]),
            ],
            'responses' => new Responses([
                Response::HTTP_OK => new OpenApiResponse([
                    'description' => 'Storage detail returned.',
                    'content' => [
                        'application/json' => new MediaType([
                            'schema' => DetailSchema::getSchemaSpec(),
                        ]),
                    ],
                ]),
                Response::HTTP_NOT_FOUND => ErrorsOpenApiResponse::spec('Storage does not exist.'),
            ]),
        ]);

        return [$this->pathGenerator->generate(self::RouteName) => new PathItem(['get' => $get])];
    }
}
