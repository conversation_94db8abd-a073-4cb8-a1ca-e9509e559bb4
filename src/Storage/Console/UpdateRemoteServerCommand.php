<?php

declare(strict_types=1);

namespace Cdn77\Api\Storage\Console;

use Cdn77\Api\Core\Application\Console\LockAcquirer;
use Cdn77\Api\Core\Application\Console\Value\LockName;
use Cdn77\Api\Core\Console\CronCommand;
use Cdn77\Api\Core\Domain\Entity\Storage\StorageServerId;
use Cdn77\Api\Core\Domain\Messaging\QueryBus;
use Cdn77\Api\Storage\Domain\Exception\ServersUpdateFailed;
use Cdn77\Api\Storage\Domain\Exception\ServerUpdateFailed;
use Cdn77\Api\Storage\Domain\Query\UpdateStorageOnAllRemoteServers;
use Cdn77\Api\Storage\Domain\Query\UpdateStoragesOnRemoteServer;
use Cdn77\Api\StorageLocation\Domain\Exception\CannotFindStorageServer;
use Psr\Log\LoggerInterface;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\OutputStyle;
use Symfony\Component\Console\Style\SymfonyStyle;
use Webmozart\Assert\Assert;

use function sprintf;

final class UpdateRemoteServerCommand extends Command implements CronCommand
{
    public const string Name = 'clap:storage:remote:update';
    private const string Alias = 'api:storage:remote:update';

    private const string ServerOptionName = 'server';

    private const string ServersUpdatedMessage = 'All servers have been updated.';
    private const string ServersFailedMessage = 'Failed to update the servers.';
    private const string ServerNotFoundMessage = 'The server "%s" does not exist.';
    private const string ServerUpdatedMessage = 'The server "%s" has been updated.';
    private const string ServerFailedMessage = 'Failed to update the server "%s".';

    public function __construct(
        private readonly LoggerInterface $logger,
        private readonly QueryBus $queryBus,
        private readonly LockAcquirer $lockAcquirer,
    ) {
        parent::__construct();
    }

    public function configure(): void
    {
        $this->setName(self::Name);
        $this->setAliases([self::Alias]);
        $this->setDescription('Updates storages on remote servers.');
        $this->addArgument(
            self::ServerOptionName,
            InputArgument::OPTIONAL,
            'The server to update or all if not specified.',
        );

        parent::configure();
    }

    public function execute(InputInterface $input, OutputInterface $output): int
    {
        $this->lockAcquirer->execute(LockName::fromCommand($this));
        $style = new SymfonyStyle($input, $output);

        $serverId = $input->getArgument(self::ServerOptionName);

        if ($serverId === null) {
            return $this->executeForAllServers($style);
        }

        Assert::string($serverId);

        return $this->executeForSingleServer(StorageServerId::fromString($serverId), $style);
    }

    private function executeForAllServers(OutputStyle $style): int
    {
        try {
            $this->queryBus->handle(new UpdateStorageOnAllRemoteServers());

            $this->logger->info(self::ServersUpdatedMessage);
            $style->success(self::ServersUpdatedMessage);

            return Command::SUCCESS;
        } catch (ServersUpdateFailed $e) {
            $this->logger->critical(self::ServersFailedMessage, ['exception' => $e]);
            $style->error(self::ServersFailedMessage);

            return Command::FAILURE;
        }
    }

    private function executeForSingleServer(StorageServerId $storageServerId, OutputStyle $style): int
    {
        try {
            $this->queryBus->handle(new UpdateStoragesOnRemoteServer($storageServerId));

            $this->logger->info(sprintf(self::ServerUpdatedMessage, $storageServerId->toString()));
            $style->success(sprintf(self::ServerUpdatedMessage, $storageServerId->toString()));

            return Command::SUCCESS;
        } catch (ServerUpdateFailed | CannotFindStorageServer $e) {
            if ($e instanceof CannotFindStorageServer) {
                $style->writeln(sprintf(self::ServerNotFoundMessage, $storageServerId->toString()));
            }

            if ($e instanceof ServerUpdateFailed) {
                $this->logger->critical(
                    sprintf(self::ServerFailedMessage, $storageServerId->toString()),
                    ['exception' => $e],
                );
                $style->error(sprintf(self::ServerFailedMessage, $storageServerId->toString()));
            }

            return Command::FAILURE;
        }
    }
}
