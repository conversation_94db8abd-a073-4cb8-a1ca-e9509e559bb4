<?php

declare(strict_types=1);

namespace Cdn77\Api\Storage\Infrastructure\Finder;

use Cdn77\Api\Core\Domain\Entity\Customer\CustomerUuid;
use Cdn77\Api\Core\Domain\Entity\Storage\StorageIdentifier;
use Cdn77\Api\Core\Infrastructure\EntityManagerConstructor;
use Cdn77\Api\Storage\Domain\Finder\StorageIdFinder;

use function array_map;

final class DoctrineStorageIdFinder implements StorageIdFinder
{
    use EntityManagerConstructor;

    public function findForCustomer(CustomerUuid $customerId): array
    {
        /** @var list<string> $storageIds */
        $storageIds = $this->entityManager->getConnection()->fetchFirstColumn(
            <<<'PSQL'
SELECT s.new_id
FROM storages.zone s
INNER JOIN public.accounts acc ON acc.id = s.user_id AND acc.new_id = :customerId
WHERE s.deleted IS NULL
PSQL
            ,
            ['customerId' => $customerId->toUid()],
        );

        return array_map(
            static fn (string $storageId): StorageIdentifier => StorageIdentifier::fromString($storageId),
            $storageIds,
        );
    }
}
