<?php

declare(strict_types=1);

namespace Cdn77\Api\Invoice\Domain\Value;

use Cdn77\ValueObject\Cache\Cache;
use Cdn77\ValueObject\Cache\HasCache;
use Webmozart\Assert\Assert;

final class CountryIsoCode implements HasCache
{
    use Cache;

    public const string IsoFormatPattern = '~^[A-Z]{2,3}$~'; // https://regex101.com/r/fwP0nY/1

    private const string IsoArgentina = 'AR';
    private const string IsoBolivia = 'BO';
    private const string IsoBrazil = 'BR';
    private const string IsoChile = 'CL';
    private const string IsoColombia = 'CO';
    private const string IsoEcuador = 'EC';
    private const string IsoGb = 'GB';
    private const string IsoFrenchGuiana = 'GF';
    private const string IsoGuyana = 'GY';
    private const string IsoFalklandIslands = 'FK';
    private const string IsoPeru = 'PE';
    private const string IsoParaguay = 'PY';
    private const string IsoSuriname = 'SR';
    private const string IsoUnknown = 'XX';
    private const string IsoUruguay = 'UY';
    private const string IsoVenezuela = 'VE';

    private function __construct(public string $isoCode)
    {
    }

    public static function fromString(string $isoCode): self
    {
        Assert::regex($isoCode, self::IsoFormatPattern);

        return self::cached($isoCode);
    }

    public static function getUkIso(): self
    {
        return self::fromString(self::IsoGb);
    }

    /** @return list<self> */
    public static function getLatamIsoCodes(): array
    {
        return [
            self::fromString(self::IsoArgentina),
            self::fromString(self::IsoBolivia),
            self::fromString(self::IsoBrazil),
            self::fromString(self::IsoChile),
            self::fromString(self::IsoColombia),
            self::fromString(self::IsoEcuador),
            self::fromString(self::IsoFrenchGuiana),
            self::fromString(self::IsoGuyana),
            self::fromString(self::IsoFalklandIslands),
            self::fromString(self::IsoPeru),
            self::fromString(self::IsoParaguay),
            self::fromString(self::IsoSuriname),
            self::fromString(self::IsoUruguay),
            self::fromString(self::IsoVenezuela),
        ];
    }

    public function get(): string
    {
        return $this->isoCode;
    }

    public function getIso2(): string
    {
        Assert::length($this->isoCode, 2, 'Provided ISO code is not in ISO2 format.');

        return $this->isoCode;
    }

    public function equals(self $isoToCompare): bool
    {
        return $this->get() === $isoToCompare->get();
    }

    public function isUnknown(): bool
    {
        return $this->isoCode === self::IsoUnknown;
    }
}
