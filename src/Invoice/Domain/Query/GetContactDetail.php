<?php

declare(strict_types=1);

namespace Cdn77\Api\Invoice\Domain\Query;

use Cdn77\Api\Core\Domain\Entity\Customer\CustomerId;
use Cdn77\Api\Core\Domain\Messaging\Query;
use Cdn77\Api\Invoice\Domain\Dto\ContactDetail;

/** @implements Query<ContactDetail, GetContactDetailHandler> */
final readonly class GetContactDetail implements Query
{
    public function __construct(private CustomerId $customerId)
    {
    }

    public function getCustomerId(): CustomerId
    {
        return $this->customerId;
    }
}
