<?php

declare(strict_types=1);

namespace Cdn77\Api\Invoice\Domain\Query\FindUnpaidInvoices;

use Brick\Money\Money;
use Cdn77\Api\Core\Domain\Billing\Biller;
use Cdn77\Api\Core\Domain\Currency\CurrencyConverter;
use Cdn77\Api\Core\Domain\Messaging\QueryHandler;
use Cdn77\Api\CoreLibrary\Money\CurrencyCode;
use Cdn77\Api\Invoice\Domain\Query\FindUnpaidInvoices\Output\UnpaidInvoice;
use Cdn77\Api\Invoice\Domain\Query\FindUnpaidInvoices\Output\UnpaidInvoices;
use Cdn77\Api\Invoice\Domain\Repository\InvoiceRepository;
use Cdn77\Api\Invoice\Domain\Repository\XeroContactRepository;
use Cdn77\Api\Invoice\Domain\Value\InvoiceNumber;
use Cdn77\Api\Invoice\Domain\Value\InvoiceStatus;
use Cdn77\Api\Payment\Domain\Repository\CustomPlanContractRepository;
use DateTimeImmutable;
use Psr\Clock\ClockInterface;
use Symfony\Component\Uid\Uuid;
use XeroPHP\Models\Accounting\Invoice;

use function sprintf;

final readonly class GetAllUnpaidHandler implements QueryHandler
{
    private const int DefaultDayOffset = 14;

    public function __construct(
        private Biller $biller,
        private ClockInterface $clock,
        private CurrencyConverter $currencyConverter,
        private CustomPlanContractRepository $contractRepository,
        private InvoiceRepository $invoiceRepository,
        private XeroContactRepository $xeroContactRepository,
    ) {
    }

    public function handle(GetAllUnpaid $query): UnpaidInvoices
    {
        $now = $this->clock->now();
        $fromDate = $query->filter->from ?? $now->modify(sprintf('-%d days', self::DefaultDayOffset));

        return new UnpaidInvoices($this->biller
            ->getUnpaidManualInvoices()
            ->merge($this->biller->getPaidManualInvoicesFrom($fromDate))
            ->map(
                function (Invoice $xeroInvoice): UnpaidInvoice {
                    $isPaid = $xeroInvoice->getStatus() === InvoiceStatus::Paid->value;
                    $total = $isPaid
                        ? Money::of($xeroInvoice->getAmountPaid(), $xeroInvoice->getCurrencyCode())
                        : Money::of($xeroInvoice->getAmountDue(), $xeroInvoice->getCurrencyCode());

                    $invoiceNumber = InvoiceNumber::fromString($xeroInvoice->getInvoiceNumber());
                    $invoice = $this->invoiceRepository
                        ->findForInvoiceNumber($invoiceNumber);
                    $xeroContact = $this->xeroContactRepository
                        ->findForXeroUuid(Uuid::fromString($xeroInvoice->getContact()->getContactID()));
                    $usdTotal = $this->currencyConverter->convertAtDate(
                        $total,
                        CurrencyCode::USD,
                        DateTimeImmutable::createFromInterface($xeroInvoice->getDate()),
                    );
                    $isResolved = $invoice !== null && $invoice->isConsideredPaid();
                    $contract = $invoice === null
                        ? null
                        : $this->contractRepository->findForInvoiceAndCustomer(
                            $invoiceNumber,
                            $invoice->customer()->getId(),
                        );

                    return new UnpaidInvoice(
                        $xeroContact,
                        $xeroInvoice,
                        $usdTotal,
                        $contract?->customPlan()->guardian(),
                        $isResolved,
                    );
                },
            )
            ->sorted(
                static fn (UnpaidInvoice $a, UnpaidInvoice $b) => $a->invoice->getDueDate()->getTimestamp()
                    <=> $b->invoice->getDueDate()->getTimestamp(),
            ));
    }
}
