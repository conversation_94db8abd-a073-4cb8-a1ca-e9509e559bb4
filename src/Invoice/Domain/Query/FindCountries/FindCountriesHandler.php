<?php

declare(strict_types=1);

namespace Cdn77\Api\Invoice\Domain\Query\FindCountries;

use Cdn77\Api\Core\Domain\Messaging\QueryHandler;
use Cdn77\Api\Invoice\Domain\Finder\CountriesFinder;
use Cdn77\Api\Invoice\Domain\Query\FindCountries\Output\Countries;

final readonly class FindCountriesHandler implements QueryHandler
{
    public function __construct(
        private CountriesFinder $countriesFinder,
    ) {
    }

    public function handle(FindCountries $query): Countries
    {
        return $this->countriesFinder->find($query->selectionFields, $query->filter);
    }
}
