<?php

declare(strict_types=1);

namespace Cdn77\Api\Invoice\Domain\Exception;

use Cdn77\Api\Core\Domain\Exception\ClapDomainException;
use Cdn77\Api\Core\Domain\Exception\SupportsHttpResponse;
use DomainException;
use Symfony\Component\HttpFoundation\Response;
use XeroPHP\Exception as XeroException;

final class XeroContactCouldNotBeCreated
    extends DomainException
    implements ClapDomainException, SupportsHttpResponse
{
    public static function fromXeroException(XeroException $exception): self
    {
        return new self('Xero contact could not be created.', 0, $exception);
    }

    public function getResponseCode(): int
    {
        return Response::HTTP_UNPROCESSABLE_ENTITY;
    }
}
