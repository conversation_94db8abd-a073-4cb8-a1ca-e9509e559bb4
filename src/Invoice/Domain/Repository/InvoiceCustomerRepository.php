<?php

declare(strict_types=1);

namespace Cdn77\Api\Invoice\Domain\Repository;

use Cdn77\Api\Core\Domain\Entity\Customer\CustomerId;
use Cdn77\Api\Core\Domain\Entity\Invoice\CountryId;
use Cdn77\Api\Core\Domain\Entity\Invoice\InvoiceCustomer;

interface InvoiceCustomerRepository
{
    public function add(InvoiceCustomer $invoiceCustomer): void;

    public function findForCustomer(CustomerId $customerId): InvoiceCustomer|null;

    public function getForCustomer(CustomerId $customerId): InvoiceCustomer;

    public function vatIdExists(string $vatId, CountryId $countryId): bool;
}
