<?php

declare(strict_types=1);

namespace Cdn77\Api\Invoice\Domain\Dto;

use Cdn77\Api\Invoice\Domain\Value\InvoiceNumber;
use DateTimeInterface;
use XeroPHP\Models\Accounting\Invoice;

use function Safe\fclose;
use function Safe\fopen;
use function Safe\fwrite;
use function sprintf;

final readonly class DownloadableInvoice
{
    public const string ContentType = 'application/pdf';
    private const string FileExtension = 'pdf';

    private function __construct(
        public DateTimeInterface $dueDate,
        private InvoiceNumber $invoiceNumber,
        private string $pdfContent,
    ) {
    }

    public static function fromXeroInvoice(Invoice $xeroInvoice): self
    {
        $invoiceNumber = InvoiceNumber::fromString($xeroInvoice->getInvoiceNumber());
        $pdfContent = $xeroInvoice->getPDF();

        return new self($xeroInvoice->getDueDate(), $invoiceNumber, $pdfContent);
    }

    public function getContent(): string
    {
        return $this->pdfContent;
    }

    public function writeContent(): void
    {
        $out = fopen('php://output', 'wb');
        fwrite($out, $this->getContent());
        fclose($out);
    }

    public function getFileName(): string
    {
        return sprintf('%s.%s', $this->invoiceNumber->toString(), self::FileExtension);
    }
}
