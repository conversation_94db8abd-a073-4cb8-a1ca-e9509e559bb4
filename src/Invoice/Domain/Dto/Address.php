<?php

declare(strict_types=1);

namespace Cdn77\Api\Invoice\Domain\Dto;

use Cdn77\Api\Core\Application\Payload\SchemaPropertyResolver;
use Cdn77\Api\Core\Domain\Entity\Invoice\CountryId;
use Cdn77\Api\Core\Domain\Entity\Invoice\InvoiceCustomer;
use Cdn77\Api\Invoice\Application\Payload\EditContactDetailSchema;

final class Address
{
    public function __construct(
        public string|null $city,
        public CountryId|null $countryId,
        public string|null $street,
        public string|null $zip,
    ) {
    }

    public static function fromContactDetailSchema(EditContactDetailSchema $schema): self
    {
        return new self(
            SchemaPropertyResolver::requireNotNull($schema->city),
            CountryId::fromInteger(SchemaPropertyResolver::requireNotNull($schema->countryId)),
            SchemaPropertyResolver::requireNotNull($schema->street),
            SchemaPropertyResolver::requireNotNull($schema->zip),
        );
    }

    public static function fromInvoiceCustomer(InvoiceCustomer $invoiceCustomer): self
    {
        return new self(
            $invoiceCustomer->getCity(),
            $invoiceCustomer->getCountryId(),
            $invoiceCustomer->getStreet(),
            $invoiceCustomer->getZip(),
        );
    }
}
