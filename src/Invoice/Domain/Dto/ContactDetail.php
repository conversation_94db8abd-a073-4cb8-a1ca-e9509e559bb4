<?php

declare(strict_types=1);

namespace Cdn77\Api\Invoice\Domain\Dto;

use Cdn77\Api\Core\Application\Payload\SchemaPropertyResolver;
use Cdn77\Api\Core\Domain\Value\Customer\EmailAddress;
use Cdn77\Api\Invoice\Application\Payload\EditContactDetailSchema;
use Ds\Set;

use function array_unique;
use function array_values;
use function Cdn77\Functions\setFromIterable;

final class ContactDetail
{
    /** @param Set<EmailAddress> $emails */
    public function __construct(
        public Address $address,
        public string|null $company,
        public Set $emails,
        public string|null $fullName,
        public string|null $vatId,
    ) {
    }

    public static function fromSchema(EditContactDetailSchema $schema): self
    {
        return new self(
            Address::fromContactDetailSchema($schema),
            $schema->company,
            setFromIterable(
                array_values(array_unique($schema->emails ?? [])),
                static fn (mixed $_, string $email): EmailAddress => EmailAddress::fromString($email),
            ),
            SchemaPropertyResolver::requireNotNull($schema->fullName),
            $schema->vatId,
        );
    }
}
