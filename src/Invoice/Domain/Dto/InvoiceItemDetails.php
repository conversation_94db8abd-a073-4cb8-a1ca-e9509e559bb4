<?php

declare(strict_types=1);

namespace Cdn77\Api\Invoice\Domain\Dto;

use Brick\Math\BigDecimal;
use Brick\Money\Money;
use Cdn77\Api\Core\Domain\Billing\Value\BillingDescription;
use Cdn77\Api\Core\Domain\Billing\Value\BillingPeriod;
use Cdn77\Api\Invoice\Domain\Value\XeroTaxType;

final readonly class InvoiceItemDetails
{
    public function __construct(
        public BigDecimal $discount,
        public BillingDescription $description,
        public float $quantity,
        public XeroTaxType $taxType,
        public Money $unitPrice,
        public BillingPeriod|null $billingPeriod,
        public string|null $accountCode = null,
    ) {
    }
}
