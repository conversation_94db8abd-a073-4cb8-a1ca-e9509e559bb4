<?php

declare(strict_types=1);

namespace Cdn77\Api\Invoice\Application\Payload;

use Cdn77\Api\Core\Application\Payload\OASchema;
use cebe\openapi\spec\Schema;
use cebe\openapi\spec\Type;

final class ReconciliationDataDetailSchema implements OASchema
{
    public function __construct(
        public string $invoiceNumber,
        public string $paymentMethod,
        public string $transactionId,
    ) {
    }

    public static function getSchemaSpec(): Schema
    {
        return new Schema([
            'type' => Type::OBJECT,
            'properties' => [
                'invoice_number' => new Schema(['type' => Type::STRING]),
                'payment_method' => new Schema(['type' => Type::STRING]),
                'transaction_id' => new Schema(['type' => Type::STRING]),
            ],
        ]);
    }
}
