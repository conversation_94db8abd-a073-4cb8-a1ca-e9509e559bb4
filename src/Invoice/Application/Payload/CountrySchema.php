<?php

declare(strict_types=1);

namespace Cdn77\Api\Invoice\Application\Payload;

use Cdn77\Api\Core\Application\Payload\OASchema;
use Cdn77\Api\Core\Domain\Billing\Value\VatRate;
use Cdn77\Api\Core\Domain\Entity\Invoice\Country;
use Cdn77\Api\Core\Domain\Entity\Invoice\CountryId;
use Cdn77\Api\Invoice\Domain\Value\CountryIsoCode;
use cebe\openapi\spec\Schema;
use cebe\openapi\spec\Type;

final class CountrySchema implements OASchema
{
    public int $id;

    public string $countryIsoCode;

    public float $vatPercent;

    public function __construct(
        CountryId $id,
        CountryIsoCode $countryIsoCode,
        public string $name,
        VatRate $vatRate,
        public bool $isEuMember,
    ) {
        $this->id = $id->toInt();
        $this->countryIsoCode = $countryIsoCode->get();
        $this->vatPercent = $vatRate->decimal()->toFloat();
    }

    public static function fromCountry(Country $country): self
    {
        return new self(
            $country->getId(),
            $country->getIso(),
            $country->getShortName(),
            $country->vatRate(),
            $country->isEuMember(),
        );
    }

    public static function getSchemaSpec(): Schema
    {
        return new Schema([
            'type' => Type::OBJECT,
            'properties' => [
                'id' => new Schema(['type' => Type::INTEGER]),
                'country_iso_code' => new Schema(['type' => Type::STRING]),
                'name' => new Schema(['type' => Type::STRING]),
                'vat_percent' => new Schema(['type' => Type::NUMBER]),
                'is_eu_member' => new Schema(['type' => Type::BOOLEAN]),
            ],
        ]);
    }
}
