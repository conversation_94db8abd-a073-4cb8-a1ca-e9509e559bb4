<?php

declare(strict_types=1);

namespace Cdn77\Api\Invoice\Application\Payload;

use Cdn77\Api\Core\Application\OpenApi\Schema\DateTimeSchema;
use Cdn77\Api\Core\Application\Payload\OASchema;
use Cdn77\Api\Core\Application\Payload\SchemaPropertyValidator;
use Cdn77\Api\Core\Application\Payload\SupportsDtoDeserialization;
use Cdn77\Api\Invoice\Application\Payload\Dto\NewInvoice;
use cebe\openapi\spec\Schema;
use cebe\openapi\spec\Type;
use DateTimeImmutable;
use J<PERSON>\Serializer\Annotation as Serializer;

/** @implements SupportsDtoDeserialization<NewInvoice> */
final class NewInvoiceSchema implements OASchema, SupportsDtoDeserialization
{
    public string|null $brandingThemeId = null;

    public string|null $currencyCode = null;

    public int|null $customerId = null;

    public DateTimeImmutable|null $dueDate = null;

    public DateTimeImmutable|null $invoiceDate = null;

    /** @var list<InvoiceItemSchema>|null */
    #[Serializer\Type('array<' . InvoiceItemSchema::class . '>')]
    public array|null $invoiceItems = null;

    public string|null $invoiceStatus = null;

    public string|null $taxAmount = null;

    private function __construct()
    {
    }

    public static function getSchemaSpec(): Schema
    {
        return new Schema([
            'type' => Type::OBJECT,
            'properties' => [
                'branding_theme_id' => new Schema(['type' => Type::STRING]),
                'currency_code' => new Schema(['type' => Type::STRING]),
                'customer_id' => new Schema(['type' => Type::INTEGER]),
                'due_date' => DateTimeSchema::spec(),
                'invoice_date' => DateTimeSchema::spec(),
                'invoice_items' => new Schema(['type' => Type::ARRAY, 'items' => InvoiceItemSchema::getSchemaSpec()]),
                'invoice_status' => new Schema(['type' => Type::STRING]),
                'tax_amount' => new Schema(['type' => Type::STRING]),
            ],
        ]);
    }

    public function validateSchemaProperties(): iterable
    {
        yield SchemaPropertyValidator::isNotNull($this->brandingThemeId, 'branding_theme_id');
        yield SchemaPropertyValidator::isNotNull($this->currencyCode, 'currency_code');
        yield SchemaPropertyValidator::isNotNull($this->customerId, 'customer_id');
        yield SchemaPropertyValidator::isNotNull($this->dueDate, 'due_date');
        yield SchemaPropertyValidator::isNotNull($this->invoiceDate, 'invoice_date');
        yield SchemaPropertyValidator::isNotNull($this->invoiceItems, 'invoice_items');
        yield SchemaPropertyValidator::isNotNull($this->invoiceStatus, 'invoice_status');
        yield SchemaPropertyValidator::isNotNull($this->taxAmount, 'tax_amount');

        foreach ($this->invoiceItems ?? [] as $invoiceItem) {
            yield from $invoiceItem->validateSchemaProperties();
        }
    }

    public function toDto(): NewInvoice
    {
        return NewInvoice::fromSchema($this);
    }
}
