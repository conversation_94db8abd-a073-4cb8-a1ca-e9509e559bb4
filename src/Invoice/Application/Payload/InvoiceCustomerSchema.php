<?php

declare(strict_types=1);

namespace Cdn77\Api\Invoice\Application\Payload;

use Cdn77\Api\Core\Application\OpenApi\Schema\IntegerSchema;
use Cdn77\Api\Core\Application\OpenApi\Schema\ObjectSchema;
use Cdn77\Api\Core\Application\OpenApi\Schema\StringSchema;
use Cdn77\Api\Core\Application\Payload\OASchema;
use Cdn77\Api\Core\Domain\Entity\Customer\CustomerId;
use Cdn77\Api\Core\Domain\Entity\Invoice\XeroContact;
use Cdn77\Api\Core\Domain\Value\Customer\EmailAddress;
use cebe\openapi\spec\Schema;

final class InvoiceCustomerSchema implements OASchema
{
    public const string FieldId = 'id';
    public const string FieldEmail = 'email';
    public const string FieldName = 'name';

    public int|null $id;

    public string|null $email;

    public function __construct(CustomerId|null $customerId, EmailAddress|null $email, public string $name)
    {
        $this->id = $customerId?->toInt();
        $this->email = $email?->get();
    }

    public static function fromXeroContact(XeroContact|null $xeroContact, string $contactName): self
    {
        return new self(
            $xeroContact?->getCustomer()->getId(),
            $xeroContact?->getCustomer()->getCredentials()->getEmail(),
            $contactName,
        );
    }

    public static function getSchemaSpec(): Schema
    {
        return ObjectSchema::spec([
            self::FieldId => IntegerSchema::spec(),
            self::FieldEmail => StringSchema::spec(),
            self::FieldName => StringSchema::spec(),
        ]);
    }
}
