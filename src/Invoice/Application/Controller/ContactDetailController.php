<?php

declare(strict_types=1);

namespace Cdn77\Api\Invoice\Application\Controller;

use Cdn77\Api\Core\Application\Controller\HasInternalOpenApiPaths;
use Cdn77\Api\Core\Application\OpenApi\Model\Tags;
use Cdn77\Api\Core\Application\OpenApi\PathGenerator;
use Cdn77\Api\Core\Application\Response\ControllerQueryHandler;
use Cdn77\Api\Core\Application\Symfony\LoggedAccountProvider;
use Cdn77\Api\Invoice\Application\Payload\ContactDetailSchema;
use Cdn77\Api\Invoice\Domain\Query\GetContactDetail;
use cebe\openapi\spec\MediaType;
use cebe\openapi\spec\Operation;
use cebe\openapi\spec\PathItem;
use cebe\openapi\spec\Response as OpenApiResponse;
use cebe\openapi\spec\Responses;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;

final readonly class ContactDetailController implements HasInternalOpenApiPaths
{
    public const string RouteName = 'invoice.contactDetail';
    private const string RouteSummary = 'Detail of billing details';

    public function __construct(
        private ControllerQueryHandler $controllerQueryHandler,
        private LoggedAccountProvider $loggedAccountProvider,
        private PathGenerator $pathGenerator,
    ) {
    }

    #[Route(path: '/invoice/contact-detail', name: self::RouteName, methods: [Request::METHOD_GET])]
    public function execute(): Response
    {
        return $this->controllerQueryHandler->handle(
            new GetContactDetail($this->loggedAccountProvider->legacyId()),
            ContactDetailSchema::class,
        );
    }

    public function getPathItems(): array
    {
        $get = new Operation(
            [
                'operationId' => self::RouteName,
                'tags' => [Tags::BillingDetails],
                'summary' => self::RouteSummary,
                'responses' => new Responses(
                    [
                        Response::HTTP_OK => new OpenApiResponse(
                            [
                                'description' => 'Customer contact details returned.',
                                'content' => [
                                    'application/json' => new MediaType([
                                        'schema' => ContactDetailSchema::getSchemaSpec(),
                                    ]),
                                ],
                            ],
                        ),
                    ],
                ),
            ],
        );

        return [$this->pathGenerator->generate(self::RouteName) => new PathItem(['get' => $get])];
    }
}
