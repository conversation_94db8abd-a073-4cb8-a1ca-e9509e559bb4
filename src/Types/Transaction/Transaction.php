<?php

declare(strict_types=1);

namespace Paygate\Types\Transaction;

use DateTimeImmutable;
use Paygate\WithDeprecatedGetters;

class Transaction
{
    use WithDeprecatedGetters;

    public function __construct(
        public string $id,
        public string $accountId,
        public string $currency,
        public string $amount,
        public DateTimeImmutable $created,
        public string $description,
    ) {
    }

    /** @param array<mixed> $data */
    public static function from(array $data): self
    {
        return new self(
            $data['id'],
            $data['accountId'],
            $data['currency'],
            $data['amount'],
            new DateTimeImmutable($data['created']),
            $data['description'],
        );
    }
}
