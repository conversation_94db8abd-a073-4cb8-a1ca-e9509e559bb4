<?php

declare(strict_types=1);

namespace Paygate\Types\Exception;

use Throwable;

class Exception extends \Exception
{
    public function __construct(
        string $message,
        private string|null $debugToken = null,
        Throwable|null $previous = null,
    ) {
        parent::__construct($message, 0, $previous);
    }

    public function getDebugToken(): string|null
    {
        return $this->debugToken;
    }
}
