<?php

declare(strict_types=1);

namespace Paygate\Types\PaymentMethod;

use DateTimeImmutable;
use Paygate\WithDeprecatedGetters;

final readonly class WireTransferDetails
{
    use WithDeprecatedGetters;

    public function __construct(
        public string $bankId,
        public string|null $reference,
        public DateTimeImmutable $received,
    ) {
    }

    /** @param array<mixed> $data */
    public static function from(array $data): self
    {
        return new self($data['bankId'], $data['reference'], new DateTimeImmutable($data['received']));
    }
}
