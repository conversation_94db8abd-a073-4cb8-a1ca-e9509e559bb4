<?php

declare(strict_types=1);

namespace Cdn77\Api\Server\Domain\Command;

use Cdn77\Api\Core\Domain\Ara\Ara;
use Cdn77\Api\Core\Domain\Entity\Datacenter\Location;
use Cdn77\Api\Core\Domain\Entity\Datacenter\Server;
use Cdn77\Api\Core\Domain\Entity\Datacenter\ServerId;
use Cdn77\Api\Core\Domain\MC\Dto\Location as LocationDto;
use Cdn77\Api\Core\Domain\MC\MCApi;
use Cdn77\Api\Core\Domain\Messaging\CommandHandler;
use Cdn77\Api\CoreLibrary\Money\PricePerByteConverter;
use Cdn77\Api\Datacenter\Domain\Exception\LocationNotFound;
use Cdn77\Api\Datacenter\Domain\Exception\PopNotFound;
use Cdn77\Api\Datacenter\Domain\Repository\LocationRepository;
use Cdn77\Api\Plan\Domain\Repository\PlanLocationRepository;
use Cdn77\Api\Server\Domain\Dto\Server as ServerDto;
use Cdn77\Api\Server\Domain\Value\Description;
use Cdn77\Api\Server\Domain\Value\LocationId;
use Cdn77\Api\Server\Domain\Value\ServerType;
use Cdn77\Api\Stats\Domain\Finder\ServerIdFinder;
use Cdn77\Api\Stats\Domain\Repository\ServerRepository;
use DateTimeImmutable;
use Doctrine\ORM\NonUniqueResultException;
use Ds\Map;
use OutOfBoundsException;
use Psr\Clock\ClockInterface;
use Psr\Log\LoggerInterface;

use function Cdn77\Functions\absurd;

final readonly class InsertNewServersHandler implements CommandHandler
{
    private const int TwoWeekInDays = 14;

    public function __construct(
        private Ara $ara,
        private ClockInterface $clock,
        private LocationRepository $locationRepository,
        private LoggerInterface $logger,
        private MCApi $mcApi,
        private PlanLocationRepository $planLocationRepository,
        private ServerIdFinder $serverIdFinder,
        private ServerRepository $serverRepository,
    ) {
    }

    public function handle(InsertNewServers $command): void
    {
        $now = $this->clock->now();
        $locations = $this->mcApi->getLocations();
        $pops = $this->mcApi->getPops();

        foreach ($this->getNewServers() as $serverId => $newServer) {
            $popId = $newServer->popId;
            $pop = $pops->get($popId, null);

            if ($pop === null) {
                $this->logger->error(PopNotFound::fromPopId($popId)->getMessage());

                continue;
            }

            try {
                $location = $this->locationRepository->getForCityCode($pop->cityCode);
            } catch (LocationNotFound | NonUniqueResultException $e) {
                $this->logger->error($e->getMessage());

                continue;
            }

            $server = new Server(
                $serverId,
                $now,
                $location,
                $pop->serverType,
                $this->getDescription($locations, $pop->locationId),
            );

            $this->serverRepository->add($server);

            if ($pop->serverType === ServerType::Storage) {
                continue;
            }

            $this->createAraPlanServer($location, $serverId, $now);
        }
    }

    /** @param Map<LocationId, LocationDto> $locations */
    private function getDescription(Map $locations, LocationId $locationId): Description
    {
        try {
            return $locations->get($locationId)->description;
        } catch (OutOfBoundsException) {
            absurd();
        }
    }

    /** @return Map<ServerId, ServerDto> */
    private function getNewServers(): Map
    {
        $serverIds = $this->serverIdFinder->findAll();
        $servers = $this->mcApi->getServers();

        return $servers->filter(
            static fn (ServerId $_): bool => ! $serverIds->contains($_),
        );
    }

    private function createAraPlanServer(Location $location, ServerId $serverId, DateTimeImmutable $now): void
    {
        $activePlanLocations = $this->planLocationRepository->findForServer(
            $now,
            $location->getId(),
            self::TwoWeekInDays,
        );

        foreach ($activePlanLocations as $planLocation) {
            $this->ara->createPlanServer(
                $planLocation->getPlan()->getId(),
                PricePerByteConverter::convertFromTib($planLocation->pricePerTiB()),
                $serverId,
            );
        }
    }
}
