<?php

declare(strict_types=1);

namespace Cdn77\Api\Server\Domain\Value;

namespace Cdn77\Api\Server\Domain\Value;

use Cdn77\Api\Core\Domain\Value\Identifier\StringIdentifier;
use Ds\Set;

final class Description extends StringIdentifier
{
    public const string PragueO2Chodov = 'Prague Chodov O2';
    public const string PragueO2Stodulky = 'Prague Stodulky O2';

    public static function pragueO2Chodov(): self
    {
        return self::fromString(self::PragueO2Chodov);
    }

    public static function pragueO2Stodulky(): self
    {
        return self::fromString(self::PragueO2Stodulky);
    }

    /** @return Set<self> */
    public static function customCraDescriptions(): Set
    {
        return self::fromStrings([
            self::PragueO2Chodov,
            self::PragueO2Stodulky,
        ]);
    }
}
