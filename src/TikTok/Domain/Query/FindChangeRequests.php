<?php

declare(strict_types=1);

namespace Cdn77\Api\TikTok\Domain\Query;

use Cdn77\Api\Core\Domain\Entity\Customer\Customer;
use Cdn77\Api\Core\Domain\Entity\Customer\CustomerUuid;
use Cdn77\Api\Core\Domain\Entity\TikTok\ChangeRequest;
use Cdn77\Api\Core\Domain\Messaging\Query;
use Generator;

/** @implements Query<Generator<array{ChangeRequest, Customer}>, FindChangeRequestsHandler> */
final readonly class FindChangeRequests implements Query
{
    public function __construct(public CustomerUuid $customerId)
    {
    }
}
