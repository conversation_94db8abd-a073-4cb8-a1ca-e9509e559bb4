<?php

declare(strict_types=1);

namespace Cdn77\Api\TikTok\Application\Controller;

use Cdn77\Api\Core\Application\Response\ControllerQueryHandler;
use Cdn77\Api\Core\Application\Symfony\LoggedAccountProvider;
use Cdn77\Api\TikTok\Application\Payload\ChangeRequestsSchema;
use Cdn77\Api\TikTok\Domain\Query\FindChangeRequests;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;

final readonly class ChangeRequestListController
{
    public const string RouteName = 'tiktok.change-requests';

    public function __construct(
        private ControllerQueryHandler $controllerQueryHandler,
        private LoggedAccountProvider $loggedAccountProvider,
    ) {
    }

    #[Route('/tiktok/change-requests', name: self::RouteName, methods: [Request::METHOD_GET])]
    public function execute(): Response
    {
        $customerId = $this->loggedAccountProvider->customerId();

        return $this->controllerQueryHandler->handle(
            new FindChangeRequests($customerId),
            ChangeRequestsSchema::class,
        );
    }
}
