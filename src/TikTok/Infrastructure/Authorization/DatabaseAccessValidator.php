<?php

declare(strict_types=1);

namespace Cdn77\Api\TikTok\Infrastructure\Authorization;

use Cdn77\Api\Core\Domain\Entity\Customer\Customer;
use Cdn77\Api\Core\Domain\Validation\TeamMemberAccessValidator;
use Cdn77\Api\Core\Domain\Value\AccessRestriction\AccessType;
use Cdn77\Api\Core\Domain\Value\AccessRestriction\ResourceRestriction;
use Cdn77\Api\TikTok\Domain\Authorization\AccessValidator;

final readonly class DatabaseAccessValidator implements AccessValidator
{
    public function __construct(private TeamMemberAccessValidator $teamMemberAccessValidator)
    {
    }

    public function isAllowed(Customer $customer, AccessType $accessType): bool
    {
        $parentAccountId = $customer->getParentId();

        if ($parentAccountId === null) {
            return $customer->isTikTokAccount();
        }

        return $this->teamMemberAccessValidator->hasAccessToRestrictedContext(
            $accessType,
            $customer->getNewId(),
            ResourceRestriction::none(),
        );
    }
}
