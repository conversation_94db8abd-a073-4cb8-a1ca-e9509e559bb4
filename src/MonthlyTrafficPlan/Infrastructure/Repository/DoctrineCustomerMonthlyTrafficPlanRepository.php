<?php

declare(strict_types=1);

namespace Cdn77\Api\MonthlyTrafficPlan\Infrastructure\Repository;

use Cdn77\Api\Core\Domain\Entity\Customer\CustomerId;
use Cdn77\Api\Core\Domain\Entity\MonthlyTrafficPlan\CustomerMonthlyTrafficPlan;
use Cdn77\Api\Core\Infrastructure\EntityManagerConstructor;
use Cdn77\Api\MonthlyTrafficPlan\Domain\Exception\CustomerMonthlyTrafficPlanNotFound;
use Cdn77\Api\MonthlyTrafficPlan\Domain\Repository\CustomerMonthlyTrafficPlanRepository;
use Webmozart\Assert\Assert;

final readonly class DoctrineCustomerMonthlyTrafficPlanRepository implements CustomerMonthlyTrafficPlanRepository
{
    use EntityManagerConstructor;

    public function add(CustomerMonthlyTrafficPlan $customerMonthlyTrafficPlan): void
    {
        $this->entityManager->persist($customerMonthlyTrafficPlan);
    }

    public function findLatestForCustomer(CustomerId $customerId): CustomerMonthlyTrafficPlan|null
    {
        $plan = $this->entityManager
            ->createQuery(
                <<<'DQL'
SELECT cmp
FROM Cdn77\Api\Core\Domain\Entity\MonthlyTrafficPlan\CustomerMonthlyTrafficPlan cmp
WHERE cmp.customer = :customer 
ORDER BY cmp.activeUntil DESC 
DQL,
            )
            ->setMaxResults(1)
            ->setParameters(['customer' => $customerId->toInt()])
            ->getOneOrNullResult();

        Assert::nullOrIsInstanceOf($plan, CustomerMonthlyTrafficPlan::class);

        return $plan;
    }

    public function getLatestForCustomer(CustomerId $customerId): CustomerMonthlyTrafficPlan
    {
        $latestCustomerMonthlyTrafficPlan = $this->findLatestForCustomer($customerId);

        if ($latestCustomerMonthlyTrafficPlan === null) {
            throw CustomerMonthlyTrafficPlanNotFound::forCustomerId($customerId);
        }

        return $latestCustomerMonthlyTrafficPlan;
    }
}
