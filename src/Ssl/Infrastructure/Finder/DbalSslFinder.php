<?php

declare(strict_types=1);

namespace Cdn77\Api\Ssl\Infrastructure\Finder;

use Cdn77\Api\Core\Domain\Entity\Customer\CustomerId;
use Cdn77\Api\Core\Infrastructure\DbalConnectionConstructor;
use Cdn77\Api\Ssl\Domain\Finder\SslFinder;
use Cdn77\Api\Ssl\Domain\Value\Certificate;
use Doctrine\DBAL\Exception;
use Webmozart\Assert\Assert;

final class DbalSslFinder implements SslFinder
{
    use DbalConnectionConstructor;

    /** @throws Exception */
    public function existsForCustomer(Certificate $certificate, CustomerId $customerId): bool
    {
        $result = $this->connection->fetchOne(
            <<<'PSQL'
            SELECT count(ssl.id)
            FROM public.service_ssl ssl
            JOIN public.services_new service ON ssl.service_id = service.id
            WHERE service.account_id = :customerId
                AND ssl.ssl_crt = :certificate
                AND ssl.deleted IS NULL
            PSQL,
            ['certificate' => $certificate->get(), 'customerId' => $customerId->toInt()],
        );

        Assert::integer($result);

        return $result > 0;
    }
}
