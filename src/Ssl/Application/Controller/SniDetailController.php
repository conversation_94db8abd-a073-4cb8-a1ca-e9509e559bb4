<?php

declare(strict_types=1);

namespace Cdn77\Api\Ssl\Application\Controller;

use Cdn77\Api\Core\Application\Controller\HasOpenApiPaths;
use Cdn77\Api\Core\Application\OpenApi\ErrorsOpenApiResponse;
use Cdn77\Api\Core\Application\OpenApi\Model\ComponentReference;
use Cdn77\Api\Core\Application\OpenApi\Model\Tags;
use Cdn77\Api\Core\Application\OpenApi\PathGenerator;
use Cdn77\Api\Core\Application\Response\ControllerQueryHandler;
use Cdn77\Api\Core\Application\Symfony\LoggedAccountProvider;
use Cdn77\Api\Ssl\Application\Docs\SslSchemaSpec;
use Cdn77\Api\Ssl\Application\Payload\SslSchema;
use Cdn77\Api\Ssl\Domain\Contract\SslId;
use Cdn77\Api\Ssl\Domain\Enum\SslType;
use Cdn77\Api\Ssl\Domain\Query\GetSsl;
use cebe\openapi\spec\MediaType;
use cebe\openapi\spec\Operation;
use cebe\openapi\spec\Parameter;
use cebe\openapi\spec\PathItem;
use cebe\openapi\spec\Responses;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;

final readonly class SniDetailController implements HasOpenApiPaths
{
    public const string RouteName = 'ssl.sni.detail';
    public const string RouteSummary = 'Detail of SNI SSL Certificate';

    public function __construct(
        private ControllerQueryHandler $controllerQueryHandler,
        private LoggedAccountProvider $loggedAccountProvider,
        private PathGenerator $pathGenerator,
    ) {
    }

    #[Route(path: '/ssl/sni/{id}', name: self::RouteName, methods: [Request::METHOD_GET])]
    public function execute(SslId $id): JsonResponse
    {
        $customerId = $this->loggedAccountProvider->customerId();

        return $this->controllerQueryHandler->handle(
            new GetSsl($customerId, $id, SslType::Sni),
            SslSchema::class,
        );
    }

    public function getPathItems(): array
    {
        $get = new Operation([
            'operationId' => self::RouteName,
            'tags' => [Tags::Ssl],
            'summary' => self::RouteSummary,
            'parameters' => [
                new Parameter([
                    'in' => 'path',
                    'name' => 'id',
                    'required' => true,
                    'schema' => SslId::getSchemaSpec(),
                ]),
            ],
            'responses' => new Responses([
                Response::HTTP_OK => new \cebe\openapi\spec\Response([
                    'description' => 'Detail of customer SSL SNI Certificate returned.',
                    'content' => [
                        'application/json' => new MediaType([
                            'schema' => ComponentReference::forSchema(SslSchemaSpec::class),
                        ]),
                    ],
                ]),
                Response::HTTP_NOT_FOUND => ErrorsOpenApiResponse::spec('SNI SSL Certificate not found.'),
                'default' => ErrorsOpenApiResponse::spec('Unknown error occurred.'),
            ]),
        ]);

        return [$this->pathGenerator->generate(self::RouteName) => new PathItem(['get' => $get])];
    }
}
