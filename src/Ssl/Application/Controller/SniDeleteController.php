<?php

declare(strict_types=1);

namespace Cdn77\Api\Ssl\Application\Controller;

use Cdn77\Api\Core\Application\Controller\HasOpenApiPaths;
use Cdn77\Api\Core\Application\OpenApi\ErrorsOpenApiResponse;
use Cdn77\Api\Core\Application\OpenApi\Model\Tags;
use Cdn77\Api\Core\Application\OpenApi\PathGenerator;
use Cdn77\Api\Core\Application\Response\ControllerCommandHandler;
use Cdn77\Api\Core\Application\Symfony\LoggedAccountProvider;
use Cdn77\Api\Ssl\Domain\Command\DeleteSsl;
use Cdn77\Api\Ssl\Domain\Contract\SslId;
use Cdn77\Api\Ssl\Domain\Enum\SslType;
use cebe\openapi\spec\Operation;
use cebe\openapi\spec\Parameter;
use cebe\openapi\spec\PathItem;
use cebe\openapi\spec\Response as OpenApiResponse;
use cebe\openapi\spec\Responses;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;

final readonly class SniDeleteController implements HasOpenApiPaths
{
    public const string RouteName = 'ssl.sni.delete';
    private const string RouteSummary = 'Delete SNI Certificate';

    public function __construct(
        private ControllerCommandHandler $controllerCommandHandler,
        private LoggedAccountProvider $loggedAccountProvider,
        private PathGenerator $pathGenerator,
    ) {
    }

    #[Route(path: '/ssl/sni/{id}', name: self::RouteName, methods: [Request::METHOD_DELETE])]
    public function execute(SslId $id): Response
    {
        return $this->controllerCommandHandler->handle(
            new DeleteSsl($this->loggedAccountProvider->legacyId(), $id, SslType::Sni),
        );
    }

    public function getPathItems(): array
    {
        $delete = new Operation([
            'operationId' => self::RouteName,
            'tags' => [Tags::Ssl],
            'summary' => self::RouteSummary,
            'parameters' => [
                new Parameter([
                    'in' => 'path',
                    'name' => 'id',
                    'required' => true,
                    'schema' => SslId::getSchemaSpec(),
                ]),
            ],
            'responses' => new Responses([
                Response::HTTP_NO_CONTENT => new OpenApiResponse(['description' => 'SSL SNI Certificate removed.']),
                Response::HTTP_NOT_FOUND => ErrorsOpenApiResponse::spec('SSL SNI Certificate not found.'),
                'default' => ErrorsOpenApiResponse::spec('Unknown error occurred.'),
            ]),
        ]);

        return [$this->pathGenerator->generate(self::RouteName) => new PathItem(['delete' => $delete])];
    }
}
