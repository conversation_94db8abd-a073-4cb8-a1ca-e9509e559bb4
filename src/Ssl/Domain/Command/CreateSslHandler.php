<?php

declare(strict_types=1);

namespace Cdn77\Api\Ssl\Domain\Command;

use Cdn77\Api\Core\Domain\Authorization\CustomerContextStore;
use Cdn77\Api\Core\Domain\Messaging\CommandHandler;
use Cdn77\Api\Core\Domain\NxgApi\Exception\NxgRequestFailed;
use Cdn77\Api\Core\Domain\NxgApi\NxgApi;
use Cdn77\Api\Core\Domain\Repository\Customer\AccountFlagsRepository;
use Cdn77\Api\Core\Domain\TeamMember\TeamMemberAccessConfigurator;
use Cdn77\Api\Core\Domain\Value\AccessRestriction\ResourceRestrictionType;
use Cdn77\Api\Customer\Domain\Exception\AccountFlagsNotFound;
use Cdn77\Api\Ssl\Domain\Dto\SslDetail;
use Cdn77\Api\Ssl\Domain\Exception\InvalidCertificate;
use Cdn77\Api\Ssl\Domain\Exception\SslAlreadyExists;
use Cdn77\Api\Ssl\Domain\Factory\SslFactory;
use Cdn77\Api\Ssl\Domain\Finder\SslFinder;
use Cdn77\Api\Ssl\Domain\Parser\PublicCertificateParser;
use Cdn77\Api\Ssl\Domain\Validation\CertificateValidator;
use Psr\Clock\ClockInterface;
use Psr\Log\LoggerInterface;

final readonly class CreateSslHandler implements CommandHandler
{
    public function __construct(
        private AccountFlagsRepository $accountFlagsRepository,
        private ClockInterface $clock,
        private CustomerContextStore $customerContextStore,
        private NxgApi $nxgApi,
        private LoggerInterface $logger,
        private SslFactory $sslFactory,
        private SslFinder $sslFinder,
        private TeamMemberAccessConfigurator $teamMemberAccessConfigurator,
    ) {
    }

    /**
     * @throws InvalidCertificate
     * @throws AccountFlagsNotFound
     */
    public function handle(CreateSsl $command): SslDetail
    {
        $certificate = $command->certificate;
        $customerContext = $this->customerContextStore->get();
        $customer = $customerContext->loggedCustomer;
        $now = $this->clock->now();
        $privateKey = $command->privateKey;
        $type = $command->sslType;

        $affectedCustomer = $customerContext->affectedCustomer();
        $affectedAccountFlags = $this->accountFlagsRepository->getForCustomer($affectedCustomer->getId());

        if ($this->sslFinder->existsForCustomer($certificate, $affectedCustomer->getId())) {
            throw SslAlreadyExists::forType($type);
        }

        $publicCertificateInfo = PublicCertificateParser::fromCertificate($certificate);
        CertificateValidator::validate($now, $publicCertificateInfo, $privateKey);

        $ssl = $this->sslFactory->createWithDomains(
            $affectedCustomer,
            $privateKey,
            $publicCertificateInfo,
            $command->sslType,
            $now,
        );

        try {
            $this->nxgApi->storePrivateKey($affectedCustomer->getId(), $ssl->id(), $certificate, $privateKey);
        } catch (NxgRequestFailed $e) {
            $this->logger->error($e->getMessage(), ['exception' => $e]);
        }

        if ($customer->isTeamMember() && $affectedAccountFlags->isReseller()) {
            $this->teamMemberAccessConfigurator->configure(
                $ssl->id(),
                $customer->getNewId(),
                ResourceRestrictionType::Ssl,
            );
        }

        return SslDetail::fromSslWithCnames($ssl, $publicCertificateInfo->cnames);
    }
}
