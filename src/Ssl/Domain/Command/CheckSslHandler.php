<?php

declare(strict_types=1);

namespace Cdn77\Api\Ssl\Domain\Command;

use Cdn77\Api\Core\Domain\Messaging\CommandHandler;
use Cdn77\Api\Ssl\Domain\Dto\SslCheckResult;
use Cdn77\Api\Ssl\Domain\Exception\CertificateExpired;
use Cdn77\Api\Ssl\Domain\Exception\CertificatePairDoesNotMatch;
use Cdn77\Api\Ssl\Domain\Exception\InvalidCertificate;
use Cdn77\Api\Ssl\Domain\Exception\InvalidPrivateKey;
use Cdn77\Api\Ssl\Domain\Parser\PublicCertificateParser;
use Cdn77\Api\Ssl\Domain\Validation\CertificateValidator;
use Psr\Clock\ClockInterface;

final readonly class CheckSslHandler implements CommandHandler
{
    public function __construct(private ClockInterface $clock)
    {
    }

    /** @throws InvalidCertificate */
    public function handle(CheckSsl $command): SslCheckResult
    {
        $certificate = $command->certificate;
        $now = $this->clock->now();
        $privateKey = $command->privateKey;

        $publicCertificateInfo = PublicCertificateParser::fromCertificate($certificate);

        try {
            CertificateValidator::validate($now, $publicCertificateInfo, $privateKey);
        } catch (InvalidPrivateKey) {
            return SslCheckResult::forInvalidPrivateKeyFromCertificateInfo($publicCertificateInfo);
        } catch (CertificatePairDoesNotMatch) {
            return SslCheckResult::forNotMatchingPairFromCertificateInfo($publicCertificateInfo);
        } catch (CertificateExpired) {
            // Ignore exception - Return expired date in JSON response as OK state
        }

        return SslCheckResult::validFromCertificateInfo($publicCertificateInfo);
    }
}
