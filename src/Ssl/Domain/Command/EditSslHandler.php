<?php

declare(strict_types=1);

namespace Cdn77\Api\Ssl\Domain\Command;

use Cdn77\Api\Core\Domain\Entity\Cdn\Cdn;
use Cdn77\Api\Core\Domain\Entity\Cdn\CdnId;
use Cdn77\Api\Core\Domain\Exception\Customer\CustomerNotFound;
use Cdn77\Api\Core\Domain\Messaging\CommandHandler;
use Cdn77\Api\Core\Domain\NxgApi\Exception\NxgRequestFailed;
use Cdn77\Api\Core\Domain\NxgApi\NxgApi;
use Cdn77\Api\Core\Domain\Repository\CustomerRepository;
use Cdn77\Api\Core\Domain\Repository\SslRepository;
use Cdn77\Api\Core\Domain\Resolver\AffectedCustomerResolver;
use Cdn77\Api\Core\Domain\Value\AccessRestriction\AccessType;
use Cdn77\Api\Ssl\Domain\Dto\SslDetail;
use Cdn77\Api\Ssl\Domain\Exception\FailedToUpdateSsl;
use Cdn77\Api\Ssl\Domain\Exception\InvalidCertificate;
use Cdn77\Api\Ssl\Domain\Exception\SslAlreadyExists;
use Cdn77\Api\Ssl\Domain\Exception\SslNotFound;
use Cdn77\Api\Ssl\Domain\Finder\SslFinder;
use Cdn77\Api\Ssl\Domain\Parser\PublicCertificateParser;
use Cdn77\Api\Ssl\Domain\Repository\CdnRepository;
use Cdn77\Api\Ssl\Domain\SetupSslDomains;
use Cdn77\Api\Ssl\Domain\Validation\CertificateValidator;
use Cdn77\Api\Ssl\Domain\Validation\SslAccessValidator;
use Psr\Clock\ClockInterface;
use Psr\Log\LoggerInterface;

use function array_map;
use function BenTools\IterableFunctions\iterable_to_array;

final readonly class EditSslHandler implements CommandHandler
{
    public function __construct(
        private AffectedCustomerResolver $affectedCustomerResolver,
        private CdnRepository $cdnRepository,
        private CustomerRepository $customerRepository,
        private ClockInterface $clock,
        private LoggerInterface $logger,
        private NxgApi $nxgApi,
        private SetupSslDomains $setupCnames,
        private SslAccessValidator $sslAccessValidator,
        private SslFinder $sslFinder,
        private SslRepository $sslRepository,
    ) {
    }

    /**
     * @throws CustomerNotFound
     * @throws FailedToUpdateSsl
     * @throws InvalidCertificate
     * @throws SslAlreadyExists
     * @throws SslNotFound
     */
    public function handle(EditSsl $command): SslDetail
    {
        $certificate = $command->certificate;
        $customerId = $command->customerId;
        $id = $command->sslId;
        $customer = $this->customerRepository->get($customerId);
        $now = $this->clock->now();
        $privateKey = $command->privateKey;
        $type = $command->sslType;
        $ssl = $this->sslRepository->getForType($id, $type);
        $oldPrivateKey = $ssl->privateKey();
        $newPrivateKey = $privateKey ?? $oldPrivateKey;

        if (! $this->sslAccessValidator->isAllowed($ssl, $customerId, AccessType::SslEdit)) {
            throw SslNotFound::forIdAndType($id, $type);
        }

        $affectedCustomer = $this->affectedCustomerResolver->resolveActive($customer);

        if (
            $ssl->certificate()->get() !== $certificate->get()
            && $this->sslFinder->existsForCustomer($certificate, $affectedCustomer->getId())
        ) {
            throw SslAlreadyExists::forType($type);
        }

        $publicCertificateInfo = PublicCertificateParser::fromCertificate($certificate);
        CertificateValidator::validate($now, $publicCertificateInfo, $newPrivateKey);

        $ssl->updatePublicCertificateInfo($publicCertificateInfo);

        if ($privateKey !== null) {
            $ssl->updatePrivateKey($privateKey);
        }

        $cnames = $publicCertificateInfo->cnames;
        $this->setupCnames->executeForSsl($cnames, $ssl);

        /** @var list<Cdn> $assignedCdns */
        $assignedCdns = iterable_to_array($this->cdnRepository->findForSsl($ssl->legacyId()));

        try {
            $this->nxgApi->changeCertificateForMultipleCdns(
                array_map(
                    static fn (Cdn $cdn): CdnId => $cdn->getId(),
                    $assignedCdns,
                ),
                $certificate,
                $newPrivateKey,
            );
        } catch (NxgRequestFailed $e) {
            throw FailedToUpdateSsl::nxgChangeFailed($e);
        }

        try {
            $this->nxgApi->storePrivateKey($affectedCustomer->getId(), $ssl->id(), $certificate, $newPrivateKey);
        } catch (NxgRequestFailed $e) {
            $this->logger->error($e->getMessage(), ['exception' => $e]);
        }

        return SslDetail::fromSslWithCnamesAndCdns($ssl, $assignedCdns, $cnames);
    }
}
