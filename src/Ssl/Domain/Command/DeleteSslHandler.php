<?php

declare(strict_types=1);

namespace Cdn77\Api\Ssl\Domain\Command;

use Cdn77\Api\Core\Domain\Exception\Customer\CustomerNotFound;
use Cdn77\Api\Core\Domain\Messaging\CommandHandler;
use Cdn77\Api\Core\Domain\NxgApi\Exception\NxgRequestFailed;
use Cdn77\Api\Core\Domain\NxgApi\NxgApi;
use Cdn77\Api\Core\Domain\Repository\CustomerRepository;
use Cdn77\Api\Core\Domain\Repository\SslRepository;
use Cdn77\Api\Core\Domain\Resolver\AffectedCustomerResolver;
use Cdn77\Api\Core\Domain\Value\AccessRestriction\AccessType;
use Cdn77\Api\Ssl\Domain\Exception\SslNotFound;
use Cdn77\Api\Ssl\Domain\Exception\SslRemovalFailed;
use Cdn77\Api\Ssl\Domain\Finder\SslAssignedCdnIdsFinder;
use Cdn77\Api\Ssl\Domain\Validation\SslAccessValidator;
use Psr\Clock\ClockInterface;
use Psr\Log\LoggerInterface;

final readonly class DeleteSslHandler implements CommandHandler
{
    public function __construct(
        private AffectedCustomerResolver $affectedCustomerResolver,
        private ClockInterface $clock,
        private CustomerRepository $customerRepository,
        private LoggerInterface $logger,
        private NxgApi $nxgApi,
        private SslAccessValidator $sslAccessValidator,
        private SslAssignedCdnIdsFinder $sslAssignedCdnIdsFinder,
        private SslRepository $sslRepository,
    ) {
    }

    /**
     * @throws CustomerNotFound
     * @throws SslNotFound
     * @throws SslRemovalFailed
     */
    public function handle(DeleteSsl $command): void
    {
        $sslId = $command->sslId;
        $ssl = $this->sslRepository->getForType($sslId, $command->sslType);

        if (! $this->sslAccessValidator->isAllowed($ssl, $command->customerId, AccessType::SslDelete)) {
            throw SslNotFound::forIdAndType($sslId, $command->sslType);
        }

        $assignedCdnIds = $this->sslAssignedCdnIdsFinder->findForSslId($sslId);

        if ($assignedCdnIds->count() > 0) {
            throw SslRemovalFailed::isInUseByCdnIds($sslId, $assignedCdnIds);
        }

        $customer = $this->customerRepository->get($command->customerId);
        try {
            $this->nxgApi->deletePrivateKey($this->affectedCustomerResolver->resolve($customer)->getId(), $sslId);
        } catch (NxgRequestFailed $e) {
            $this->logger->error($e->getMessage(), ['exception' => $e]);
        }

        $ssl->markAsDeleted($this->clock->now());
    }
}
