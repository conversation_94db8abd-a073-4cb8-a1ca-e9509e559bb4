<?php

declare(strict_types=1);

namespace Cdn77\Api\Ssl\Domain\Dto;

use Cdn77\Api\Core\Domain\Entity\Cdn\Cdn;
use Cdn77\Api\Core\Domain\Entity\Ssl\Ssl;
use Cdn77\Api\Ssl\Domain\Contract\SslId;
use Cdn77\Api\Ssl\Domain\Value\Certificate;
use Cdn77\Api\Ssl\Domain\Value\SslCname;
use DateTimeImmutable;
use Ds\Set;

final readonly class SslDetail
{
    /**
     * @param array<int, Cdn> $assignedCdns
     * @param Set<SslCname> $cnames
     */
    private function __construct(
        public SslId $id,
        public Certificate $certificate,
        public Set $cnames,
        public DateTimeImmutable $expiresAt,
        public array $assignedCdns,
    ) {
    }

    /**
     * @param array<int, Cdn> $assignedCdns
     * @param Set<SslCname> $cnames
     */
    public static function fromSslWithCnamesAndCdns(Ssl $ssl, array $assignedCdns, Set $cnames): self
    {
        return new self(
            $ssl->id(),
            $ssl->certificate(),
            $cnames,
            $ssl->expiresAt(),
            $assignedCdns,
        );
    }

    /** @param Set<SslCname> $cnames */
    public static function fromSslWithCnames(Ssl $ssl, Set $cnames): self
    {
        return self::fromSslWithCnamesAndCdns($ssl, [], $cnames);
    }
}
