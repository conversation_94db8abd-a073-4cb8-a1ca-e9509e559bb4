<?php

declare(strict_types=1);

namespace Cdn77\Api\Ssl\Domain;

use Cdn77\Api\Core\Domain\Entity\Ssl\Ssl;
use Cdn77\Api\Core\Domain\Entity\Ssl\SslDomain;
use Cdn77\Api\Ssl\Domain\Repository\SslDomainRepository;
use Cdn77\Api\Ssl\Domain\Value\SslCname;
use Ds\Pair;
use Ds\Set;

use function Cdn77\Functions\mapFromIterable;

final readonly class SetupSslDomains
{
    public function __construct(private SslDomainRepository $sslDomainRepository)
    {
    }

    /** @param Set<SslCname> $newCnames */
    public function executeForSsl(Set $newCnames, Ssl $ssl): void
    {
        $newDomains = mapFromIterable(
            $newCnames,
            static fn (mixed $_, SslCname $newCname): Pair => new Pair($newCname, new SslDomain($newCname, $ssl)),
        );

        $currentDomains = mapFromIterable(
            $this->sslDomainRepository->findForSsl($ssl->legacyId()),
            static fn (mixed $_, SslDomain $sslDomain): Pair => new Pair($sslDomain->domain(), $sslDomain),
        );

        foreach ($currentDomains->diff($newDomains) as $domain) {
            $this->sslDomainRepository->remove($domain);
        }

        foreach ($newDomains->diff($currentDomains) as $domain) {
            $this->sslDomainRepository->add($domain);
        }
    }
}
