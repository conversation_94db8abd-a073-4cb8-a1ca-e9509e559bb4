<?php

declare(strict_types=1);

namespace Cdn77\Api\Ssl\Domain\Exception;

use Cdn77\Api\Core\Domain\Exception\ClapDomainException;
use Cdn77\Api\Ssl\Domain\Contract\SslId;
use Cdn77\Api\Ssl\Domain\Enum\SslType;
use DomainException;

use function sprintf;

final class SslNotFound extends DomainException implements ClapDomainException
{
    public static function forIdAndType(SslId $id, SslType $sslType): self
    {
        return new self(sprintf('%s certificate with id "%s" was not found.', $sslType->value, $id->toString()));
    }
}
