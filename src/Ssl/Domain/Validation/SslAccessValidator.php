<?php

declare(strict_types=1);

namespace Cdn77\Api\Ssl\Domain\Validation;

use Cdn77\Api\Core\Domain\Authorization\AccessValidator;
use Cdn77\Api\Core\Domain\Authorization\Context\SslAccessContext;
use Cdn77\Api\Core\Domain\Entity\Customer\CustomerId;
use Cdn77\Api\Core\Domain\Entity\Ssl\Ssl;
use Cdn77\Api\Core\Domain\Value\AccessRestriction\AccessType;

interface SslAccessValidator
{
    public function isAllowed(Ssl $ssl, CustomerId $customerId, AccessType $accessType): bool;

    /** @return AccessValidator<SslAccessContext> */
    public function get(AccessType $accessType): AccessValidator;
}
