<?php

declare(strict_types=1);

namespace Cdn77\Api\RawLog\Infrastructure\FileSystem;

use Cdn77\Api\Core\Domain\Entity\Cdn\CdnId;
use Cdn77\Api\RawLog\Domain\Dto\RequestedLogFile;
use Cdn77\Api\RawLog\Domain\RawLogFileSystem;

use function Safe\filesize;

final class LocalRawLogFileSystem extends BaseLogFileSystem implements RawLogFileSystem
{
    public function getFileSize(CdnId $cdnId, RequestedLogFile $file): int
    {
        return filesize($this->getFullPath($cdnId, $file));
    }
}
