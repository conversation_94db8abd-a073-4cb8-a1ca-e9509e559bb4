<?php

declare(strict_types=1);

namespace Cdn77\Api\RawLog\Domain\Dto;

use Cdn77\Api\Core\Domain\Entity\Cdn\Cdn;
use Cdn77\Api\Core\Domain\Entity\Origin\OriginId;
use Cdn77\Api\Core\Domain\Entity\RawLogs\RawLog;
use Webmozart\Assert\Assert;

final readonly class CdnRawLog
{
    private function __construct(
        public Cdn $cdn,
        public OriginId|null $originId,
        public string $originUrl,
        public RawLogFiles $files,
        public RawLog|null $rawLog,
    ) {
    }

    public static function fromCdnWithFiles(
        Cdn $cdn,
        OriginId|null $originId,
        string $originUrl,
        RawLog|null $rawLog,
        RawLogFiles $files,
    ): self {
        return new self(
            $cdn,
            $originId,
            $originUrl,
            $files,
            $rawLog,
        );
    }

    public function isActive(): bool
    {
        return $this->rawLog !== null && ! $this->rawLog->getAddOn()->isCanceled();
    }

    public function hasNoRawLog(): bool
    {
        return $this->rawLog === null;
    }

    public function isCustomPeriod(): bool
    {
        return $this->rawLog !== null
            && $this->rawLog->getActiveTo() > $this->rawLog->getActiveFrom()->modify('+1 month');
    }

    public function getRawLog(): RawLog
    {
        Assert::notNull($this->rawLog, 'Missing raw log add on.');

        return $this->rawLog;
    }
}
