<?php

declare(strict_types=1);

namespace Cdn77\Api\RawLog\Domain\Query;

use Cdn77\Api\Cdn\Domain\Exception\CdnNotFound;
use Cdn77\Api\Core\Domain\Messaging\QueryHandler;
use Cdn77\Api\Core\Domain\Repository\Cdn\CdnRepository;
use Cdn77\Api\Core\Domain\Validation\CdnAccessValidator;
use Cdn77\Api\Core\Domain\Value\AccessRestriction\AccessType;
use Cdn77\Api\RawLog\Domain\Dto\RawLogFile;
use Cdn77\Api\RawLog\Domain\Exception\RawLogFileNotFound;
use Cdn77\Api\RawLog\Domain\RawLogFileSystem;

use function sprintf;
use function str_replace;

final readonly class GetRawLogForDownloadHandler implements QueryHandler
{
    public function __construct(
        private CdnAccessValidator $cdnAccessValidator,
        private CdnRepository $cdnRepository,
        private RawLogFileSystem $rawLogFileSystem,
    ) {
    }

    /**
     * @throws CdnNotFound
     * @throws RawLogFileNotFound
     */
    public function handle(GetRawLogForDownload $query): RawLogFile
    {
        $cdnId = $query->cdnId;
        $customerId = $query->customerId;
        $cdn = $this->cdnRepository->get($cdnId);
        $requestedFile = $query->requestedFile;

        if (! $this->cdnAccessValidator->isOwnerOrAdmin($cdn, $customerId, AccessType::LogsRead)) {
            throw CdnNotFound::forId($cdnId);
        }

        if ($this->rawLogFileSystem->exists($cdnId, $requestedFile)) {
            return new RawLogFile(
                sprintf('%s-%s', str_replace('/', '', $cdn->label()->value), $requestedFile->fileName),
                $this->rawLogFileSystem->getFullPath($cdnId, $requestedFile),
                $requestedFile->extension,
            );
        }

        throw new RawLogFileNotFound('Raw log file could not be found.', 0);
    }
}
