<?php

declare(strict_types=1);

namespace Cdn77\Api\RawLog\Domain\Command;

use Cdn77\Api\Core\Domain\Entity\Cdn\CdnId;
use Cdn77\Api\Core\Domain\Entity\Customer\CustomerId;
use Cdn77\Api\Core\Domain\Entity\RawLogs\RawLog;
use Cdn77\Api\Core\Domain\Messaging\Command;

/** @implements Command<RawLog, ActivateRawLogHandler> */
final class ActivateRawLog implements Command
{
    public function __construct(public CdnId $cdnId, public CustomerId $customer)
    {
    }
}
