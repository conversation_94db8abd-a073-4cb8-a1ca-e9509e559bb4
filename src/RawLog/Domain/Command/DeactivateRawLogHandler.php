<?php

declare(strict_types=1);

namespace Cdn77\Api\RawLog\Domain\Command;

use Cdn77\Api\Cdn\Domain\Exception\CdnNotFound;
use Cdn77\Api\Core\Domain\Entity\RawLogs\RawLog;
use Cdn77\Api\Core\Domain\Messaging\CommandHandler;
use Cdn77\Api\Core\Domain\NxgApi\NxgApi;
use Cdn77\Api\Core\Domain\Repository\Cdn\CdnRepository;
use Cdn77\Api\Core\Domain\Validation\CdnAccessValidator;
use Cdn77\Api\Core\Domain\Value\AccessRestriction\AccessType;
use Cdn77\Api\RawLog\Domain\Repository\RawLogRepository;
use Psr\Clock\ClockInterface;

final readonly class DeactivateRawLogHandler implements CommandHandler
{
    public function __construct(
        private CdnAccessValidator $cdnAccessValidator,
        private CdnRepository $cdnRepository,
        private ClockInterface $clock,
        private NxgApi $nxgApi,
        private RawLogRepository $rawLogRepository,
    ) {
    }

    /** @throws CdnNotFound */
    public function handle(DeactivateRawLog $command): RawLog
    {
        $cdnId = $command->cdnId;
        $cdn = $this->cdnRepository->get($cdnId);

        if (! $this->cdnAccessValidator->isAllowed($cdn, $command->customer, AccessType::LogsToggle)) {
            throw CdnNotFound::forId($cdnId);
        }

        $activeRawLog = $this->rawLogRepository->getActiveForCdn($cdn->getLegacyId());

        $activeRawLog->deactivate($this->clock->now(), $cdn->getOwner()->getId());
        $cdn->deactivateRawLogs($this->nxgApi);

        return $activeRawLog;
    }
}
