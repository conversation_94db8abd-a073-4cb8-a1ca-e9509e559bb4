<?php

declare(strict_types=1);

namespace Cdn77\Api\RawLog\Domain\Value;

enum ContentType: string
{
    case Gzip = 'application/x-gzip';
    case PlainText = 'text/plain';

    public static function fromExtension(LogFileExtension $extension): self
    {
        return match ($extension) {
            LogFileExtension::Compressed => self::Gzip,
            LogFileExtension::Uncompressed => self::PlainText,
        };
    }
}
