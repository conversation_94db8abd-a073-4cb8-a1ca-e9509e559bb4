<?php

declare(strict_types=1);

namespace Cdn77\Api\RawLog\Domain\Exception;

use Cdn77\Api\Core\Domain\Exception\ClapDomainException;
use DomainException;

use function sprintf;

final class InvalidLogFile extends DomainException implements ClapDomainException
{
    public static function fromFileName(string $fileName): self
    {
        return new self(sprintf('The log file "%s" is invalid.', $fileName));
    }
}
