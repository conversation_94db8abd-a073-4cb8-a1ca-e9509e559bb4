<?xml version="1.0" encoding="UTF-8" ?>
<container xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
           xmlns="http://symfony.com/schema/dic/services"
           xsi:schemaLocation="http://symfony.com/schema/dic/services
        https://symfony.com/schema/dic/services/services-1.0.xsd">

    <services>
        <defaults autowire="true" autoconfigure="true" public="false" />

        <service id="Cdn77\Api\RawLog\Infrastructure\FileSystem\Resolver\RawLogPathResolver">
            <argument key="$compressedPath">%env(RAW_LOG_COMPRESSED_FULL_PATH)%</argument>
            <argument key="$uncompressedPath">%env(RAW_LOG_UNCOMPRESSED_FULL_PATH)%</argument>
        </service>
        <service id="Cdn77\Api\RawLog\Infrastructure\FileSystem\Resolver\SampleLogPathResolver">
            <argument key="$path">%env(SAMPLE_LOG_FULL_PATH)%</argument>
        </service>

        <service id="Cdn77\Api\RawLog\Infrastructure\FileSystem\LocalRawLogFileSystem">
            <argument key="$pathResolver" type="service" id="Cdn77\Api\RawLog\Infrastructure\FileSystem\Resolver\RawLogPathResolver" />
        </service>
        <service id="Cdn77\Api\RawLog\Infrastructure\FileSystem\LocalSampleLogFileSystem">
            <argument key="$pathResolver" type="service" id="Cdn77\Api\RawLog\Infrastructure\FileSystem\Resolver\SampleLogPathResolver" />
        </service>

        <service id="Cdn77\Api\RawLog\Domain\RawLogFileSystem" alias="Cdn77\Api\RawLog\Infrastructure\FileSystem\LocalRawLogFileSystem" />
    </services>
</container>
