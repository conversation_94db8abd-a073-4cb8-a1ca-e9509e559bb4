<?php

declare(strict_types=1);

namespace Cdn77\Api\Payment\Domain\Paygate;

use Cdn77\Api\Payment\Domain\Dto\PaymentRequestData;
use Paygate\Types\Payment\Payment;
use Paygate\Types\Payment\Recipe;
use Paygate\Types\PaymentMethod\PaymentMethodInfo;
use Symfony\Component\Uid\Uuid;

interface Paygate
{
    public function executePayment(PaymentRequestData $paymentData): Payment;

    public function getPaymentMethodInfo(Payment $payment): PaymentMethodInfo;

    /** @param array<string, mixed> $cardData */
    public function createRechargeableRecipe(array $cardData): Recipe;

    public function cancelRechargeableRecipe(Uuid $paymentRecipeId): void;
}
