<?php

declare(strict_types=1);

namespace Cdn77\Api\Payment\Domain\Dto;

use Cdn77\Api\Core\Domain\Entity\Customer\CustomerId;
use Cdn77\Api\Core\Domain\Entity\CustomPlan\ContractId;
use Cdn77\Api\Core\Domain\Value\RequestSource;
use Cdn77\Api\Payment\Application\Payload\Dto\PaymentData as PaymentRequestData;
use Cdn77\Api\Payment\Domain\Value\PaymentData;
use Cdn77\Api\Payment\Domain\Value\PaymentMethod;
use Webmozart\Assert\Assert;

final class CustomPlanPaymentData implements PaymentData
{
    public function __construct(
        public CustomerId $depositorId,
        public ContractId $contractId,
        public PaymentMethod $paymentMethod,
        public RequestSource $source,
        public PaymentRecipeConfiguration|null $paymentRecipeConfiguration = null,
    ) {
    }

    public static function stripe(
        PaymentClientData $clientData,
        RequestSource $source,
        PaymentRecipeConfiguration|null $paymentRecipeConfiguration,
    ): CustomPlanPaymentData {
        $contractId = $clientData->contractId();
        Assert::notNull($contractId);

        return new self(
            $clientData->depositorId(),
            $contractId,
            PaymentMethod::Stripe,
            $source,
            $paymentRecipeConfiguration,
        );
    }

    public static function paypal(PaymentClientData $clientData): CustomPlanPaymentData
    {
        $contractId = $clientData->contractId();
        Assert::notNull($contractId);

        return new self(
            $clientData->depositorId(),
            $contractId,
            PaymentMethod::Paypal,
            RequestSource::Crop,
        );
    }

    public static function fromPaymentDto(
        PaymentRequestData $dto,
    ): CustomPlanPaymentData {
        return $dto->isCardPayment()
            ? self::stripe($dto->paymentData->clientData(), $dto->source, $dto->paymentRecipeConfiguration)
            : self::paypal($dto->paymentData->clientData());
    }

    public function getPaymentMethod(): PaymentMethod
    {
        return $this->paymentMethod;
    }

    public function getSource(): RequestSource
    {
        return $this->source;
    }

    public function recipeConfiguration(): PaymentRecipeConfiguration|null
    {
        return $this->paymentRecipeConfiguration;
    }
}
