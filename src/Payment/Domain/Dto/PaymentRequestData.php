<?php

declare(strict_types=1);

namespace Cdn77\Api\Payment\Domain\Dto;

use Brick\Math\BigDecimal;
use Brick\Math\Exception\MathException;
use Brick\Money\Money;
use Cdn77\Api\Core\Domain\Billing\Value\BillingDescription;
use Cdn77\Api\Core\Domain\Entity\Customer\CustomerId;
use Cdn77\Api\Core\Domain\Entity\CustomPlan\ContractId;
use Cdn77\Api\CoreLibrary\Money\CurrencyCode;
use Cdn77\Api\Payment\Domain\Value\PaymentType;
use Symfony\Component\Uid\Uuid;

use function Cdn77\Functions\absurd;

/**
 * @phpstan-import-type ClientDataShape from PaymentClientData
 * @phpstan-type RequestDataShape array{
 *    amount: float,
 *    currency: string,
 *    description?: string,
 *    processorData?: array<mixed>,
 *    recipe?: array<mixed>,
 *    recipeId?: string,
 *    clientData: ClientDataShape
 *  }
 */
final readonly class PaymentRequestData
{
    /** @param RequestDataShape $data */
    public function __construct(
        public array $data,
    ) {
    }

    public static function rechargeCustomPlan(
        Money $totalPrice,
        Uuid $recipeId,
        CustomerId $customerId,
        CustomerId $depositorId,
        ContractId $contractId,
    ): self {
        return new self([
            'amount' => $totalPrice->getAmount()->toFloat(),
            'currency' => $totalPrice->getCurrency()->getCurrencyCode(),
            'recipeId' => $recipeId->toString(),
            'processorData' => [],
            'clientData' => [
                'paymentMethod' => PaymentType::Card->value,
                'customerId' => $customerId->toInt(),
                'depositorId' => $depositorId->toInt(),
                'promoCode' => null,
                'customPlanId' => $contractId->toString(),
            ],
        ]);
    }

    public static function createMonthlyPlanRenewal(
        Money $totalPrice,
        CustomerId $customerId,
        Uuid $recipeId,
    ): self {
        return new self([
            'amount' => $totalPrice->getAmount()->toFloat(),
            'currency' => $totalPrice->getCurrency()->getCurrencyCode(),
            'clientData' => [
                'paymentMethod' => PaymentType::Card->value,
                'customerId' => $customerId->toInt(),
            ],
            'processorData' => [],
            'recipeId' => $recipeId->toString(),
        ]);
    }

    public function withDescriptionAndMoney(
        BillingDescription $description,
        Money $totalMoney,
    ): self {
        $currentData = $this->data;
        $currentData['amount'] = $totalMoney->getAmount()->toFloat();
        $currentData['currency'] = $totalMoney->getCurrency()->getCurrencyCode();
        $currentData['description'] = $description->value;

        return new self($currentData);
    }

    public function amount(): BigDecimal
    {
        try {
            return BigDecimal::of($this->data['amount']);
        } catch (MathException) {
            absurd();
        }
    }

    public function currency(): CurrencyCode
    {
        return CurrencyCode::from($this->data['currency']);
    }

    public function clientData(): PaymentClientData
    {
        return new PaymentClientData($this->data['clientData']);
    }

    public function isCardPayment(): bool
    {
        return $this->clientData()->paymentMethod() === PaymentType::Card;
    }
}
