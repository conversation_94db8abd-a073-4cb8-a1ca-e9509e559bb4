<?php

declare(strict_types=1);

namespace Cdn77\Api\Payment\Domain;

use Cdn77\Api\Core\Domain\Entity\Customer\CustomerId;
use Cdn77\Api\Core\Domain\Entity\Invoice\Invoice;
use Cdn77\Api\Core\Domain\Repository\Customer\CustomerPaymentSettingsRepository;
use Cdn77\Api\CoreLibrary\Money\CurrencyCode;
use Cdn77\Api\Payment\Domain\Value\PaymentMethod;

final readonly class PaymentCurrencyResolver
{
    public function __construct(
        private CustomerPaymentSettingsRepository $customerPaymentSettingsRepository,
    ) {
    }

    public function resolve(
        CustomerId $customerId,
        CurrencyCode $currency,
        PaymentMethod $paymentMethod,
        Invoice|null $invoice = null,
    ): CurrencyCode {
        if ($paymentMethod->isPaypal()) {
            return $currency;
        }

        $customerPaymentSettings = $this->customerPaymentSettingsRepository->getForCustomer($customerId);

        return $invoice?->currency() ?? $customerPaymentSettings->creditCardCurrency();
    }
}
