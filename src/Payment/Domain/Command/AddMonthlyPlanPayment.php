<?php

declare(strict_types=1);

namespace Cdn77\Api\Payment\Domain\Command;

use Brick\Math\RoundingMode;
use Brick\Money\Money;
use Cdn77\Api\Core\Domain\Entity\Customer\CustomerId;
use Cdn77\Api\Core\Domain\Messaging\Command;
use Cdn77\Api\CoreLibrary\Money\CurrencyCode;
use Cdn77\Api\Payment\Domain\Dto\PaymentRequestData;
use Cdn77\Api\Payment\Domain\Value\MonthlyPlanPaymentData;
use Paygate\Types\Payment\Payment;

/** @implements Command<Payment, AddMonthlyPlanPaymentHandler> */
final readonly class AddMonthlyPlanPayment implements Command
{
    public function __construct(
        public MonthlyPlanPaymentData $paymentData,
        public CustomerId $customerId,
        public PaymentRequestData $paymentRequestData,
        public Money $usdMoney,
        public CurrencyCode $currency,
    ) {
    }

    public static function fromPaymentData(
        MonthlyPlanPaymentData $paymentData,
        CustomerId $customerId,
        PaymentRequestData $paymentRequestData,
    ): self {
        $usdMoney = Money::of(
            $paymentRequestData->amount(),
            CurrencyCode::USD->toCurrency(),
            roundingMode: RoundingMode::HALF_UP,
        );

        return new self(
            $paymentData,
            $customerId,
            $paymentRequestData,
            $usdMoney,
            $paymentRequestData->currency(),
        );
    }
}
