<?php

declare(strict_types=1);

namespace Cdn77\Api\Payment\Domain\Command;

use Cdn77\Api\Core\Domain\Entity\Customer\CustomerUuid;
use Cdn77\Api\Core\Domain\Entity\Payment\PaymentRecipe;
use Cdn77\Api\Core\Domain\Messaging\Command;
use Cdn77\Api\Core\Domain\Value\Label;
use Cdn77\Api\Payment\Domain\Dto\NewCreditCard;

/** @implements Command<PaymentRecipe, AddCreditCardHandler> */
final readonly class AddCreditCard implements Command
{
    public function __construct(public NewCreditCard $newCreditCard, public CustomerUuid $customerId)
    {
    }

    /** @param array<string, mixed> $cardData */
    public static function create(
        CustomerUuid $customerId,
        array $cardData,
        bool $isDefault,
        Label|null $label,
    ): self {
        return new self(
            new NewCreditCard($cardData, $isDefault, $label),
            $customerId,
        );
    }
}
