<?php

declare(strict_types=1);

namespace Cdn77\Api\Payment\Domain;

use Brick\Money\Money;
use Cdn77\Api\Core\Domain\Billing\Value\BillingDescription;
use Cdn77\Api\Core\Domain\Billing\Value\BillingPeriod;
use Cdn77\Api\Core\Domain\Billing\Value\VatRate;
use Cdn77\Api\Core\Domain\Currency\CurrencyConverter;
use Cdn77\Api\Core\Domain\Entity\Customer\Customer;
use Cdn77\Api\Core\Domain\Entity\CustomPlan\Contract;
use Cdn77\Api\Core\Domain\Exception\Currency\CurrencyConversionFailed;
use Cdn77\Api\CoreLibrary\Money\CurrencyCode;
use Cdn77\Api\CoreLibrary\Money\MoneyHelper;
use Cdn77\Api\Payment\Domain\Dto\CustomPlanPaymentData;
use Cdn77\Api\Payment\Domain\Dto\PaymentRequestData;
use Cdn77\Api\Payment\Domain\Paygate\PaymentProcessor;
use Cdn77\Api\Tariff\Domain\Factory\TariffFactory;
use Cdn77\Api\Tariff\Domain\Repository\TariffRepository;
use DateTimeImmutable;
use Paygate\Types\Payment\Payment as PaygatePayment;

final readonly class CustomPlanPaymentProcessor
{
    public function __construct(
        private CurrencyConverter $currencyConverter,
        private CustomPlanCharger $customPlanCharger,
        private PaymentCurrencyResolver $paymentCurrencyResolver,
        private PaymentProcessor $paymentProcessor,
        private TariffFactory $tariffFactory,
        private TariffRepository $tariffRepository,
    ) {
    }

    /** @throws CurrencyConversionFailed */
    public function process(
        Customer $customer,
        Contract $contract,
        Money $totalMoney,
        CurrencyCode $currency,
        VatRate $vatRate,
        Customer $depositor,
        CustomPlanPaymentData $paymentData,
        PaymentRequestData $paymentRequestData,
        DateTimeImmutable $now,
    ): PaygatePayment {
        $customerId = $customer->getId();
        $depositorId = $depositor->getId();
        $contractInvoice = $contract->invoice();
        $paymentCurrency = $this->paymentCurrencyResolver->resolve(
            $customerId,
            $currency,
            $paymentData->paymentMethod,
            $contractInvoice,
        );

        if ($contractInvoice !== null) {
            $invoiceDate = $contractInvoice->createdAt();
            $paymentMoney = $this->currencyConverter->convertAtDate($totalMoney, $paymentCurrency, $invoiceDate);
            $usdMoney = $this->currencyConverter->convertAtDate($totalMoney, CurrencyCode::USD, $invoiceDate);
        } else {
            $paymentMoney = $this->currencyConverter->convert($totalMoney, $paymentCurrency);
            $usdMoney = $this->currencyConverter->convert($totalMoney, CurrencyCode::USD);
        }

        $creditMoney = MoneyHelper::excludeVatFromPrice($usdMoney, $vatRate);
        $servicePeriod = $contract->getServicePeriod();

        $description = $contractInvoice?->description()
            ?? BillingDescription::createForCustomPlan(
                $contract->getPlanVolume(),
                $servicePeriod,
                $vatRate,
            );

        $usdCurrency = CurrencyCode::USD->toCurrency();
        $newTariff = $this->tariffFactory->new(
            $customer,
            $creditMoney,
            Money::zero($usdCurrency),
            $now,
            $depositorId,
        );

        $this->tariffRepository->add($newTariff);
        $this->customPlanCharger->charge($contract, $newTariff, $creditMoney, $now, $depositorId, $description->value);

        return $this->paymentProcessor->process(
            $customer,
            $vatRate,
            $usdMoney,
            $paymentMoney,
            Money::zero($usdCurrency),
            $paymentData,
            $paymentRequestData,
            $description,
            $depositor,
            new BillingPeriod($servicePeriod->activeFrom, $servicePeriod->activeTo),
        );
    }
}
