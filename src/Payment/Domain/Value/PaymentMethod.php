<?php

declare(strict_types=1);

namespace Cdn77\Api\Payment\Domain\Value;

use Cdn77\Api\Core\Application\Payload\HasReference;
use Cdn77\Api\Core\Domain\Value\Enums\EnumSchema;
use cebe\openapi\spec\Reference;

enum PaymentMethod: string implements HasReference
{
    public const string Name = 'paymentMethod';

    case Invoice = 'invoice';
    case Netbanx = 'netbanx';
    case Paypal = 'paypal';
    case Stripe = 'stripe';

    public function isPaypal(): bool
    {
        return $this === self::Paypal;
    }

    public function isStripe(): bool
    {
        return $this === self::Stripe;
    }

    public static function reference(): Reference
    {
        return EnumSchema::getReference(self::Name);
    }
}
