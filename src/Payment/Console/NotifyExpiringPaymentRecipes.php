<?php

declare(strict_types=1);

namespace Cdn77\Api\Payment\Console;

use Cdn77\Api\Core\Application\Console\LockAcquirer;
use Cdn77\Api\Core\Application\Console\Value\LockName;
use Cdn77\Api\Core\Console\CronCommand;
use Cdn77\Api\Core\Domain\Messaging\CommandBus;
use Cdn77\Api\Payment\Domain\Command\NotifyExpiringPaymentRecipes as NotifyExpiringPaymentRecipesCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

final class NotifyExpiringPaymentRecipes extends Command implements CronCommand
{
    public function __construct(private readonly CommandBus $commandBus, private readonly LockAcquirer $lockAcquirer)
    {
        parent::__construct();
    }

    public function configure(): void
    {
        $this->setName('clap:notify-expiring-payment-methods')
            ->setDescription('Check expiring payment methods and send email notification');

        parent::configure();
    }

    public function execute(InputInterface $input, OutputInterface $output): int
    {
        $this->lockAcquirer->execute(LockName::fromCommand($this));
        $this->commandBus->handle(new NotifyExpiringPaymentRecipesCommand());

        return self::SUCCESS;
    }
}
