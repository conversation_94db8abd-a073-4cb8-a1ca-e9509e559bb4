<?php

declare(strict_types=1);

namespace Cdn77\Api\Payment\Application\Payload;

use Cdn77\Api\Core\Application\Payload\OASchema;
use Cdn77\Api\Core\Application\Payload\SchemaPropertyResolver;
use Cdn77\Api\Core\Application\Payload\SchemaPropertyValidator;
use Cdn77\Api\Core\Application\Payload\SupportsDtoDeserialization;
use Cdn77\Api\Payment\Application\Payload\Dto\NotificationData;
use cebe\openapi\spec\Schema;
use cebe\openapi\spec\Type;
use JMS\Serializer\Annotation as Serializer;

/** @implements SupportsDtoDeserialization<NotificationData> */
final class NotificationPayloadSchema implements OASchema, SupportsDtoDeserialization
{
    /** @var array{payload: non-empty-string}|null */
    #[Serializer\Type('array')]
    public array|null $variables = null;

    private function __construct()
    {
    }

    public static function getSchemaSpec(): Schema
    {
        return new Schema([
            'type' => Type::OBJECT,
            'properties' => [
                'variables' => new Schema([
                    'type' => Type::ARRAY,
                    'items' => [
                        'anyOf' => [
                            new Schema(['type' => Type::ARRAY]),
                            new Schema(['type' => Type::BOOLEAN]),
                            new Schema(['type' => Type::STRING]),
                            new Schema(['type' => Type::INTEGER]),
                            new Schema(['type' => Type::NUMBER]),
                            new Schema(['type' => Type::OBJECT]),
                        ],
                    ],
                    'example' => '{"payload": "..."}',
                ]),
            ],
        ]);
    }

    public function validateSchemaProperties(): iterable
    {
        yield SchemaPropertyValidator::isNotNull($this->variables, 'variables');
    }

    public function toDto(): NotificationData
    {
        $payload = SchemaPropertyResolver::requireNotNull($this->variables['payload'] ?? null);

        return new NotificationData($payload);
    }
}
