<?php

declare(strict_types=1);

namespace Cdn77\Api\Payment\Application\Payload;

use Cdn77\Api\Core\Application\Payload\OASchema;
use Cdn77\Api\Core\Application\Payload\SchemaPropertyValidator;
use Cdn77\Api\Core\Application\Payload\SupportsDtoDeserialization;
use Cdn77\Api\Payment\Application\Payload\Dto\PaymentData;
use Cdn77\Api\Payment\Domain\Dto\PaymentRequestData;
use cebe\openapi\spec\Schema;
use cebe\openapi\spec\Type;
use JMS\Serializer\Annotation as Serializer;

/**
 * @implements SupportsDtoDeserialization<PaymentData>
 * @phpstan-import-type RequestDataShape from PaymentRequestData
 */
final class PaymentSchema implements OASchema, SupportsDtoDeserialization
{
    /** @var RequestDataShape|null */
    #[Serializer\Type('array')]
    public array|null $payment = null;

    public string|null $paymentSource = null;

    public PaymentRecipeConfigurationSchema|null $paymentRecipe = null;

    private function __construct()
    {
    }

    public static function getSchemaSpec(): Schema
    {
        return new Schema([
            'type' => Type::OBJECT,
            'properties' => [
                'payment' => new Schema([
                    'type' => Type::ARRAY,
                    'items' => [
                        'anyOf' => [
                            new Schema(['type' => Type::ARRAY]),
                            new Schema(['type' => Type::BOOLEAN]),
                            new Schema(['type' => Type::STRING]),
                            new Schema(['type' => Type::INTEGER]),
                            new Schema(['type' => Type::NUMBER]),
                            new Schema(['type' => Type::OBJECT]),
                        ],
                    ],
                    'example' => '...payment data from paygate (amount, currency, clientData)',
                ]),
            ],
        ]);
    }

    public function validateSchemaProperties(): iterable
    {
        yield SchemaPropertyValidator::isNotNull($this->payment, 'payment');
        yield SchemaPropertyValidator::isNotNull($this->paymentSource, 'payment_source');

        yield SchemaPropertyValidator::keyExists(
            $this->payment ?? [],
            'clientData',
            'Invalid payment request.',
            'payment',
        );

        yield SchemaPropertyValidator::keyExists(
            $this->payment ?? [],
            'amount',
            'Invalid payment request.',
            'payment',
        );

        yield SchemaPropertyValidator::keyExists(
            $this->payment ?? [],
            'currency',
            'Invalid payment request.',
            'payment',
        );
    }

    public function toDto(): PaymentData
    {
        return PaymentData::fromSchema($this);
    }
}
