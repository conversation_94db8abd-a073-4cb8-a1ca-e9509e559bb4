<?php

declare(strict_types=1);

namespace Cdn77\Api\Payment\Application\Payload\Dto;

use Paygate\Client;
use Paygate\Types\Notification\Notification;

final readonly class NotificationData
{
    /** @param non-empty-string $payload */
    public function __construct(private string $payload)
    {
    }

    public function getNotification(Client $paygate): Notification
    {
        return $paygate->parseNotificationPayload($this->payload);
    }
}
