<?php

declare(strict_types=1);

namespace Cdn77\Api\Payment\Application\Controller;

use Cdn77\Api\Core\Application\Controller\HasInternalOpenApiPaths;
use Cdn77\Api\Core\Application\OpenApi\Model\Tags;
use Cdn77\Api\Core\Application\OpenApi\PathGenerator;
use Cdn77\Api\Core\Application\Payload\ErrorsSchema;
use Cdn77\Api\Core\Application\Request\ControllerSchemaSerializer;
use Cdn77\Api\Core\Application\Response\ControllerCommandHandler;
use Cdn77\Api\Core\Application\Symfony\LoggedAccountProvider;
use Cdn77\Api\Core\Domain\Entity\Payment\PaymentRecipeId;
use Cdn77\Api\Payment\Application\Payload\EditCreditCardSchema;
use Cdn77\Api\Payment\Domain\Command\EditCreditCard;
use cebe\openapi\spec\MediaType;
use cebe\openapi\spec\Operation;
use cebe\openapi\spec\PathItem;
use cebe\openapi\spec\RequestBody;
use cebe\openapi\spec\Response as OpenApiResponse;
use cebe\openapi\spec\Responses;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;

final readonly class EditCreditCardController implements HasInternalOpenApiPaths
{
    public const string RouteName = 'payment.credit-card.edit';
    public const string RouteSummary = 'Edit credit card';

    public function __construct(
        private ControllerCommandHandler $controllerCommandHandler,
        private ControllerSchemaSerializer $controllerSchemaSerializer,
        private LoggedAccountProvider $loggedAccountProvider,
        private PathGenerator $pathGenerator,
    ) {
    }

    #[Route(path: '/payment/credit-card/{id}', name: self::RouteName, methods: [Request::METHOD_PATCH])]
    public function execute(Request $request, PaymentRecipeId $id): Response
    {
        $result = $this->controllerSchemaSerializer->deserialize($request, EditCreditCardSchema::class);

        if ($result instanceof ErrorsSchema) {
            return $this->controllerSchemaSerializer->serializeToResponse($result, Response::HTTP_UNPROCESSABLE_ENTITY);
        }

        $customerId = $this->loggedAccountProvider->customerId();

        return $this->controllerCommandHandler->handle(
            new EditCreditCard($id, $customerId, $result),
        );
    }

    public function getPathItems(): array
    {
        $patch = new Operation([
            'operationId' => self::RouteName,
            'tags' => [Tags::Payment],
            'summary' => self::RouteSummary,
            'responses' => new Responses([
                Response::HTTP_NO_CONTENT => new OpenApiResponse(
                    ['description' => 'Credit card updated succesfully.'],
                ),
            ]),
            'requestBody' => new RequestBody([
                'required' => true,
                'content' => [
                    'application/json' => new MediaType([
                        'schema' => EditCreditCardSchema::getSchemaSpec(),
                    ]),
                ],
            ]),
        ]);

        return [$this->pathGenerator->generate(self::RouteName) => new PathItem(['patch' => $patch])];
    }
}
