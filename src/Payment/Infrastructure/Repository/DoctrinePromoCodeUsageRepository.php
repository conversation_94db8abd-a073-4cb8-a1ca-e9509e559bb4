<?php

declare(strict_types=1);

namespace Cdn77\Api\Payment\Infrastructure\Repository;

use Cdn77\Api\Core\Domain\Entity\Promo\PromoCodeUsage;
use Cdn77\Api\Core\Infrastructure\EntityManagerConstructor;
use Cdn77\Api\Payment\Domain\Repository\PromoCodeUsageRepository;

final class DoctrinePromoCodeUsageRepository implements PromoCodeUsageRepository
{
    use EntityManagerConstructor;

    public function add(PromoCodeUsage $promoCodeUsage): void
    {
        $this->entityManager->persist($promoCodeUsage);
    }
}
