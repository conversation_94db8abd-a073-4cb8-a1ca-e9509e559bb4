<?php

declare(strict_types=1);

namespace Cdn77\AraClient\Prices\VO;

use JMS\Serializer\Annotation as Serializer;

final class Prices
{
    /** @var array<Price> */
    #[Serializer\Type('array<' . Price::class . '>')]
    #[Serializer\Inline]
    public array $prices;

    private function __construct()
    {
    }

    /** @return array<Price> */
    public function getPrices() : array
    {
        return $this->prices;
    }
}
