<?php

declare(strict_types=1);

namespace Cdn77\Api\Origin\Infrastructure\Finder;

use Cdn77\Api\Core\Domain\Entity\Customer\CustomerUuid;
use Cdn77\Api\Core\Domain\Entity\Origin\OriginId;
use Cdn77\Api\GraphQL\Domain\Schema\Type\Dto\DefaultDtoMapperProvider;
use Cdn77\Api\Origin\Domain\Dto\SharedOriginInfo;
use Cdn77\Api\Origin\Domain\Finder\OriginsFinder;
use Cdn77\Api\Origin\Domain\Query\FindOrigins\Filter;
use Cdn77\Api\Origin\Domain\Query\FindOrigins\OrderField;
use Cdn77\Api\Origin\Domain\Query\FindOrigins\Output\Origin;
use Cdn77\Api\Origin\Domain\Query\FindOrigins\Output\Origins;
use Cdn77\Api\Origin\Domain\Query\FindOrigins\SelectionField;
use Cdn77\GraphQLUtils\Pagination\OffsetPagination\Builder\Dbal\OffsetPaginationBuilder;
use Cdn77\GraphQLUtils\Pagination\OffsetPagination\Value\OffsetPagination;
use Cdn77\GraphQLUtils\Pagination\OffsetPagination\Value\OrderDirection;
use CuyZ\Valinor\Mapper\MappingError;
use Doctrine\DBAL\ArrayParameterType;
use Doctrine\DBAL\Connection;
use Doctrine\DBAL\Exception;
use Doctrine\DBAL\Query\QueryBuilder;
use Ds\Map;
use Ds\Pair;
use Ds\Set;
use Ds\Vector;
use Webmozart\Assert\Assert;

use function Cdn77\Functions\mapFromIterable;
use function Cdn77\Functions\vectorFromIterable;

final readonly class DbalOriginsFinder implements OriginsFinder
{
    public function __construct(
        private Connection $connection,
        private OffsetPaginationBuilder $paginationBuilder,
    ) {
    }

    /**
     * @throws Exception
     * @throws MappingError
     */
    public function find(
        Set $selectionFields,
        Filter $filter,
        OffsetPagination $pagination,
        Map $orderings,
    ): Origins {
        $originQueryBuilder = $this->createOriginQueryBuilder($selectionFields, $filter);
        $storageQueryBuilder = $this->createStorageQueryBuilder($selectionFields, $filter);

        $originsSql = $originQueryBuilder->getSQL();
        $storagesSql = $storageQueryBuilder->getSQL();
        $queryBuilder = $this->connection->createQueryBuilder()
            ->from(
                <<<PSQL
                (
                    $originsSql
                    UNION
                    $storagesSql
                )
                PSQL,
                'origin',
            );
        $queryBuilder->setParameters([
            ...$originQueryBuilder->getParameters(),
            ...$storageQueryBuilder->getParameters(),
        ], [
            ...$originQueryBuilder->getParameterTypes(),
            ...$storageQueryBuilder->getParameterTypes(),
        ]);

        $totalCount = $selectionFields->contains(SelectionField::TotalCount)
            ? $queryBuilder->select('count(1)')->fetchOne()
            : null;

        Assert::nullOrInteger($totalCount);
        $this->applySelectionFields($queryBuilder, $selectionFields);

        if ($orderings->isEmpty()) {
            $queryBuilder->addSelect('origin."createdAt"');
            $orderings->put(OrderField::CreatedAt, OrderDirection::Desc);
        }

        /** @var Map<string, OrderDirection> $sqlOrderings */
        $sqlOrderings = mapFromIterable(
            $orderings,
            static fn (OrderField $field, OrderDirection $direction): Pair => new Pair(
                match ($field) {
                    OrderField::CreatedAt => 'origin."createdAt"',
                    OrderField::Label => 'origin."label"',
                },
                $direction,
            ),
        );
        $queryBuilder = $this->paginationBuilder->build(
            $queryBuilder,
            $pagination,
            $sqlOrderings,
        );
        $mapper = DefaultDtoMapperProvider::get();
        $origins = new Vector($mapper->map(Origin::class . '[]', $queryBuilder->fetchAllAssociative()));

        return new Origins(
            origins: $origins,
            totalCount: $totalCount,
        );
    }

    public function findSharedForCustomerAndHost(
        CustomerUuid $customerId,
        string $host,
    ): Vector {
        /** @var list<array{id: string, url: string, customer_id: string}> $result */
        $result = $this->connection->fetchAllAssociative(
            <<<'PSQL'
SELECT origin.id, origin.url, origin.customer_id
FROM cdn_origin origin
WHERE origin.customer_id != :customerId
AND origin.url ILIKE :host
PSQL,
            [
                'customerId' => $customerId->toString(),
                'host' => $host,
            ],
        );

        return vectorFromIterable(
            $result,
            /** @param array{id: string, url: string, customer_id: string} $result */
            static fn (mixed $_, array $result): SharedOriginInfo => new SharedOriginInfo(
                CustomerUuid::fromString($result['customer_id']),
                $result['url'],
                OriginId::fromString($result['id']),
            ),
        );
    }

    /** @param Set<SelectionField> $selectionFields */
    private function createOriginQueryBuilder(Set $selectionFields, Filter $filter): QueryBuilder
    {
        $queryBuilder = $this->connection->createQueryBuilder()
            ->select('origin.id', 'origin.type', 'origin.created_at as "createdAt"')
            ->from('cdn_origin', 'origin');

        foreach ($selectionFields as $field) {
            $sqlColumn = match ($field) {
                SelectionField::Customer => 'origin.customer_id as "customerId"',
                SelectionField::Host => 'origin.url as "host"',
                SelectionField::Label => 'origin.label as "label"',
                SelectionField::RemovedAt => 'origin.removed_at as "removedAt"',
                SelectionField::Scheme => 'origin.scheme as "scheme"',
                SelectionField::CreatedAt,
                SelectionField::Id,
                SelectionField::TotalCount,
                SelectionField::Type => null,
            };

            if ($sqlColumn === null) {
                continue;
            }

            $queryBuilder->addSelect($sqlColumn);
        }

        $this->applyOriginFilters($queryBuilder, $filter);

        return $queryBuilder;
    }

    /** @param Set<SelectionField> $selectionFields */
    private function createStorageQueryBuilder(Set $selectionFields, Filter $filter): QueryBuilder
    {
        $queryBuilder = $this->connection->createQueryBuilder()
            ->select('storage.new_id as id', "'storage' as type", 'storage.ts as "createdAt"')
            ->from('storages.zone', 'storage');

        if (! $filter->customerIds->isEmpty() || $selectionFields->contains(SelectionField::Customer)) {
            $queryBuilder->join(
                'storage',
                'public.accounts',
                'customer',
                'customer.id = storage.user_id',
            );
        }

        foreach ($selectionFields as $field) {
            $sqlColumn = match ($field) {
                SelectionField::Customer => 'customer.new_id as "customerId"',
                SelectionField::Host => 'storage.www_url as "host"',
                SelectionField::Label => 'storage.zone_name as "label"',
                SelectionField::RemovedAt => 'null as "removedAt"',
                SelectionField::Scheme => '\'http\' as "scheme"',
                SelectionField::CreatedAt,
                SelectionField::Id,
                SelectionField::TotalCount,
                SelectionField::Type => null,
            };

            if ($sqlColumn === null) {
                continue;
            }

            $queryBuilder->addSelect($sqlColumn);
        }

        $this->applyStorageFilters($queryBuilder, $filter);

        return $queryBuilder;
    }

    private function applyStorageFilters(QueryBuilder $queryBuilder, Filter $filter): void
    {
        if (! $filter->originIds->isEmpty()) {
            $queryBuilder
                ->andWhere('storage.new_id IN (:originIds)')
                ->setParameter('originIds', $filter->originIds->toArray(), ArrayParameterType::STRING);
        }

        if (! $filter->customerIds->isEmpty()) {
            $queryBuilder
                ->andWhere('customer.new_id IN (:customerIds)')
                ->setParameter('customerIds', $filter->customerIds->toArray(), ArrayParameterType::STRING);
        }

        if ($filter->search === null) {
            return;
        }

        $queryBuilder
            ->andWhere(
                <<<'PSQL'
(storage.new_id::text = :query)
    OR (storage.zone_name ILIKE :pattern)
    OR (storage.www_url ILIKE :pattern)
PSQL,
            )
            ->setParameters([
                'query' => $filter->search,
                'pattern' => '%' . $filter->search . '%',
            ]);
    }

    private function applyOriginFilters(QueryBuilder $queryBuilder, Filter $filter): void
    {
        if (! $filter->originIds->isEmpty()) {
            $queryBuilder
                ->andWhere('origin.id IN (:originIds)')
                ->setParameter('originIds', $filter->originIds->toArray(), ArrayParameterType::STRING);
        }

        if (! $filter->customerIds->isEmpty()) {
            $queryBuilder
                ->andWhere('origin.customer_id IN (:customerIds)')
                ->setParameter('customerIds', $filter->customerIds->toArray(), ArrayParameterType::STRING);
        }

        if ($filter->search === null) {
            return;
        }

        $queryBuilder
            ->andWhere(
                <<<'PSQL'
(origin.id::text = :query)
    OR (origin.label ILIKE :pattern)
    OR (origin.url ILIKE :pattern)
PSQL,
            )
            ->setParameters([
                'query' => $filter->search,
                'pattern' => '%' . $filter->search . '%',
            ]);
    }

    /** @param Set<SelectionField> $selectionFields */
    private function applySelectionFields(
        QueryBuilder $queryBuilder,
        Set $selectionFields,
    ): void {
        $queryBuilder->select('origin."id"', 'origin."type"');

        foreach ($selectionFields as $field) {
            $sqlColumn = match ($field) {
                SelectionField::CreatedAt => 'origin."createdAt"',
                SelectionField::Customer => 'origin."customerId"',
                SelectionField::Host => 'origin."host"',
                SelectionField::Label => 'origin."label"',
                SelectionField::RemovedAt => 'origin."removedAt"',
                SelectionField::Scheme => 'origin."scheme"',
                SelectionField::Id,
                SelectionField::TotalCount,
                SelectionField::Type => null,
            };

            if ($sqlColumn === null) {
                continue;
            }

            $queryBuilder->addSelect($sqlColumn);
        }
    }
}
