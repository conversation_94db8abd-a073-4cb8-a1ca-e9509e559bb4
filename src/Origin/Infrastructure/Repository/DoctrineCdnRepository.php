<?php

declare(strict_types=1);

namespace Cdn77\Api\Origin\Infrastructure\Repository;

use Cdn77\Api\Core\Domain\Entity\Cdn\Cdn;
use Cdn77\Api\Core\Domain\Entity\Cdn\CdnHttp;
use Cdn77\Api\Core\Domain\Entity\Origin\OriginId;
use Cdn77\Api\Core\Infrastructure\EntityManagerConstructor;
use Cdn77\Api\Origin\Domain\Repository\CdnRepository;
use Cdn77\Api\Storage\Domain\Value\StorageSecret;
use Doctrine\ORM\Query\Expr\Join;
use Webmozart\Assert\Assert;

final class DoctrineCdnRepository implements CdnRepository
{
    use EntityManagerConstructor;

    public function findAllForOrigin(OriginId $originId): array
    {
        $cdns = $this->entityManager->createQueryBuilder()
            ->select('cdn')
            ->from(Cdn::class, 'cdn')
            ->innerJoin(CdnHttp::class, 'http', Join::WITH, 'http.cdnLegacyId = cdn.legacyId')
            ->where('http.originId = :originId')
            ->setParameter('originId', $originId->toUid())
            ->andWhere('cdn.removalTime IS NULL')
            ->andWhere('cdn.suspensionTime IS NULL')
            ->getQuery()
            ->getResult();

        Assert::isList($cdns);

        return $cdns;
    }

    public function findAllForStorage(StorageSecret $storageSecret): array
    {
        $cdns = $this->entityManager->createQueryBuilder()
            ->select('cdn')
            ->from(Cdn::class, 'cdn')
            ->innerJoin(CdnHttp::class, 'http', Join::WITH, 'http.cdnLegacyId = cdn.legacyId')
            ->where('http.storageSecret = :storageSecret')
            ->setParameter('storageSecret', $storageSecret->get())
            ->andWhere('cdn.removalTime IS NULL')
            ->getQuery()
            ->getResult();

        Assert::isList($cdns);

        return $cdns;
    }
}
