<?php

declare(strict_types=1);

namespace Cdn77\Api\Origin\Application\Payload;

use Cdn77\Api\Core\Application\Payload\OASchema;
use Cdn77\Api\Core\Application\Payload\SchemaPropertyResolver;
use Cdn77\Api\Core\Application\Payload\SchemaPropertyValidator;
use Cdn77\Api\Core\Application\Payload\SupportsRequestCommandDeserialization;
use Cdn77\Api\Core\Domain\Entity\Customer\CustomerUuid;
use Cdn77\Api\Core\Domain\Entity\Origin\Origin;
use Cdn77\Api\Core\Domain\Value\Enums\EnumValues;
use Cdn77\Api\Core\Domain\Value\Label;
use Cdn77\Api\Core\Domain\Value\Origin\BaseDirectory;
use Cdn77\Api\Core\Domain\Value\Origin\Host;
use Cdn77\Api\Core\Domain\Value\Origin\Port;
use Cdn77\Api\Core\Domain\Value\OriginScheme;
use Cdn77\Api\Origin\Domain\Command\CreateOrigin;
use cebe\openapi\spec\Schema;
use cebe\openapi\spec\Type;

use function rtrim;
use function sprintf;
use function trim;

/** @implements SupportsRequestCommandDeserialization<CreateOrigin> */
final class UrlOriginSchema implements OASchema, SupportsRequestCommandDeserialization
{
    public const string FieldHost = 'host';
    public const string FieldLabel = 'label';
    public const string FieldNote = 'note';
    public const string FieldPort = 'port';
    public const string FieldScheme = 'scheme';

    private const string ErrorOriginHostOrIp = 'Origin scheme and host must represent a valid domain name'
        . ' or public range IP address.';

    public string|null $baseDir = null;

    public string|null $label = null;

    public string|null $note = null;

    public int|null $port = null;

    public string|null $scheme = null;

    public string|null $host = null;

    public OriginFallbackConfigurationSchema|null $fallbackConfiguration = null;

    private function __construct()
    {
    }

    public static function getSchemaSpec(): Schema
    {
        return new Schema([
            'type' => Type::OBJECT,
            'properties' => [
                'base_dir' => new Schema([
                    'type' => Type::STRING,
                    'example' => '/pictures/images',
                    'description' =>
                        sprintf(
                            'Directory where the content is stored on the URL Origin. ' .
                            'It must not end with the slash. Maximum length is %d.',
                            BaseDirectory::MaxLength,
                        ),
                    'nullable' => true,
                ]),
                self::FieldLabel => new Schema([
                    'type' => Type::STRING,
                    'example' => 'My URL Origin',
                    'description' => 'The label helps you to identify your URL Origin.',
                ]),
                self::FieldNote => OriginDetailSchema::getNoteSchemaSpec(),
                self::FieldPort => new Schema([
                    'type' => Type::INTEGER,
                    'example' => 8080,
                    'description' =>
                        sprintf(
                            'URL Origin port. If not specified, default scheme port is used. ' .
                            'Allowed range is between %d and %d.',
                            Origin::PortMin,
                            Origin::PortMax,
                        ),
                    'nullable' => true,
                ]),
                self::FieldScheme => OriginScheme::getReference(),
                self::FieldHost => new Schema([
                    'type' => Type::STRING,
                    'example' => 'my-domain.com',
                    'description' => 'URL Origin host without scheme and port. Can be domain name or IP address.',
                ]),
                //todo: component
                //self::FieldFallbackConfiguration => OriginFallbackConfigurationSchema::getSchemaSpec(),
            ],
            'required' => [
                self::FieldHost,
                self::FieldLabel,
                self::FieldScheme,
            ],
        ]);
    }

    public function validateSchemaProperties(): iterable
    {
        if ($this->baseDir !== null) {
            yield SchemaPropertyValidator::regex(
                $this->baseDir,
                BaseDirectory::AllowedPattern,
                'Path (base dir) should not end with "/"'
                . ' and should only contain alphanumeric characters or (.-_[]+%*/).',
                'base_dir',
            );

            yield SchemaPropertyValidator::maxLength($this->baseDir, BaseDirectory::MaxLength, 'base_dir');
        }

        if ($this->port !== null) {
            yield SchemaPropertyValidator::range(
                $this->port,
                Origin::PortMin,
                Origin::PortMax,
                'origin_port',
            );
        }

        yield SchemaPropertyValidator::isNotNull($this->host, 'host');
        yield SchemaPropertyValidator::notContains($this->host ?? '', '/', 'host');

        yield SchemaPropertyValidator::notRegex(
            $this->host ?? '',
            Host::PatternHostContainsPort,
            'Origin host cannot contain port number. You should specify it in the "port" parameter.',
            'host',
        );

        $hostIsIpError = SchemaPropertyValidator::isIpv4(
            $this->host ?? '',
            self::ErrorOriginHostOrIp,
            'host',
        );

        yield $hostIsIpError === null
            ? SchemaPropertyValidator::isPublicIpv4(
                $this->host ?? '',
                self::ErrorOriginHostOrIp,
                'host',
            )
            : SchemaPropertyValidator::regex(
                $this->host ?? '',
                Host::PatternHost,
                self::ErrorOriginHostOrIp,
                'host',
            );

        yield SchemaPropertyValidator::isNotNull($this->label, self::FieldLabel);

        if ($this->label !== null) {
            $label = trim($this->label);

            yield SchemaPropertyValidator::notContainsTags($label, self::FieldLabel);
            yield SchemaPropertyValidator::lengthBetween($label, 1, Origin::LabelMaxLength, self::FieldLabel);
        }

        yield SchemaPropertyValidator::isNotNull($this->scheme, 'scheme');
        yield SchemaPropertyValidator::isInArray($this->scheme, EnumValues::get(OriginScheme::class), 'scheme');
    }

    public function toCommand(CustomerUuid $customerId): CreateOrigin
    {
        return CreateOrigin::urlOrigin(
            $customerId,
            rtrim(SchemaPropertyResolver::requireNotNull($this->host), '/'),
            Label::fromString(SchemaPropertyResolver::requireNotNull($this->label)),
            $this->note,
            OriginScheme::from(SchemaPropertyResolver::requireNotNull($this->scheme)),
            BaseDirectory::new($this->baseDir),
            Port::fromNullableInteger($this->port),
            $this->fallbackConfiguration?->toDto(),
        );
    }
}
