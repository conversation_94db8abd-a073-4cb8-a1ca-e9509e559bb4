<?php

declare(strict_types=1);

namespace Cdn77\Api\Origin\Application\Payload;

use Cdn77\Api\Core\Application\OpenApi\Model\ComponentReference;
use Cdn77\Api\Core\Application\Payload\CommandBusResultSchema;
use Cdn77\Api\Core\Application\Payload\QueryBusResultSchema;
use Cdn77\Api\Core\Domain\Entity\Cdn\Cdn;
use Cdn77\Api\Core\Domain\Value\Origin\ConnectionType;
use Cdn77\Api\Core\Domain\Value\Origin\ObjectStorageType;
use Cdn77\Api\Core\Domain\Value\Origin\Port;
use Cdn77\Api\Core\Domain\Value\Origin\Ssl;
use Cdn77\Api\Core\Domain\Value\OriginScheme;
use Cdn77\Api\Origin\Application\Docs\ObjectStorage\ObjectStorageOriginDetailSchemaSpec;
use Cdn77\Api\Origin\Domain\Dto\ObjectStorageOriginDetail;
use cebe\openapi\exceptions\TypeErrorException;
use cebe\openapi\spec\Reference;
use DateTimeImmutable;
use Symfony\Component\Uid\Uuid;
use Webmozart\Assert\Assert;

use function array_map;

final class ObjectStorageOriginDetailSchema
    extends OriginDetailSchema
    implements CommandBusResultSchema, QueryBusResultSchema
{
    public const string FieldBaseDir = 'base_dir';
    public const string FieldBucketName = 'bucket_name';
    public const string FieldCreatedAt = 'created_at';
    public const string FieldObjectStorageType = 'object_storage_type';
    public const string FieldPrimaryOriginId = 'primary_origin_id';
    public const string FieldUsage = 'usage';

    /** @param list<CdnReferenceSchema> $cdns */
    public function __construct(
        Uuid $id,
        public readonly DateTimeImmutable $createdAt,
        public readonly string $bucketName,
        string $label,
        string|null $note,
        array $cdns,
        OriginScheme $scheme,
        ConnectionType $type,
        public readonly string $host,
        public readonly string|null $baseDir,
        public readonly int|null $port,
        int|null $timeout,
        Ssl $ssl,
        public readonly ObjectStorageUsageSchema $usage,
        public readonly Uuid|null $primaryOriginId,
        public readonly ObjectStorageType|null $objectStorageType,
        public readonly null $accessKeyId = null, // legacy
        public readonly null $accessSecret = null, // legacy
    ) {
        parent::__construct($id, $label, $note, $cdns, $scheme, $type, $timeout, $ssl);
    }

    public static function fromQueryBusResult(mixed $result): self
    {
        Assert::isInstanceOf($result, ObjectStorageOriginDetail::class);

        return self::fromOriginDetail($result);
    }

    public static function fromOriginDetail(ObjectStorageOriginDetail $cdnOrigin): self
    {
        return new static(
            $cdnOrigin->id,
            $cdnOrigin->createdAt,
            $cdnOrigin->bucketName,
            $cdnOrigin->label->value,
            $cdnOrigin->note->get(),
            array_map(
                static fn (Cdn $cdn): CdnReferenceSchema => CdnReferenceSchema::fromCdn($cdn),
                $cdnOrigin->cdnsConnected,
            ),
            OriginScheme::Https,
            $cdnOrigin->connectionType,
            $cdnOrigin->host,
            $cdnOrigin->baseDir->value,
            Port::fromNullableInteger(443)->value,
            $cdnOrigin->timeout,
            $cdnOrigin->ssl,
            ObjectStorageUsageSchema::fromUsage($cdnOrigin->usage),
            $cdnOrigin->primaryOriginId?->toUid(),
            $cdnOrigin->objectStorageType === ObjectStorageType::Origin ? null : $cdnOrigin->objectStorageType,
        );
    }

    /** @throws TypeErrorException */
    public static function reference(): Reference
    {
        return ComponentReference::forSchema(ObjectStorageOriginDetailSchemaSpec::class);
    }

    public static function fromCommandBusResult(mixed $result): CommandBusResultSchema
    {
        Assert::isInstanceOf($result, ObjectStorageOriginDetail::class);

        return self::fromOriginDetail($result);
    }
}
