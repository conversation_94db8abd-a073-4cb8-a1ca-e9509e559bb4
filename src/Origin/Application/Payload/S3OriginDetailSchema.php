<?php

declare(strict_types=1);

namespace Cdn77\Api\Origin\Application\Payload;

use Cdn77\Api\Core\Application\OpenApi\Model\ComponentReference;
use Cdn77\Api\Core\Application\Payload\CommandBusResultSchema;
use Cdn77\Api\Core\Application\Payload\QueryBusResultSchema;
use Cdn77\Api\Core\Domain\Entity\Cdn\Cdn;
use Cdn77\Api\Core\Domain\Value\Origin\ConnectionType;
use Cdn77\Api\Core\Domain\Value\Origin\Ssl;
use Cdn77\Api\Core\Domain\Value\OriginScheme;
use Cdn77\Api\Origin\Application\Docs\S3\S3OriginDetailSchemaSpec;
use Cdn77\Api\Origin\Domain\Dto\S3OriginDetail;
use cebe\openapi\exceptions\TypeErrorException;
use cebe\openapi\spec\Reference;
use Symfony\Component\Uid\Uuid;
use Webmozart\Assert\Assert;

use function array_map;

final class S3OriginDetailSchema
    extends OriginDetailSchema
    implements CommandBusResultSchema, QueryBusResultSchema
{
    public const string FieldAwsAccessKeyId = 'aws_access_key_id';
    public const string FieldAwsRegion = 'aws_region';
    public const string FieldBaseDir = 'base_dir';

    /** @param list<CdnReferenceSchema> $cdns */
    public function __construct(
        Uuid $id,
        string $label,
        string|null $note,
        array $cdns,
        public string|null $awsAccessKeyId,
        public string|null $awsRegion,
        public string|null $baseDir,
        OriginScheme $scheme,
        ConnectionType $type,
        public string $host,
        public int|null $port,
        int|null $timeout,
        Ssl $ssl,
    ) {
        parent::__construct($id, $label, $note, $cdns, $scheme, $type, $timeout, $ssl);
    }

    public static function fromQueryBusResult(mixed $result): self
    {
        Assert::isInstanceOf($result, S3OriginDetail::class);

        return self::fromOriginDetail($result);
    }

    public static function fromOriginDetail(S3OriginDetail $cdnOrigin): self
    {
        return new self(
            $cdnOrigin->id,
            $cdnOrigin->label->value,
            $cdnOrigin->note->get(),
            array_map(
                static fn (Cdn $cdn): CdnReferenceSchema => CdnReferenceSchema::fromCdn($cdn),
                $cdnOrigin->cdnsConnected,
            ),
            $cdnOrigin->s3Credentials?->accessKeyId?->value,
            $cdnOrigin->s3Credentials?->region?->value,
            $cdnOrigin->baseDir->value,
            $cdnOrigin->scheme,
            $cdnOrigin->connectionType,
            $cdnOrigin->host,
            $cdnOrigin->port->value,
            $cdnOrigin->timeout,
            $cdnOrigin->ssl,
        );
    }

    /** @throws TypeErrorException */
    public static function reference(): Reference
    {
        return ComponentReference::forSchema(S3OriginDetailSchemaSpec::class);
    }

    public static function fromCommandBusResult(mixed $result): CommandBusResultSchema
    {
        Assert::isInstanceOf($result, S3OriginDetail::class);

        return self::fromOriginDetail($result);
    }
}
