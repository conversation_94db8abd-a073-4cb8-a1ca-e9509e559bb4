<?php

declare(strict_types=1);

namespace Cdn77\Api\Origin\Application\Controller;

use Cdn77\Api\Core\Application\Controller\HasOpenApiPaths;
use Cdn77\Api\Core\Application\OpenApi\ErrorsOpenApiResponse;
use Cdn77\Api\Core\Application\OpenApi\Model\Tags;
use Cdn77\Api\Core\Application\OpenApi\PathGenerator;
use Cdn77\Api\Core\Application\Response\ControllerCommandHandler;
use Cdn77\Api\Core\Application\Symfony\LoggedAccountProvider;
use Cdn77\Api\Core\Domain\Entity\Origin\OriginId;
use Cdn77\Api\Origin\Domain\Command\RemoveOrigin;
use cebe\openapi\exceptions\TypeErrorException;
use cebe\openapi\spec\Operation;
use cebe\openapi\spec\Parameter;
use cebe\openapi\spec\PathItem;
use cebe\openapi\spec\Response as OpenApiResponse;
use cebe\openapi\spec\Responses;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;

final readonly class RemoveController implements HasOpenApiPaths
{
    public const string LegacyRouteName = 'origin.delete.legacy';
    public const string RouteNameAws = 'origin.delete.aws';
    public const string RouteNameObjectStorage = 'origin.delete.object-storage';
    public const string RouteNameObjectStorageLegacy = 'origin.delete.bucket';
    public const string RouteNameUrl = 'origin.delete.url';

    private const string RouteSummaryAws = 'Delete AWS Origin';
    private const string RouteSummaryObjectStorage = 'Delete CDN77 Object Storage';
    private const string RouteSummaryUrl = 'Delete Your Origin';

    public function __construct(
        private ControllerCommandHandler $controllerCommandHandler,
        private LoggedAccountProvider $loggedAccountProvider,
        private PathGenerator $pathGenerator,
    ) {
    }

    #[Route(path: '/origin/{id}', name: self::LegacyRouteName, methods: [Request::METHOD_DELETE])]
    #[Route(path: '/origin/aws/{id}', name: self::RouteNameAws, methods: [Request::METHOD_DELETE])]
    #[Route(path: '/origin/object-storage/{id}', name: self::RouteNameObjectStorage, methods: [Request::METHOD_DELETE])]
    #[Route(path: '/origin/bucket/{id}', name: self::RouteNameObjectStorageLegacy, methods: [Request::METHOD_DELETE])]
    #[Route(path: '/origin/url/{id}', name: self::RouteNameUrl, methods: [Request::METHOD_DELETE])]
    public function execute(OriginId $id): Response
    {
        $customerId = $this->loggedAccountProvider->customerId();

        return $this->controllerCommandHandler->handle(new RemoveOrigin($customerId, $id));
    }

    public function getPathItems(): array
    {
        return [
            $this->pathGenerator->generate(self::RouteNameAws) => $this->getPathItem(
                [Tags::Origin, Tags::OriginAws],
                self::RouteSummaryAws,
                self::RouteNameAws,
            ),
            $this->pathGenerator->generate(self::RouteNameUrl) => $this->getPathItem(
                [Tags::Origin, Tags::OriginUrl],
                self::RouteSummaryUrl,
                self::RouteNameUrl,
            ),
            $this->pathGenerator->generate(self::RouteNameObjectStorage) => $this->getPathItem(
                [Tags::Origin, Tags::OriginObjectStorage],
                self::RouteSummaryObjectStorage,
                self::RouteNameObjectStorage,
            ),
        ];
    }

    /**
     * @param list<string> $tags
     *
     * @throws TypeErrorException
     */
    protected function getPathItem(array $tags, string $routeSummary, string $operationId): PathItem
    {
        return new PathItem([
            'delete' => new Operation([
                'operationId' => $operationId,
                'tags' => $tags,
                'summary' => $routeSummary,
                'parameters' => [
                    new Parameter([
                        'name' => 'id',
                        'in' => 'path',
                        'required' => true,
                        'schema' => OriginId::getSchemaSpec(),
                    ]),
                ],
                'responses' => new Responses([
                    Response::HTTP_NO_CONTENT => new OpenApiResponse(
                        ['description' => 'Origin removed.'],
                    ),
                    Response::HTTP_NOT_FOUND => ErrorsOpenApiResponse::spec('Origin not found.'),
                    Response::HTTP_UNPROCESSABLE_ENTITY => ErrorsOpenApiResponse::spec('Error occurred.'),
                    'default' => ErrorsOpenApiResponse::spec('Unknown error occurred.'),
                ]),
            ]),
        ]);
    }
}
