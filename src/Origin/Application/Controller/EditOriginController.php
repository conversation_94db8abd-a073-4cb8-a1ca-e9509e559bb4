<?php

declare(strict_types=1);

namespace Cdn77\Api\Origin\Application\Controller;

use Cdn77\Api\Core\Application\Controller\HasOpenApiPaths;
use Cdn77\Api\Core\Application\OpenApi\ErrorsOpenApiResponse;
use Cdn77\Api\Core\Application\OpenApi\FieldsErrorsOpenApiResponse;
use Cdn77\Api\Core\Application\OpenApi\Model\Tags;
use Cdn77\Api\Core\Application\OpenApi\PathGenerator;
use Cdn77\Api\Core\Application\Payload\ErrorsSchema;
use Cdn77\Api\Core\Application\Request\ControllerSchemaSerializer;
use Cdn77\Api\Core\Application\Response\ControllerCommandHandler;
use Cdn77\Api\Core\Application\Symfony\LoggedAccountProvider;
use Cdn77\Api\Core\Domain\Entity\Origin\OriginId;
use Cdn77\Api\Origin\Application\Payload\EditOriginSchema;
use Cdn77\Api\Origin\Application\Payload\EditS3OriginSchema;
use Cdn77\Api\Origin\Application\Payload\EditUrlOriginSchema;
use Cdn77\Api\Origin\Domain\Command\EditOrigin;
use cebe\openapi\exceptions\TypeErrorException;
use cebe\openapi\spec\MediaType;
use cebe\openapi\spec\Operation;
use cebe\openapi\spec\Parameter;
use cebe\openapi\spec\PathItem;
use cebe\openapi\spec\RequestBody;
use cebe\openapi\spec\Response as OpenApiResponse;
use cebe\openapi\spec\Responses;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;

final readonly class EditOriginController implements HasOpenApiPaths
{
    public const string LegacyRouteName = 'origin.edit';
    public const string RouteNameAws = 'origin.edit.aws';
    public const string RouteNameUrl = 'origin.edit.url';

    private const string RouteSummaryAws = 'Edit AWS Origin.';
    private const string RouteSummaryUrl = 'Edit Your Origin.';

    public function __construct(
        private ControllerCommandHandler $controllerCommandHandler,
        private ControllerSchemaSerializer $controllerSchemaSerializer,
        private LoggedAccountProvider $loggedAccountProvider,
        private PathGenerator $pathGenerator,
    ) {
    }

    #[Route(path: '/origin/{id}', name: self::LegacyRouteName, methods: [Request::METHOD_PATCH])]
    #[Route(path: '/origin/aws/{id}', name: self::RouteNameAws, methods: [Request::METHOD_PATCH])]
    #[Route(path: '/origin/url/{id}', name: self::RouteNameUrl, methods: [Request::METHOD_PATCH])]
    public function execute(Request $request, OriginId $id): Response
    {
        $customerId = $this->loggedAccountProvider->customerId();
        $result = $this->controllerSchemaSerializer->deserializeToSchema($request, EditS3OriginSchema::class);

        if ($result instanceof ErrorsSchema) {
            return $this->controllerSchemaSerializer->serializeToResponse($result, Response::HTTP_UNPROCESSABLE_ENTITY);
        }

        return $this->controllerCommandHandler->handle(new EditOrigin($id, $customerId, $result->toDto()));
    }

    public function getPathItems(): array
    {
        return [
            $this->pathGenerator->generate(self::RouteNameAws) => $this->getPathItem(
                EditS3OriginSchema::class,
                [Tags::Origin, Tags::OriginAws],
                self::RouteSummaryAws,
                self::RouteNameAws,
            ),
            $this->pathGenerator->generate(self::RouteNameUrl) => $this->getPathItem(
                EditUrlOriginSchema::class,
                [Tags::Origin, Tags::OriginUrl],
                self::RouteSummaryUrl,
                self::RouteNameUrl,
            ),
        ];
    }

    /**
     * @param class-string<EditOriginSchema> $schemaClass
     * @param list<string> $tags
     *
     * @throws TypeErrorException
     */
    private function getPathItem(string $schemaClass, array $tags, string $routeSummary, string $operationId): PathItem
    {
        return new PathItem([
            'patch' => new Operation([
                'operationId' => $operationId,
                'tags' => $tags,
                'summary' => $routeSummary,
                'description' => 'Change of origin affects all your assigned CDN Resources.',
                'parameters' => [
                    new Parameter([
                        'name' => 'id',
                        'in' => 'path',
                        'required' => true,
                        'schema' => OriginId::getSchemaSpec(),
                    ]),
                ],
                'requestBody' => new RequestBody([
                    'required' => true,
                    'content' => [
                        'application/json' => new MediaType([
                            'schema' => $schemaClass::getSchemaSpec(),
                        ]),
                    ],
                ]),
                'responses' => new Responses([
                    Response::HTTP_NO_CONTENT => new OpenApiResponse(
                        ['description' => 'Origin updated.'],
                    ),
                    Response::HTTP_NOT_FOUND => ErrorsOpenApiResponse::spec('Origin not found.'),
                    Response::HTTP_UNPROCESSABLE_ENTITY => FieldsErrorsOpenApiResponse::spec('Invalid input.'),
                    'default' => ErrorsOpenApiResponse::spec('Unknown error occurred.'),
                ]),
            ]),
        ]);
    }
}
