<?php

declare(strict_types=1);

namespace Cdn77\Api\GraphQL\Domain\DataResolution;

use Cdn77\Api\GraphQL\Domain\Schema\Type\Dto\DataTransferObject;
use Generator;

/**
 * @see DataTransferObject
 *
 * @template-covariant T of DataTransferObject
 */
interface DataProvider
{
    /** @return iterable<string|FieldId> */
    public function typeNamesOrFieldIds(): iterable;

    /**
     * @return Generator<array{class-string<T>, array<string, mixed>}>
     *
     * @phpstan-ignore shipmonk.deadMethod
     */
    public function provide(DataProviderQuery $query): Generator;
}
