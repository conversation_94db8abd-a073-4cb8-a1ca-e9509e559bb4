<?php

declare(strict_types=1);

namespace Cdn77\Api\GraphQL\Domain\DataResolution\Type\CustomerFlags;

use BackedEnum;
use Cdn77\Api\Core\Domain\Entity\Customer\CustomerUuid;
use Cdn77\Api\Core\Domain\Exception\NotImplemented;
use Cdn77\Api\Core\Domain\Messaging\QueryBus;
use Cdn77\Api\Customer\Domain\Query\FindCustomerFlags\FindCustomerFlags;
use Cdn77\Api\Customer\Domain\Query\FindCustomerFlags\Input\Filter;
use Cdn77\Api\Customer\Domain\Query\FindCustomerFlags\Input\SelectionField;
use Cdn77\Api\Customer\Domain\Query\FindCustomerFlags\Output\CustomerFlags;
use Cdn77\Api\Customer\Domain\Query\FindCustomers\Input\SelectionField as CustomerSelectionField;
use Cdn77\Api\GraphQL\Domain\DataResolution\DataLoader\DataLoader;
use Cdn77\Api\GraphQL\Domain\DataResolution\DataLoader\DataLoaderPromiseAdapterProvider;
use Cdn77\Api\GraphQL\Domain\Schema\Type\SelectionFieldsFactory;
use Cdn77\GraphQLUtils\DataLoader\DataLoaderPromiseValueSkeleton;
use Ds\Set;
use GraphQL\Executor\Promise\Promise;

use function Cdn77\Functions\absurd;

final readonly class CustomerFlagsDataLoaderFactory
{
    public function __construct(
        private QueryBus $queryBus,
        private DataLoaderPromiseAdapterProvider $promiseAdapterProvider,
    ) {
    }

    /**
     * @param array<string, mixed> $fieldSelection
     *
     * @throws NotImplemented
     */
    public function get(
        BackedEnum $field,
        array $fieldSelection,
    ): DataLoader {
        $fields = SelectionFieldsFactory::enumFromFieldSelection(
            $fieldSelection,
            SelectionField::class,
        );

        return match ($field) {
            CustomerSelectionField::Flags => $this->createForCustomerId($fields),
            $field => throw new NotImplemented(),
        };
    }

    /** @param Set<SelectionField> $fields */
    public function createForCustomerId(Set $fields): DataLoader
    {
        return new DataLoader(
            function (array $customerIds) use ($fields): Promise {
                /** @var list<CustomerUuid> $customerIds */
                $customerIdsSet = new Set($customerIds);

                $fields->add(SelectionField::CustomerId);

                $filter = new Filter(customerIds: $customerIdsSet);
                $queryResult = $this->queryBus->handle(new FindCustomerFlags($fields, $filter));

                $skeleton = DataLoaderPromiseValueSkeleton::forSingle($customerIdsSet, CustomerFlags::class);
                foreach ($queryResult->customersFlags as $view) {
                    $skeleton->put($view->customerId ?? absurd(), $view);
                }

                return $this->promiseAdapterProvider->get()->createFulfilled($skeleton);
            },
            $this->promiseAdapterProvider->get(),
        );
    }
}
