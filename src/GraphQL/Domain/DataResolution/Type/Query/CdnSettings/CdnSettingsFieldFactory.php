<?php

declare(strict_types=1);

namespace Cdn77\Api\GraphQL\Domain\DataResolution\Type\Query\CdnSettings;

use Cdn77\Api\Cdn\Domain\Query\FindCdnSettings\FindCdnSettings;
use Cdn77\Api\Cdn\Domain\Query\FindCdnSettings\Input\Filter;
use Cdn77\Api\Cdn\Domain\Query\FindCdnSettings\Input\SelectionField;
use Cdn77\Api\Core\Domain\Entity\Cdn\CdnId;
use Cdn77\Api\Core\Domain\Messaging\QueryBus;
use Cdn77\Api\GraphQL\Domain\DataResolution\Validation\OperationAssertion;
use Cdn77\Api\GraphQL\Domain\Schema\Factory\QueryFieldFactory;
use Cdn77\Api\GraphQL\Domain\Schema\Type\CdnSettings\CdnSettings;
use Cdn77\GraphQLUtils\Type\FieldSelectionMapper;
use Ds\Set;
use GraphQL\Type\Definition\ResolveInfo;
use GraphQL\Type\Definition\Type;
use SimPod\GraphQLUtils\Builder\FieldBuilder;

use function assert;
use function Cdn77\Functions\absurd;
use function is_int;

final readonly class CdnSettingsFieldFactory implements QueryFieldFactory
{
    public const string ArgumentCdnId = 'cdnId';

    public const string Name = 'cdnSettings';

    public function __construct(
        private CdnSettings $cdnSettings,
        private QueryBus $queryBus,
    ) {
    }

    public function builder(): FieldBuilder
    {
        return FieldBuilder::create(self::Name, $this->cdnSettings)
            ->addArgument(self::ArgumentCdnId, Type::nonNull(Type::int()))
            ->setResolver(
                function (
                    mixed $source,
                    array $arguments,
                    mixed $context,
                    ResolveInfo $resolveInfo,
                ): mixed {
                    $cdnId = $arguments[self::ArgumentCdnId] ?? absurd();
                    assert(is_int($cdnId));

                    $cdnSettings = $this->queryBus->handle(
                        new FindCdnSettings(
                            FieldSelectionMapper::toSelectionFields(
                                $resolveInfo->getFieldSelection(),
                                SelectionField::class,
                            ),
                            new Filter(new Set([CdnId::fromInteger($cdnId)])),
                        ),
                    )->cdnSettings;

                    if ($cdnSettings->isEmpty()) {
                        return null;
                    }

                    OperationAssertion::count($cdnSettings, 1);

                    return $cdnSettings->first();
                },
            );
    }
}
