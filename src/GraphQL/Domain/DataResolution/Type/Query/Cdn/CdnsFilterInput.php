<?php

declare(strict_types=1);

namespace Cdn77\Api\GraphQL\Domain\DataResolution\Type\Query\Cdn;

use GraphQL\Type\Definition\InputObjectType;
use GraphQL\Type\Definition\Type;
use SimPod\GraphQLUtils\Builder\InputFieldBuilder;
use SimPod\GraphQLUtils\Builder\InputObjectBuilder;

/** @phpstan-type FilterShape array{query?: string, id?: list<numeric-string>} */
final class CdnsFilterInput extends InputObjectType
{
    public const string Name = 'CdnsFilterInput';
    public const string FieldQuery = 'query';

    public const string FieldId = 'id';

    public function __construct()
    {
        $config = InputObjectBuilder::create(self::Name)
            ->setFields([
                InputFieldBuilder::create(self::FieldQuery, Type::string())->build(),
                InputFieldBuilder::create(self::FieldId, Type::listOf(Type::nonNull(Type::id())))->build(),
            ]);

        parent::__construct($config->build());
    }
}
