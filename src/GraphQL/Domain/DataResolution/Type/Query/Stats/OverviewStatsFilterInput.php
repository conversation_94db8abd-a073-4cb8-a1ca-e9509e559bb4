<?php

declare(strict_types=1);

namespace Cdn77\Api\GraphQL\Domain\DataResolution\Type\Query\Stats;

use Cdn77\Api\GraphQL\Domain\Schema\Type\Stats\DataSource;
use Cdn77\Api\GraphQL\Domain\Schema\Type\Stats\OverviewSubType;
use Cdn77\Api\GraphQL\Domain\Schema\Type\Stats\OverviewType;
use GraphQL\Type\Definition\InputObjectType;
use GraphQL\Type\Definition\Type;
use MLL\GraphQLScalars\DateTimeTz;
use SimPod\GraphQLUtils\Builder\InputFieldBuilder;
use SimPod\GraphQLUtils\Builder\InputObjectBuilder;

final class OverviewStatsFilterInput extends InputObjectType
{
    public const string FieldCustomers = 'customers';
    public const string FieldDataSource = 'dataSource';
    public const string FieldFrom = 'from';
    public const string FieldOverviewType = 'overviewType';
    public const string FieldOverviewSubType = 'overviewSubType';
    public const string FieldTo = 'to';

    public const string Name = 'OverviewStatsFilterInput';

    public function __construct(
        DateTimeTz $dateTime,
        OverviewSubType $overviewSubType,
        OverviewType $overviewType,
        DataSource $dataSource,
    ) {
        $config = InputObjectBuilder::create(self::Name)
            ->setFields([
                InputFieldBuilder::create(self::FieldCustomers, Type::listOf(Type::nonNull(Type::id())))->build(),
                InputFieldBuilder::create(self::FieldDataSource, $dataSource)->build(),
                InputFieldBuilder::create(self::FieldFrom, Type::nonNull($dateTime))->build(),
                InputFieldBuilder::create(self::FieldTo, Type::nonNull($dateTime))->build(),
                InputFieldBuilder::create(self::FieldOverviewType, Type::nonNull($overviewType))->build(),
                InputFieldBuilder::create(self::FieldOverviewSubType, Type::nonNull($overviewSubType))->build(),
            ]);

        parent::__construct($config->build());
    }
}
