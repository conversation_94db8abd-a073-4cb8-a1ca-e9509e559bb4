<?php

declare(strict_types=1);

namespace Cdn77\Api\GraphQL\Domain\DataResolution\Type\Mutation\TeamMemberAccessConfiguration;

use Cdn77\Api\GraphQL\Domain\Schema\Type\TeamMemberAccessConfiguration\Configuration;

final class EditConfigurationPayload extends MutateConfigurationPayload
{
    public const string Name = 'EditConfigurationPayload';

    public function __construct(private readonly Configuration $configuration)
    {
        parent::__construct(self::Name, $this->configuration);
    }
}
