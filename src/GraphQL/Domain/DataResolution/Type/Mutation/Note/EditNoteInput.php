<?php

declare(strict_types=1);

namespace Cdn77\Api\GraphQL\Domain\DataResolution\Type\Mutation\Note;

use Cdn77\Api\GraphQL\Domain\DataResolution\Type\Mutation\TeamMemberAccessConfiguration\MutateConfigurationInput;
use GraphQL\Type\Definition\Type;
use SimPod\GraphQLUtils\Builder\InputFieldBuilder;
use SimPod\GraphQLUtils\Builder\InputObjectBuilder;

final class EditNoteInput extends MutateConfigurationInput
{
    public const string FieldIsPinned = 'isPinned';
    public const string FieldText = 'text';
    public const string Name = 'EditNoteInput';

    public function __construct()
    {
        $config = InputObjectBuilder::create(self::Name)
            ->setFields([
                InputFieldBuilder::create(self::FieldText, Type::nonNull(Type::string()))->build(),
                InputFieldBuilder::create(self::FieldIsPinned, Type::nonNull(Type::boolean()))->build(),
            ]);

        parent::__construct($config->build());
    }
}
