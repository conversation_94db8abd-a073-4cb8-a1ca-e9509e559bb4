<?php

declare(strict_types=1);

namespace Cdn77\Api\GraphQL\Domain\DataResolution\Type\Mutation\CustomerCdnSettings;

use Cdn77\Api\Core\Domain\Entity\Customer\CustomerId;
use Cdn77\Api\Core\Domain\Messaging\CommandBus;
use Cdn77\Api\Customer\Domain\Command\UpdateCustomerCdnSettings;
use Cdn77\Api\GraphQL\Domain\DataResolution\DataLoader\DataLoaderRegistry;
use Cdn77\Api\GraphQL\Domain\DataResolution\Validation\OperationAssertion;
use Cdn77\Api\GraphQL\Domain\Schema\Factory\MutationFieldFactory;
use GraphQL\Type\Definition\ResolveInfo;
use GraphQL\Type\Definition\Type;
use SimPod\GraphQLUtils\Builder\FieldBuilder;

use function Cdn77\Functions\absurd;

final readonly class EditCustomerCdnSettings implements MutationFieldFactory
{
    public const string ArgumentCustomerId = 'customerId';
    public const string ArgumentInput = 'input';

    public const string Name = 'editCustomerCdnSettings';

    public function __construct(
        private CommandBus $commandBus,
        private DataLoaderRegistry $dataLoaderRegistry,
        private EditCustomerCdnSettingsInput $editCustomerCdnSettingsInput,
        private EditCustomerCdnSettingsPayload $editCustomerCdnSettingsPayload,
    ) {
    }

    public function builder(): FieldBuilder
    {
        return FieldBuilder::create(self::Name, Type::nonNull($this->editCustomerCdnSettingsPayload))
            ->addArgument(self::ArgumentCustomerId, Type::nonNull(Type::id()))
            ->addArgument(self::ArgumentInput, Type::nonNull($this->editCustomerCdnSettingsInput))
            ->setResolver(
                function (
                    mixed $source,
                    array $arguments,
                    mixed $context,
                    ResolveInfo $resolveInfo,
                ) {
                    $customerIdRaw = $arguments[self::ArgumentCustomerId];
                    OperationAssertion::string($customerIdRaw);
                    OperationAssertion::numeric($customerIdRaw);
                    $inputArguments = $arguments[self::ArgumentInput] ?? absurd();
                    OperationAssertion::isMap($inputArguments);
                    $customerId = CustomerId::fromString($customerIdRaw);

                    $this->commandBus->handle(
                        UpdateCustomerCdnSettings::fromGraphQLMutation(
                            $customerId,
                            $inputArguments,
                        ),
                    );

                    return new CustomerCdnSettingsPayloadDto(
                        customerCdnSettings: $this->dataLoaderRegistry->subSelection(
                            CustomerCdnSettingsPayloadField::CustomerCdnSettings,
                            $resolveInfo,
                            1,
                        )->load($customerId),
                    );
                },
            );
    }
}
