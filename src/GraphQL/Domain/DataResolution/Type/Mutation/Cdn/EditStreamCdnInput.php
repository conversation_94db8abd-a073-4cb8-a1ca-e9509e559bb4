<?php

declare(strict_types=1);

namespace Cdn77\Api\GraphQL\Domain\DataResolution\Type\Mutation\Cdn;

use Cdn77\Api\GraphQL\Domain\Schema\Type\Cdn\StreamProtocol;
use GraphQL\Type\Definition\InputObjectType;
use GraphQL\Type\Definition\Type;
use SimPod\GraphQLUtils\Builder\InputFieldBuilder;
use SimPod\GraphQLUtils\Builder\InputObjectBuilder;

final class EditStreamCdnInput extends InputObjectType
{
    public const string FieldKey = 'key';

    public const string FieldPassword = 'password';

    public const string FieldPath = 'path';

    public const string FieldPort = 'port';

    public const string FieldProtocol = 'protocol';

    public const string FieldStreamOriginId = 'streamOriginId';

    private const string Name = 'EditStreamCdnInput';

    public function __construct(StreamProtocol $streamProtocol)
    {
        $config = InputObjectBuilder::create(self::Name)
            ->setFields([
                InputFieldBuilder::create(self::FieldKey, Type::nonNull(Type::string()))->build(),
                InputFieldBuilder::create(self::FieldPassword, Type::string())->build(),
                InputFieldBuilder::create(self::FieldPath, Type::string())->build(),
                InputFieldBuilder::create(self::FieldPort, Type::nonNull(Type::int()))->build(),
                InputFieldBuilder::create(self::FieldProtocol, Type::nonNull($streamProtocol))->build(),
                InputFieldBuilder::create(self::FieldStreamOriginId, Type::nonNull(Type::id()))->build(),
            ]);

        parent::__construct($config->build());
    }
}
