<?php

declare(strict_types=1);

namespace Cdn77\Api\GraphQL\Domain\DataResolution\Type\Mutation\Cdn;

use Cdn77\Api\Cdn\Domain\Command\ChangeLocationGroup;
use Cdn77\Api\Core\Domain\Entity\Cdn\CdnId;
use Cdn77\Api\Core\Domain\Entity\Datacenter\ServerGroupId;
use Cdn77\Api\Core\Domain\Messaging\CommandBus;
use Cdn77\Api\GraphQL\Domain\Schema\Factory\MutationFieldFactory;
use GraphQL\Type\Definition\Type;
use SimPod\GraphQLUtils\Builder\FieldBuilder;

/** @phpstan-type ArgumentShape array{cdnId: int, serverGroupId: int} */
final readonly class ChangeServerGroup implements MutationFieldFactory
{
    public const string ArgumentCdnId = 'cdnId';
    public const string ArgumentServerGroupId = 'serverGroupId';

    public const string Name = 'changeServerGroup';

    public function __construct(
        private CommandBus $commandBus,
    ) {
    }

    public function builder(): FieldBuilder
    {
        return FieldBuilder::create(self::Name, Type::nonNull(Type::boolean()))
            ->addArgument(self::ArgumentCdnId, Type::nonNull(Type::id()))
            ->addArgument(self::ArgumentServerGroupId, Type::nonNull(Type::id()))
            ->setResolver(
                function (
                    mixed $source,
                    array $arguments,
                ): bool {
                    /** @phpstan-var ArgumentShape $arguments */
                    $this->commandBus->handle(
                        new ChangeLocationGroup(
                            CdnId::fromInteger($arguments[self::ArgumentCdnId]),
                            ServerGroupId::fromInteger($arguments[self::ArgumentServerGroupId]),
                        ),
                    );

                    return true;
                },
            );
    }
}
