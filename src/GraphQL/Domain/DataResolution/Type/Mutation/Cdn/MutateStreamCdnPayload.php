<?php

declare(strict_types=1);

namespace Cdn77\Api\GraphQL\Domain\DataResolution\Type\Mutation\Cdn;

use Cdn77\Api\GraphQL\Domain\Schema\Type\Cdn\StreamCdn;
use GraphQL\Type\Definition\ObjectType;
use GraphQL\Type\Definition\Type;
use SimPod\GraphQLUtils\Builder\FieldBuilder;
use SimPod\GraphQLUtils\Builder\ObjectBuilder;

abstract class MutateStreamCdnPayload extends ObjectType
{
    final public const string FieldStreamCdn = 'streamCdn';

    public function __construct(string $name, private readonly StreamCdn $streamCdn)
    {
        parent::__construct(
            ObjectBuilder::create($name)
                ->setFields([
                    FieldBuilder::create(
                        self::FieldStreamCdn,
                        Type::nonNull($this->streamCdnReturnType()),
                    )->build(),
                ])
                ->build(),
        );
    }

    public function streamCdnReturnType(): ObjectType
    {
        return $this->streamCdn;
    }
}
