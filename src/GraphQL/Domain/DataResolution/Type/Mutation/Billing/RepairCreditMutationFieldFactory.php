<?php

declare(strict_types=1);

namespace Cdn77\Api\GraphQL\Domain\DataResolution\Type\Mutation\Billing;

use Cdn77\Api\Core\Domain\Entity\Customer\CustomerUuid;
use Cdn77\Api\Core\Domain\Messaging\CommandBus;
use Cdn77\Api\GraphQL\Domain\DataResolution\Validation\OperationAssertion;
use Cdn77\Api\GraphQL\Domain\Schema\Factory\MutationFieldFactory;
use Cdn77\Api\Tariff\Domain\Command\RepairCredit;
use DateTimeImmutable;
use GraphQL\Error\InvariantViolation;
use GraphQL\Type\Definition\Type;
use MLL\GraphQLScalars\DateTimeTz;
use SimPod\GraphQLUtils\Builder\FieldBuilder;

final readonly class RepairCreditMutationFieldFactory implements MutationFieldFactory
{
    public const string ArgumentCustomerId = 'customerId';
    public const string ArgumentRepairFrom = 'repairFrom';

    public const string Name = 'repairCredit';

    public function __construct(
        private CommandBus $commandBus,
        private DateTimeTz $dateTimeTz,
    ) {
    }

    /** @throws InvariantViolation */
    public function builder(): FieldBuilder
    {
        return FieldBuilder::create(self::Name, Type::nonNull(Type::boolean()))
            ->addArgument(self::ArgumentCustomerId, Type::nonNull(Type::id()))
            ->addArgument(self::ArgumentRepairFrom, Type::nonNull($this->dateTimeTz))
            ->setResolver(
                function (
                    mixed $source,
                    array $arguments,
                ) {
                    $customerIdRaw = $arguments[self::ArgumentCustomerId];
                    OperationAssertion::string($customerIdRaw);
                    OperationAssertion::uuid($customerIdRaw);
                    $repairFrom = $arguments[self::ArgumentRepairFrom];
                    OperationAssertion::isInstanceOf($repairFrom, DateTimeImmutable::class);

                    $this->commandBus->handle(new RepairCredit(
                        CustomerUuid::fromString($customerIdRaw),
                        $repairFrom,
                    ));

                    return true;
                },
            );
    }
}
