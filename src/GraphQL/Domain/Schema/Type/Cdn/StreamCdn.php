<?php

declare(strict_types=1);

namespace Cdn77\Api\GraphQL\Domain\Schema\Type\Cdn;

use Cdn77\Api\Cdn\Domain\Query\FindStreamCdns\Input\SelectionField;
use Cdn77\Api\Cdn\Domain\Query\FindStreamCdns\Output\StreamCdn as View;
use Cdn77\Api\Core\Domain\Encryption\ValueEncryptor;
use Cdn77\Api\GraphQL\Domain\DataResolution\DataLoader\DataLoaderRegistry;
use Cdn77\Api\GraphQL\Domain\Schema\Type\Customer\Customer;
use GraphQL\Type\Definition\ObjectType;
use GraphQL\Type\Definition\ResolveInfo;
use GraphQL\Type\Definition\Type;
use SimPod\GraphQLUtils\Builder\FieldBuilder;
use SimPod\GraphQLUtils\Builder\ObjectBuilder;

final class StreamCdn extends ObjectType
{
    public const string Name = 'StreamCdn';

    public function __construct(
        DataLoaderRegistry $dataLoaderRegistry,
        StreamProtocol $streamProtocol,
        ValueEncryptor $valueEncryptor,
        Customer $customer,
    ) {
        $config = ObjectBuilder::create(self::Name)
            ->setFields([
                FieldBuilder::create(
                    SelectionField::Id,
                    Type::nonNull(Type::id()),
                )
                    ->setResolver(static fn (View $streamCdn) => $streamCdn->id)
                    ->build(),
                FieldBuilder::create(
                    SelectionField::Key,
                    Type::nonNull(Type::string()),
                )
                    ->setResolver(static fn (View $streamCdn) => $streamCdn->key)
                    ->build(),
                FieldBuilder::create(
                    SelectionField::StreamOriginId,
                    Type::nonNull(Type::id()),
                )
                    ->setResolver(static fn (View $streamCdn) => $streamCdn->streamOriginId)
                    ->build(),
                FieldBuilder::create(
                    SelectionField::Password,
                    Type::string(),
                )
                    ->setResolver(static fn (View $streamCdn) => $streamCdn->password?->decrypt($valueEncryptor))
                    ->build(),
                FieldBuilder::create(
                    SelectionField::Path,
                    Type::string(),
                )
                    ->setResolver(static fn (View $streamCdn) => $streamCdn->path)
                    ->build(),
                FieldBuilder::create(
                    SelectionField::Port,
                    Type::nonNull(Type::int()),
                )
                    ->setResolver(static fn (View $streamCdn) => $streamCdn->port?->value)
                    ->build(),
                FieldBuilder::create(
                    SelectionField::Protocol,
                    Type::nonNull($streamProtocol),
                )
                    ->setResolver(static fn (View $streamCdn) => $streamCdn->streamProtocol?->value)
                    ->build(),
                FieldBuilder::create(
                    SelectionField::Customer,
                    Type::nonNull($customer),
                )
                    ->setResolver(
                        static fn (
                            View $streamCdn,
                            array $arguments,
                            mixed $context,
                            ResolveInfo $resolveInfo,
                        ) => $dataLoaderRegistry
                            ->forResolveInfo(SelectionField::Customer, $resolveInfo)
                            ->load($streamCdn->customerId),
                    )
                    ->build(),
            ]);

        parent::__construct($config->build());
    }
}
