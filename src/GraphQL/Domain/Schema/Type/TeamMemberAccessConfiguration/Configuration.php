<?php

declare(strict_types=1);

namespace Cdn77\Api\GraphQL\Domain\Schema\Type\TeamMemberAccessConfiguration;

use Cdn77\Api\Customer\Domain\Query\FindTeamMemberAccessConfigurations\Input\SelectionField;
use Cdn77\Api\Customer\Domain\Query\FindTeamMemberAccessConfigurations\Output\AccessConfiguration;
use GraphQL\Type\Definition\ObjectType;
use GraphQL\Type\Definition\Type;
use SimPod\GraphQLUtils\Builder\FieldBuilder;
use SimPod\GraphQLUtils\Builder\ObjectBuilder;

final class Configuration extends ObjectType
{
    public const string Name = 'Configuration';

    public function __construct(AccessType $accessType, Restriction $restriction)
    {
        $config = ObjectBuilder::create(self::Name)
            ->setFields([
                FieldBuilder::create(SelectionField::Id, Type::nonNull(Type::id()))
                    ->setResolver(static fn (AccessConfiguration $view) => $view->id)
                    ->build(),
                FieldBuilder::create(SelectionField::Type, Type::nonNull($accessType))
                    ->setResolver(static fn (AccessConfiguration $view) => $view->type?->value)
                    ->build(),
                FieldBuilder::create(SelectionField::Restrictions, Type::nonNull($restriction))
                    ->setResolver(static fn (AccessConfiguration $view) => $view->restrictions)
                    ->build(),
            ]);

        parent::__construct($config->build());
    }
}
