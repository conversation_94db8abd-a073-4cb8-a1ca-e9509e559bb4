<?php

declare(strict_types=1);

namespace Cdn77\Api\GraphQL\Domain\Schema\Type\RealTimeLog;

use Cdn77\Api\ObjectStorage\Domain\Value\TtlConfig;
use GraphQL\Type\Definition\InputObjectType;
use GraphQL\Type\Definition\Type;
use SimPod\GraphQLUtils\Builder\InputFieldBuilder;
use SimPod\GraphQLUtils\Builder\InputObjectBuilder;
use Webmozart\Assert\Assert;

final class TtlConfigInput extends InputObjectType
{
    public const string Name = 'TtlConfigInput';

    public const string FieldDays = 'days';

    public function __construct()
    {
        $config = InputObjectBuilder::create(self::Name);

        $config->setFields(
            static fn (): array => [
                InputFieldBuilder::create(self::FieldDays, Type::int())->build(),
            ],
        );

        parent::__construct($config->build());
    }

    public function parseValue(array $value): TtlConfig
    {
        $days = $value[self::FieldDays] ?? null;
        Assert::nullOrInteger($days);

        return $days === null
            ? TtlConfig::indefinite()
            : TtlConfig::fromDays($days);
    }
}
