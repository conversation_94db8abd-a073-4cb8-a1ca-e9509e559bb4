<?php

declare(strict_types=1);

namespace Cdn77\Api\GraphQL\Domain\Schema\Type\RealTimeLog;

use Cdn77\Api\GraphQL\Domain\Schema\Builder\EnumBuilder;
use GraphQL\Type\Definition\EnumType;

final class OutputFormat extends EnumType
{
    public const string Name = 'OutputFormat';

    public function __construct()
    {
        $config = EnumBuilder::create(self::Name)
            ->buildFromEnum(\Cdn77\Api\Cdn\Domain\Value\Log\OutputFormat::class);

        parent::__construct($config);
    }
}
