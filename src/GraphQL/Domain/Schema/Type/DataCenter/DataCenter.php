<?php

declare(strict_types=1);

namespace Cdn77\Api\GraphQL\Domain\Schema\Type\DataCenter;

use Cdn77\Api\Datacenter\Domain\Query\FindDataCenters\Output\DataCenter as View;
use GraphQL\Type\Definition\ObjectType;
use GraphQL\Type\Definition\Type;
use SimPod\GraphQLUtils\Builder\FieldBuilder;
use SimPod\GraphQLUtils\Builder\ObjectBuilder;

final class DataCenter extends ObjectType
{
    public const string FieldCity = 'city';
    public const string FieldContinentCode = 'continentCode';
    public const string FieldCountryIso = 'countryIso';
    public const string FieldCountryName = 'countryName';
    public const string FieldId = 'id';
    public const string FieldLegacyId = 'legacyId';

    public const string Name = 'DataCenter';

    public function __construct()
    {
        $config = ObjectBuilder::create(self::Name)
            ->setFields([
                FieldBuilder::create(self::FieldId, Type::nonNull(Type::id()))
                    ->setResolver(static fn (View $view) => $view->id)
                    ->build(),
                FieldBuilder::create(self::FieldLegacyId, Type::nonNull(Type::string()))
                    ->setResolver(static fn (View $view) => $view->legacyId?->toString())
                    ->build(),
                FieldBuilder::create(self::FieldCity, Type::nonNull(Type::string()))
                    ->setResolver(static fn (View $view) => $view->city)
                    ->build(),
                FieldBuilder::create(self::FieldContinentCode, Type::nonNull(Type::string()))
                    ->setResolver(static fn (View $view) => $view->continentCode?->value)
                    ->build(),
                FieldBuilder::create(self::FieldCountryIso, Type::nonNull(Type::string()))
                    ->setResolver(static fn (View $view) => $view->countryIso?->get())
                    ->build(),
                FieldBuilder::create(self::FieldCountryName, Type::nonNull(Type::string()))
                    ->setResolver(static fn (View $view) => $view->countryName)
                    ->build(),
            ]);

        parent::__construct($config->build());
    }
}
