<?php

declare(strict_types=1);

namespace Cdn77\Api\GraphQL\Domain\Schema\Type\Income;

use Cdn77\Api\GraphQL\Domain\Schema\Type\Money\Money;
use Cdn77\Api\Plan\Domain\Dto\Revenue as RevenueDto;
use GraphQL\Type\Definition\ObjectType;
use GraphQL\Type\Definition\Type;
use SimPod\GraphQLUtils\Builder\FieldBuilder;
use SimPod\GraphQLUtils\Builder\ObjectBuilder;

final class Revenue extends ObjectType
{
    public const string FieldLost = 'lost';
    public const string FieldMonth = 'month';
    public const string FieldNew = 'new';
    public const string FieldNetChange = 'netChange';
    public const string FieldUpsell = 'upsell';

    public const string Name = 'Revenue';

    public function __construct(Money $money)
    {
        $config = ObjectBuilder::create(self::Name)
            ->setFields([
                FieldBuilder::create(self::FieldMonth, Type::nonNull(Type::string()))
                    ->setResolver(static fn (RevenueDto $revenue) => $revenue->timeRange->from->format('F'))
                    ->build(),
                FieldBuilder::create(self::FieldLost, Type::nonNull($money))
                    ->setResolver(static fn (RevenueDto $revenue) => $revenue->lost)
                    ->build(),
                FieldBuilder::create(self::FieldNew, Type::nonNull($money))
                    ->setResolver(static fn (RevenueDto $revenue) => $revenue->new)
                    ->build(),
                FieldBuilder::create(self::FieldNetChange, Type::nonNull($money))
                    ->setResolver(static fn (RevenueDto $revenue) => $revenue->netChange)
                    ->build(),
                FieldBuilder::create(self::FieldUpsell, Type::nonNull($money))
                    ->setResolver(static fn (RevenueDto $revenue) => $revenue->upsell)
                    ->build(),
            ]);

        parent::__construct($config->build());
    }
}
