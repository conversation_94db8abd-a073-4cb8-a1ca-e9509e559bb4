<?php

declare(strict_types=1);

namespace Cdn77\Api\GraphQL\Domain\Schema\Type\Stats;

use Cdn77\Api\GraphQL\Domain\Schema\Type\Scalar\ArrayType;
use GraphQL\Type\Definition\ObjectType;
use GraphQL\Type\Definition\Type;
use SimPod\GraphQLUtils\Builder\FieldBuilder;
use SimPod\GraphQLUtils\Builder\ObjectBuilder;

use function array_keys;

final class TimeSeries extends ObjectType
{
    public const string FieldTimestamps = 'timestamps';
    public const string FieldFields = 'fields';

    public const string Name = 'TimeSeries';

    public function __construct(ArrayType $array)
    {
        $config = ObjectBuilder::create(self::Name)
            ->setFields([
                FieldBuilder::create(self::FieldTimestamps, Type::nonNull(Type::listOf(Type::nonNull(Type::int()))))
                    ->setResolver(
                        /** @param array<string, int|float|string> $data */
                        static fn (array $data) => array_keys($data),
                    )
                    ->build(),
                FieldBuilder::create(self::FieldFields, Type::nonNull(Type::listOf(Type::nonNull($array))))
                    ->setResolver(
                        /** @param array<string, int|float|string> $data */
                        static fn (array $data) => $data,
                    )
                    ->build(),
            ]);

        parent::__construct($config->build());
    }
}
