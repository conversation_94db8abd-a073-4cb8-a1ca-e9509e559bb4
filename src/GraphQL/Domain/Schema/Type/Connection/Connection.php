<?php

declare(strict_types=1);

namespace Cdn77\Api\GraphQL\Domain\Schema\Type\Connection;

use GraphQL\Type\Definition\InterfaceType;
use SimPod\GraphQLUtils\Builder\InterfaceBuilder;

final class Connection extends InterfaceType
{
    private const string Name = 'Connection';

    public function __construct(ConnectionFieldsFactory $connectionFieldsFactory, Edge $edgeInterface)
    {
        $configBuilder = InterfaceBuilder::create(self::Name);

        $connectionFieldsFactory->create($configBuilder, $edgeInterface);

        parent::__construct($configBuilder->build());
    }
}
