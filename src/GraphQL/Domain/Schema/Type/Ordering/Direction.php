<?php

declare(strict_types=1);

namespace Cdn77\Api\GraphQL\Domain\Schema\Type\Ordering;

use GraphQL\Type\Definition\EnumType;
use SimPod\GraphQLUtils\Builder\EnumBuilder;

use function Symfony\Component\String\u;

final class Direction extends EnumType
{
    public const string DirectionAsc = 'ASC';
    public const string DirectionDesc = 'DESC';

    private const string Name = 'Direction';

    public function __construct()
    {
        $builder = EnumBuilder::create(self::Name);

        $values = [self::DirectionAsc, self::DirectionDesc];

        foreach ($values as $value) {
            $pascalName = u($value)->lower()->camel()->title()->toString();

            $builder->addValue($value, $pascalName);
        }

        parent::__construct($builder->build());
    }
}
