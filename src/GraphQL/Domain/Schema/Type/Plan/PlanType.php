<?php

declare(strict_types=1);

namespace Cdn77\Api\GraphQL\Domain\Schema\Type\Plan;

use Cdn77\Api\GraphQL\Domain\Schema\Builder\EnumBuilder;
use GraphQL\Type\Definition\EnumType;

final class PlanType extends EnumType
{
    public const string Name = 'PlanType';

    public function __construct()
    {
        $config = EnumBuilder::create(self::Name)
            ->buildFromEnum(\Cdn77\Api\Plan\Domain\Value\PlanType::class);

        parent::__construct($config);
    }
}
