<?php

declare(strict_types=1);

namespace Cdn77\Api\GraphQL\Domain\Schema\Type\CustomerCdnSettings;

use Cdn77\Api\Customer\Domain\Query\FindCustomerCdnSettings\Input\SelectionField;
use Cdn77\Api\Customer\Domain\Query\FindCustomerCdnSettings\Output\CustomerCdnSettings as View;
use GraphQL\Type\Definition\ObjectType;
use GraphQL\Type\Definition\Type;
use SimPod\GraphQLUtils\Builder\FieldBuilder;
use SimPod\GraphQLUtils\Builder\ObjectBuilder;

final class CustomerCdnSettings extends ObjectType
{
    public const string Name = 'CustomerCdnSettings';

    public function __construct(private readonly PrefetchMode $prefetchMode)
    {
        $config = ObjectBuilder::create(self::Name)
            ->setFields([
                FieldBuilder::create(SelectionField::Id, Type::nonNull(Type::id()))
                    ->setResolver(static fn (View $view) => $view->id?->toInt())
                    ->build(),
                FieldBuilder::create(SelectionField::ActiveCdnsCountQuota, Type::nonNull(Type::int()))
                    ->setResolver(static fn (View $view) => $view->activeCdnsCountQuota)
                    ->build(),
                FieldBuilder::create(SelectionField::ActiveTrialCdnsCountQuota, Type::nonNull(Type::int()))
                    ->setResolver(static fn (View $view) => $view->activeTrialCdnsCountQuota)
                    ->build(),
                FieldBuilder::create(SelectionField::CustomerId, Type::nonNull(Type::int()))
                    ->setResolver(static fn (View $view) => $view->customerId?->toInt())
                    ->build(),
                FieldBuilder::create(SelectionField::DailyDeletedCdnsQuota, Type::nonNull(Type::int()))
                    ->setResolver(static fn (View $view) => $view->dailyDeletedCdnsQuota)
                    ->build(),
                FieldBuilder::create(SelectionField::DataCentersEditEnabled, Type::nonNull(Type::boolean()))
                    ->setResolver(static fn (View $view) => $view->dataCentersEditEnabled)
                    ->build(),
                FieldBuilder::create(SelectionField::MaxCnames, Type::nonNull(Type::int()))
                    ->setResolver(static fn (View $view) => $view->maxCnames)
                    ->build(),
                FieldBuilder::create(SelectionField::MaxGeoProtectionCountries, Type::nonNull(Type::int()))
                    ->setResolver(static fn (View $view) => $view->maxGeoProtectionCountries)
                    ->build(),
                FieldBuilder::create(SelectionField::MaxHotlinkProtectionDomains, Type::nonNull(Type::int()))
                    ->setResolver(static fn (View $view) => $view->maxHotlinkProtectionDomains)
                    ->build(),
                FieldBuilder::create(SelectionField::MaxIgnoredQueryParameters, Type::nonNull(Type::int()))
                    ->setResolver(static fn (View $view) => $view->maxIgnoredQueryParameters)
                    ->build(),
                FieldBuilder::create(SelectionField::MaxIpProtectionAddresses, Type::nonNull(Type::int()))
                    ->setResolver(static fn (View $view) => $view->maxIpProtectionAddresses)
                    ->build(),
                FieldBuilder::create(SelectionField::MinDataCenters, Type::nonNull(Type::int()))
                    ->setResolver(static fn (View $view) => $view->minDataCenters)
                    ->build(),
                FieldBuilder::create(SelectionField::PurgeAllDisabled, Type::nonNull(Type::boolean()))
                    ->setResolver(static fn (View $view) => $view->purgeAllDisabled)
                    ->build(),
                FieldBuilder::create(SelectionField::PrefetchMode, Type::nonNull($this->prefetchMode))
                    ->setResolver(static fn (View $view) => $view->prefetchMode)
                    ->build(),
            ]);

        parent::__construct($config->build());
    }
}
