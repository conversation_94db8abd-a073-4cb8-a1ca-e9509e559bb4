<?php

declare(strict_types=1);

namespace Cdn77\Api\GraphQL\Domain\Schema\Type\MailLog;

use Cdn77\Api\Core\Domain\Query\FindMailLogs\Output\MailLog as View;
use GraphQL\Type\Definition\ObjectType;
use GraphQL\Type\Definition\Type;
use MLL\GraphQLScalars\DateTime;
use SimPod\GraphQLUtils\Builder\FieldBuilder;
use SimPod\GraphQLUtils\Builder\ObjectBuilder;

final class MailLog extends ObjectType
{
    public const string Name = 'MailLog';

    public const string FieldId = 'id';
    public const string FieldBody = 'body';
    public const string FieldDeliveryStatus = 'deliveryStatus';
    public const string FieldRecipient = 'recipient';
    public const string FieldSentAt = 'sentAt';
    public const string FieldSender = 'sender';
    public const string FieldSubject = 'subject';
    public const string FieldTemplateAlias = 'templateAlias';

    public function __construct(DateTime $dateTime)
    {
        $config = ObjectBuilder::create(self::Name);

        $config->setFields(
            static fn (): array => [
                FieldBuilder::create(self::FieldId, Type::nonNull(Type::id()))
                    ->setResolver(static fn (View $view) => $view->id->toInt())->build(),
                FieldBuilder::create(self::FieldBody, Type::string())
                    ->setResolver(static fn (View $view) => $view->body)->build(),
                FieldBuilder::create(self::FieldDeliveryStatus, Type::string())
                    ->setResolver(static fn (View $view) => $view->deliveryStatus?->value)->build(),
                FieldBuilder::create(self::FieldRecipient, Type::nonNull(Type::string()))
                    ->setResolver(static fn (View $view) => $view->recipient?->get())->build(),
                FieldBuilder::create(self::FieldSentAt, Type::nonNull($dateTime))
                    ->setResolver(static fn (View $view) => $view->sentAt)->build(),
                FieldBuilder::create(self::FieldSender, Type::string())
                    ->setResolver(static fn (View $view) => $view->sender?->get())->build(),
                FieldBuilder::create(self::FieldSubject, Type::string())
                    ->setResolver(static fn (View $view) => $view->subject)->build(),
                FieldBuilder::create(self::FieldTemplateAlias, Type::string())
                    ->setResolver(static fn (View $view) => $view->templateAlias)->build(),
            ],
        );

        parent::__construct($config->build());
    }
}
