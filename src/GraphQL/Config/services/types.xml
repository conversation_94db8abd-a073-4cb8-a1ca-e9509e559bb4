<?xml version="1.0" encoding="UTF-8" ?>
<container xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
           xmlns="http://symfony.com/schema/dic/services"
           xsi:schemaLocation="http://symfony.com/schema/dic/services
        https://symfony.com/schema/dic/services/services-1.0.xsd">

    <services>
        <defaults autowire="true" autoconfigure="true" public="false" />

        <service id="GraphQL\Type\Schema">
            <factory service="Cdn77\Api\GraphQL\Application\Runtime\SchemaFactory" method="create"/>
        </service>

        <service id="MLL\GraphQLScalars\DateTime" />
        <service id="MLL\GraphQLScalars\DateTimeTz" />
        <service id="MLL\GraphQLScalars\BigInt" />
    </services>
</container>
