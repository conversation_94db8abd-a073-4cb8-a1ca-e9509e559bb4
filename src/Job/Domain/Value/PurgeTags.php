<?php

declare(strict_types=1);

namespace Cdn77\Api\Job\Domain\Value;

use Ds\Set;

final readonly class PurgeTags
{
    public const string PatternAllowedChars = '~^[a-zA-Z0-9_.:-]{1,256}$~'; // https://regex101.com/r/TrBX3F/1

    /** @param Set<string> $values */
    public function __construct(
        public Set $values,
    ) {
    }

    public static function empty(): self
    {
        return new self(new Set());
    }
}
