<?php

declare(strict_types=1);

namespace Cdn77\Api\Job\Domain\Value;

use Cdn77\Api\Core\Domain\Value\Enums\EnumSchema;
use Cdn77\Api\Core\Domain\Value\Enums\EnumValues;
use cebe\openapi\exceptions\TypeErrorException;
use cebe\openapi\spec\Reference;
use cebe\openapi\spec\Schema;
use cebe\openapi\spec\Type;

enum JobState: string
{
    public const string Name = 'jobState';

    case Done = 'done';
    case Processing = 'processing';
    case Queued = 'queued';

    /** @throws TypeErrorException */
    public static function getSchemaSpec(): Schema
    {
        return new Schema([
            'description' => 'State of the request.',
            'enum' => EnumValues::get(self::class),
            'example' => self::Done->value,
            'type' => Type::STRING,
        ]);
    }

    /** @throws TypeErrorException */
    public static function getReference(): Reference
    {
        return EnumSchema::getReference(self::Name);
    }
}
