<?php

declare(strict_types=1);

namespace Cdn77\Api\Job\Domain\Command;

use Cdn77\Api\Core\Domain\Entity\Cdn\CdnId;
use Cdn77\Api\Core\Domain\Entity\Customer\CustomerId;
use Cdn77\Api\Core\Domain\Entity\DataManipulation\Job;
use Cdn77\Api\Core\Domain\Messaging\Command;
use Cdn77\Api\Job\Domain\Dto\PrefetchJob;
use Cdn77\Api\Job\Domain\Dto\PurgeJob;
use Cdn77\Api\Job\Domain\Value\JobType;
use Cdn77\Api\Job\Domain\Value\Paths;

/** @implements Command<Job, ScheduleJobHandler> */
final class ScheduleJob implements Command
{
    public function __construct(
        public CdnId $cdnId,
        public CustomerId $customerId,
        public JobType $type,
        public Paths $paths,
        public string|null $upstreamHost = null,
    ) {
    }

    public static function fromPrefetchJob(CdnId $cdnId, CustomerId $customerId, PrefetchJob $prefetchJob): self
    {
        return new self(
            $cdnId,
            $customerId,
            JobType::Prefetch,
            $prefetchJob->paths,
            $prefetchJob->upstreamHost,
        );
    }

    public static function fromPurgeJob(CdnId $cdnId, CustomerId $customerId, PurgeJob $purgeJob): self
    {
        return new self(
            $cdnId,
            $customerId,
            JobType::Purge,
            $purgeJob->paths,
        );
    }
}
