<?php

declare(strict_types=1);

namespace Cdn77\Api\Job\Domain\Command;

use Cdn77\Api\Core\Domain\Entity\Cdn\CdnId;
use Cdn77\Api\Core\Domain\Entity\Customer\CustomerId;
use Cdn77\Api\Core\Domain\Entity\DataManipulation\Job;
use Cdn77\Api\Core\Domain\Messaging\Command;
use Cdn77\Api\Job\Domain\Value\PurgeTags;

/** @implements Command<Job, SchedulePurgeTagJobHandler> */
final readonly class SchedulePurgeTagJob implements Command
{
    public function __construct(
        public CdnId $cdnId,
        public CustomerId $customerId,
        public PurgeTags $tags,
    ) {
    }
}
