<?php

declare(strict_types=1);

namespace Cdn77\Api\Job\Application\Payload;

use Cdn77\Api\Core\Application\OpenApi\Schema\DateTimeSchema;
use Cdn77\Api\Core\Application\OpenApi\Schema\UuidSchema;
use Cdn77\Api\Core\Domain\Entity\DataManipulation\PurgeAll;
use Cdn77\Api\Job\Domain\Value\JobState;
use Cdn77\Api\Job\Domain\Value\JobType;
use cebe\openapi\spec\Schema;
use cebe\openapi\spec\Type;
use Webmozart\Assert\Assert;

final class PurgeAllJobSchema extends JobBaseSchema
{
    public static function fromCommandBusResult(mixed $result): self
    {
        Assert::isInstanceOf($result, PurgeAll::class);

        return self::fromPurgeAll($result);
    }

    public static function getSchemaSpec(): Schema
    {
        return new Schema([
            'type' => Type::OBJECT,
            'properties' => [
                'id' => UuidSchema::spec('Job ID.'),
                'type' => JobType::getReference(), // :((
                'cdn' => CdnReference::getSchemaSpec(),
                'state' => JobState::getReference(),
                'queued_at' => DateTimeSchema::spec('Time of the request at queueing.'),
                'done_at' => DateTimeSchema::spec('Time of request at completion.'),
            ],
        ]);
    }

    private static function fromPurgeAll(PurgeAll $job): self
    {
        return new self(
            $job->getId(),
            $job->getType(),
            new CdnReference($job->getCdnId()),
            $job->getState(),
            $job->getScheduledAt(),
            $job->getCompletedAt(),
        );
    }
}
