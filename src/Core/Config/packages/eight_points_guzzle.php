<?php

declare(strict_types=1);

namespace Symfony\Component\DependencyInjection\Loader\Configurator;

use GuzzleHttp\Handler\CurlMultiHandler;
use GuzzleHttp\HandlerStack;

return static function (ContainerConfigurator $containerConfigurator): void {
    $parameters = $containerConfigurator->parameters();

    $parameters->set(
        'eight_points_guzzle.symfony_log_formatter.pattern',
        '>>>>>>>>{request}<<<<<<<<|>>>>>>>>{response}<<<<<<<<|{error}',
    );

    $containerConfigurator->extension('eight_points_guzzle', [
        'clients' => [
            'push_zone' => [
                'base_url' => 'https://place.holder/index.php',
                'options' => [
                    'connect_timeout' => 5,
                    'timeout' => 60,
                    'headers' => ['User-Agent' => '%env(string:HTTP_USER_AGENT)%'],
                ],
            ],
            'ara_api' => [
                'base_url' => '%env(ARA_CLIENT_URL)%',
                'options' => ['timeout' => 300],
            ],
        ],
    ]);

    $services = $containerConfigurator->services();

    $services->set(CurlMultiHandler::class);

    $services->set(HandlerStack::class)
        ->arg('$handler', service(CurlMultiHandler::class));

    $services->alias('GuzzleHttp\ClientInterface.push-zone', 'eight_points_guzzle.client.push_zone');

    $services->alias('api.push-zone', 'eight_points_guzzle.client.push_zone');

    $services->alias('api.ara', 'eight_points_guzzle.client.ara_api');
};
