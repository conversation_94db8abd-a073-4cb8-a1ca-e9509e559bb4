<?php

declare(strict_types=1);

namespace Symfony\Component\DependencyInjection\Loader\Configurator;

use Cdn77\Api\Tests\Utils\NoopPropertyAccessor;
use <PERSON>elmio\Alice\PropertyAccess\ReflectionPropertyAccessor;
use <PERSON>el<PERSON>\Alice\PropertyAccess\StdPropertyAccessor;

return static function (ContainerConfigurator $containerConfigurator): void {
    $services = $containerConfigurator->services();

    $services->defaults()
        ->autowire()
        ->autoconfigure();

    $services->alias('nelmio_alice.property_accessor', 'fixtures.reflection_property_accessor.chain');

    $services->alias('fixtures.reflection_property_accessor.chain', 'fixtures.reflection_property_accessor.chain.std');

    $services->set('fixtures.reflection_property_accessor.chain.std', StdPropertyAccessor::class)
        ->args([
            service('fixtures.reflection_property_accessor.chain.reflection'),
        ]);

    $services->set('fixtures.reflection_property_accessor.chain.reflection', ReflectionPropertyAccessor::class)
        ->args([
            service('fixtures.reflection_property_accessor.chain.noop'),
        ]);

    $services->set('fixtures.reflection_property_accessor.chain.noop', NoopPropertyAccessor::class);
};
