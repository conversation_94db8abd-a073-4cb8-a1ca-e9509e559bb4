<?php

declare(strict_types=1);

use Symfony\Component\DependencyInjection\Loader\Configurator\ContainerConfigurator;

return static function (ContainerConfigurator $containerConfigurator): void {
    $containerConfigurator->extension('doctrine_migrations', [
        'migrations_paths' => ['Cdn77\Api\Migrations' => '%kernel.project_dir%/migrations'],
        'em' => 'migrations',
        'storage' => [
            'table_storage' => ['table_name' => 'doctrine_migrations_versions'],
        ],
        'organize_migrations' => 'BY_YEAR_AND_MONTH',
        'custom_template' => '%kernel.project_dir%/migrations/migration.tpl',
    ]);
};
