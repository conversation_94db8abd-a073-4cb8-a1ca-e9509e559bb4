<?php

declare(strict_types=1);

use Symfony\Component\DependencyInjection\Loader\Configurator\ContainerConfigurator;

return static function (ContainerConfigurator $containerConfigurator): void {
    $containerConfigurator->extension('eight_points_guzzle', [
        'clients' => [
            'push_zone' => [
                'options' => [
                    'auth' => [
                        '%env(PUSH_ZONE_API_USERNAME)%',
                        '%env(PUSH_ZONE_API_PASSWORD)%',
                    ],
                ],
            ],
        ],
    ]);
};
