services:
    _defaults:
        autowire: true
        autoconfigure: true
        public: false

    Cdn77\Api\Customer\Domain\EmailAddressConfirmationLinkSender:
        arguments:
            $clientBaseUrl: '%env(CLIENT_BASE_URL)%'
            $hashSecret: '%env(PUBLIC_URL_HASH_SECRET)%'

    Cdn77\Api\Customer\Domain\Command\ConfirmEmailAddressHandler:
        arguments:
            $hashSecret: '%env(PUBLIC_URL_HASH_SECRET)%'

    Cdn77\Api\Customer\Domain\Command\AddCustomerFromInviteHandler:
        arguments:
            $signUpHashSecret: '%env(PUBLIC_URL_HASH_SECRET)%'

    Cdn77\Api\Customer\Domain\SignUpInviteSender:
        arguments:
            $hashSecret: '%env(PUBLIC_URL_HASH_SECRET)%'
