services:
    _defaults:
        autoconfigure: true
        autowire: true
        public: false

    Cdn77\Api\Customer\Domain\ApiPasswordEncryptor:
        arguments:
            $encryptionKey: '%env(PASSWORD_ENCRYPTION_KEY)%'

    Cdn77\Api\Customer\Domain\PasswordHasher:
        arguments:
            $cost: 13

    Cdn77\Api\Customer\Domain\TokenHasher:
        arguments:
            $cost: 13

    Cdn77\Api\Core\Application\Symfony\Authenticator\JwtAuthenticator:
        arguments:
            $jwtSecret: '%env(SIGN_IN_TOKEN_SECRET)%'
