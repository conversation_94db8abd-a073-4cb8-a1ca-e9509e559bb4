<?xml version="1.0" encoding="UTF-8" ?>
<container
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xmlns="http://symfony.com/schema/dic/services"
        xsi:schemaLocation="http://symfony.com/schema/dic/services
    https://symfony.com/schema/dic/services/services-1.0.xsd"
>
    <services>
        <defaults autowire="true" autoconfigure="true" public="false" />

        <service
                id="netcraftAuthentication"
                class="Cdn77\Api\Core\Infrastructure\HttpClient\Authentication\BearerTokenAuthentication"
        >
            <argument key="$token" type="string">%env(NETCRAFT_API_KEY)%</argument>
        </service>

        <service id="netcraftClient" class="Cdn77\Api\Core\Infrastructure\HttpClient\Client">
            <argument key="$host">%env(NETCRAFT_API_URL)%</argument>
            <argument key="$authentication" type="service" id="netcraftAuthentication" />
        </service>

        <service id="Cdn77\Api\Core\Infrastructure\Netcraft\HttpApi">
            <argument key="$client" type="service" id="netcraftClient" />
        </service>
    </services>
</container>
