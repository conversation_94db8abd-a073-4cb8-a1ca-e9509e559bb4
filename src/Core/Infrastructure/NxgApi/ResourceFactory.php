<?php

declare(strict_types=1);

namespace Cdn77\Api\Core\Infrastructure\NxgApi;

use Cdn77\Api\Cdn\Domain\Dto\QueryStringSettings;
use Cdn77\Api\Cdn\Domain\Dto\SecureToken;
use Cdn77\Api\Cdn\Domain\Value\CdnUrl;
use Cdn77\Api\Cdn\Domain\Value\HttpSettings;
use Cdn77\Api\Cdn\Domain\Value\Parameters\CustomHeader;
use Cdn77\Api\Cdn\Domain\Value\Parameters\FollowRedirect;
use Cdn77\Api\Core\Domain\Dto\Cname\Cnames;
use Cdn77\Api\Core\Domain\Dto\Origin\CdnOrigin;
use Cdn77\Api\Core\Domain\Dto\Origin\ObjectStorageOrigin;
use Cdn77\Api\Core\Domain\Dto\Origin\S3Origin;
use Cdn77\Api\Core\Domain\Encryption\ValueEncryptor;
use Cdn77\Api\Core\Domain\Entity\Cdn\Cdn;
use Cdn77\Api\Core\Domain\Entity\Cdn\CdnHttp;
use Cdn77\Api\Core\Domain\Entity\Cdn\CdnId;
use Cdn77\Api\Core\Domain\Entity\Customer\CustomerId;
use Cdn77\Api\Core\Domain\Entity\Datacenter\ServerGroupId;
use Cdn77\Api\Core\Domain\Value\ObjectStorage\BucketName;
use Cdn77\Api\Origin\Domain\Value\S3Credentials;
use Cdn77\Api\Origin\Domain\Value\S3Type;
use Cdn77\NxgApiClient\Cdn\VO\Cache;
use Cdn77\NxgApiClient\Cdn\VO\EditCdnResource;
use Cdn77\NxgApiClient\Cdn\VO\FollowRedirect as NxgFollowRedirect;
use Cdn77\NxgApiClient\Cdn\VO\Header;
use Cdn77\NxgApiClient\Cdn\VO\NewCdnResource;
use Cdn77\NxgApiClient\Cdn\VO\SecureToken as NxgSecureToken;
use Defuse\Crypto\Exception\CryptoException;
use Ds\Set;
use TypeError;

final readonly class ResourceFactory
{
    public function __construct(
        private ValueEncryptor $valueEncryptor,
    ) {
    }

    /**
     * @throws CryptoException
     * @throws TypeError
     */
    public function createNewResource(
        CdnId $cdnId,
        CustomerId $customerId,
        HttpSettings $httpSettings,
        Cnames $cnames,
        ServerGroupId $groupId,
        CdnOrigin $cdnOrigin,
    ): NewCdnResource {
        $s3Credentials = self::s3Credentials($cdnOrigin);
        $s3BucketName = self::s3BucketName($cdnOrigin);
        $s3Type = self::s3Type($cdnOrigin);

        return new NewCdnResource(
            $cdnId->toInt(),
            CdnUrl::fromCdnId($cdnId)->get(),
            $customerId->toInt(),
            $httpSettings->cacheSettings->maxAge->minutes,
            $httpSettings->cacheSettings->maxAge404->seconds,
            $cnames->toStrings(),
            [$groupId->toInt()],
            $cdnOrigin->baseDir->nxgValue(),
            $cdnOrigin->scheme->value,
            $cdnOrigin->url,
            $cdnOrigin->timeout ?? 0,
            $cdnOrigin->port->value ?? 0,
            $s3Credentials?->accessKeyId?->value,
            $s3Credentials?->secret?->value,
            $s3Credentials?->region?->value,
            $s3BucketName?->value,
            $s3Type?->value,
            $httpSettings->streamingPlaylistBypass->enabled,
            $httpSettings->rateLimit->isEnabled,
            $httpSettings->headersSettings->contentDisposition->isEnabledByParam(),
            $httpSettings->customOriginHeaders->toArray(),
            $this->createResponseHeaders($httpSettings->customResponseHeaders->headers),
            $this->createSecureToken($httpSettings->secureToken),
            $httpSettings->headersSettings->corsEnabled,
            $httpSettings->instantSslSettings->isEnabled,
            $httpSettings->queryStringSettings->isIgnoreAllEnabled(),
            $httpSettings->queryStringSettings->getParameters()->toArray(),
            $httpSettings->cacheSettings->requestsWithCookies->areIgnored(),
            $httpSettings->mp4PseudoStreamingSettings->isEnabled,
            $httpSettings->quicSettings->isEnabled,
            $httpSettings->wafSettings->isEnabled,
            self::createFollowRedirect($httpSettings->followRedirect),
            $cdnOrigin->ssl->verify->isDisabled(),
            $httpSettings->httpsRedirectSettings->code?->value,
            $httpSettings->headersSettings->hostForwardingEnabled,
        );
    }

    /**
     * @throws CryptoException
     * @throws TypeError
     */
    public function createEditedResource(
        Cdn $cdn,
        CdnHttp $cdnHttp,
        CdnOrigin $cdnOrigin,
        Cnames|null $cnames,
        QueryStringSettings|null $queryStringSettings,
    ): EditCdnResource {
        $s3Credentials = self::s3Credentials($cdnOrigin);
        $s3BucketName = self::s3BucketName($cdnOrigin);
        $s3Type = self::s3Type($cdnOrigin);

        return new EditCdnResource(
            $cdn->getId()->toInt(),
            $cdn->getOwner()->getId()->toInt(),
            $cdnOrigin->ssl->verify->isDisabled(),
            null,
            $cnames?->toStrings(),
            $cdnOrigin->url,
            $cdnOrigin->scheme->value,
            $cdnOrigin->port->value ?? 0,
            $cdnOrigin->timeout ?? 0,
            new Cache(
                $cdnHttp->maxAge()->minutes,
                $cdnHttp->maxAge404()->seconds,
                $cdnHttp->cacheContentLengthLimit()->value,
                $cdnHttp->cacheLockAge()->value?->toInt(),
                $cdnHttp->cacheLockTimeout()->value?->toInt(),
            ),
            $queryStringSettings?->isIgnoreAllEnabled(),
            $queryStringSettings?->getParameters()->toArray(),
            $cdnHttp->requestsWithCookies()->areIgnored(),
            ! $cdnHttp->hasMp4PseudoStreamingDisabled(),
            $this->createSecureToken($cdnHttp->secureToken()),
            ! $cdnHttp->hasInstantSslDisabled(),
            $cdnOrigin->baseDir->nxgValue(),
            $cdnHttp->hasWafEnabled(),
            $cdnHttp->hasQuicEnabled(),
            $cdnHttp->hasCorsOriginHeaderEnabled(),
            $cdnHttp->hasCorsTimingEnabled(),
            $cdnHttp->hasCorsWildcardEnabled(),
            $cdnHttp->httpRedirectSettings()->code->value ?? 0,
            $s3Credentials?->accessKeyId?->value,
            $s3Credentials?->secret?->value,
            $s3Credentials?->region?->value,
            $s3BucketName?->value,
            $s3Type?->value,
            $cdnHttp->hasStreamingPlaylistBypassEnabled(),
            $cdnHttp->hasForwardHostHeaderEnabled(),
            $cdnHttp->rateLimit()->isEnabled,
            $cdnHttp->contentDisposition()->isEnabledByParam(),
            $cdnHttp->customOriginHeaders()->toArray(),
            $this->createResponseHeaders($cdnHttp->customResponseHeaders()->headers),
            self::createFollowRedirect($cdnHttp->followRedirect()),
        );
    }

    private static function s3Credentials(CdnOrigin $cdnOrigin): S3Credentials|null
    {
        return match ($cdnOrigin::class) {
            ObjectStorageOrigin::class,
            S3Origin::class => $cdnOrigin->connection?->s3Credentials,
            default => null,
        };
    }

    private static function s3BucketName(CdnOrigin $cdnOrigin): BucketName|null
    {
        return match ($cdnOrigin::class) {
            ObjectStorageOrigin::class => $cdnOrigin->connection->bucketName,
            default => null,
        };
    }

    private static function s3Type(CdnOrigin $cdnOrigin): S3Type|null
    {
        return match ($cdnOrigin::class) {
            ObjectStorageOrigin::class => S3Type::Cdn77CephRgw,
            S3Origin::class => S3Type::ExternalS3,
            default => null,
        };
    }

    /**
     * @throws CryptoException
     * @throws TypeError
     */
    private function createSecureToken(SecureToken $secureToken): NxgSecureToken
    {
        return $secureToken->config === null
            ? new NxgSecureToken(
                $secureToken->type->value,
                $secureToken->token?->decrypt($this->valueEncryptor),
            ) : new NxgSecureToken(
                $secureToken->type->value,
                $secureToken->token?->decrypt($this->valueEncryptor),
                $secureToken->config->expiryParam,
                $secureToken->config->tokenParam,
                $secureToken->config->pathLengthParam,
                $secureToken->config->secretParam,
                $secureToken->config->rewritePlaylist,
            );
    }

    private static function createFollowRedirect(FollowRedirect $followRedirect): NxgFollowRedirect
    {
        return new NxgFollowRedirect(
            $followRedirect->isEnabled(),
            $followRedirect->codes(),
        );
    }

    /**
     * @param Set<CustomHeader> $headers
     *
     * @return list<Header>
     */
    private function createResponseHeaders(Set $headers): array
    {
        return $headers->map(
            static fn (CustomHeader $customHeader): Header => new Header($customHeader->name, $customHeader->value),
        )->toArray();
    }
}
