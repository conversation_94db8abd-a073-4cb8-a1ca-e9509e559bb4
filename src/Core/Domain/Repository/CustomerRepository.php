<?php

declare(strict_types=1);

namespace Cdn77\Api\Core\Domain\Repository;

use Cdn77\Api\Core\Domain\Entity\Customer\Customer;
use Cdn77\Api\Core\Domain\Entity\Customer\CustomerId;
use Cdn77\Api\Core\Domain\Entity\Customer\CustomerUuid;
use Cdn77\Api\Core\Domain\Entity\Customer\PublicCustomerId;
use Cdn77\Api\Core\Domain\Exception\Customer\CustomerNotFound;
use Cdn77\Api\Core\Domain\Value\Customer\EmailAddress;
use DateTimeImmutable;
use Ds\Set;

interface CustomerRepository
{
    public function find(CustomerId $id): Customer|null;

    /** @throws CustomerNotFound */
    public function get(CustomerId $id): Customer;

    public function remove(Customer $customer): void;

    public function findForEmail(EmailAddress $emailAddress): Customer|null;

    public function findForId(CustomerUuid $customerId): Customer|null;

    /**
     * @param Set<CustomerUuid> $customerIds
     *
     * @return Set<Customer>
     */
    public function findForIds(Set $customerIds): Set;

    /** @return array<Customer> */
    public function findTestingUntil(DateTimeImmutable $createdUntil): array;

    /** @throws CustomerNotFound */
    public function getForId(CustomerUuid $customerId): Customer;

    public function findForPublicId(PublicCustomerId $publicCustomerId): Customer|null;
}
