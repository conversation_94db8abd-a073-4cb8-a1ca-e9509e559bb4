<?php

declare(strict_types=1);

namespace Cdn77\Api\Core\Domain\Resolver;

use Cdn77\Api\Core\Domain\Entity\Customer\Customer;
use Cdn77\Api\Core\Domain\Exception\Customer\CustomerIsSuspended;
use Cdn77\Api\Core\Domain\Exception\Customer\CustomerIsTerminated;
use Cdn77\Api\Core\Domain\Repository\CustomerRepository;

final readonly class AffectedCustomerResolver
{
    public function __construct(private CustomerRepository $customerRepository)
    {
    }

    public function resolveActive(Customer $customer): Customer
    {
        $affectedCustomer = $this->resolve($customer);

        return $customer->isSuspended() || $affectedCustomer->isSuspended()
            ? throw CustomerIsSuspended::new()
            : $affectedCustomer;
    }

    public function resolveSuspended(Customer $customer): Customer
    {
        $affectedCustomer = $this->resolve($customer);

        return $customer->isTerminated() || $affectedCustomer->isTerminated()
            ? throw CustomerIsTerminated::new()
            : $affectedCustomer;
    }

    public function resolve(Customer $customer): Customer
    {
        return $customer->getParentId() === null
            ? $customer
            : $this->customerRepository->get($customer->getParentId());
    }
}
