<?php

declare(strict_types=1);

namespace Cdn77\Api\Core\Domain\JWT;

use Cdn77\Api\Core\Domain\Entity\Authentication\SessionId;
use Cdn77\Api\Core\Domain\Entity\Customer\CustomerId;
use Cdn77\Api\Core\Domain\Entity\Customer\CustomerUuid;
use Cdn77\Api\Core\Domain\Value\Customer\PlainPassword;
use Cdn77\Api\Core\Domain\Value\SignInToken;
use Cdn77\Api\Customer\Domain\Value\TokenType;
use DateTimeImmutable;
use Lcobucci\JWT\Configuration;
use Lcobucci\JWT\Exception;
use Lcobucci\JWT\Signer\Hmac\Sha256;
use L<PERSON><PERSON>cci\JWT\Signer\Key\InMemory;

use function Cdn77\Functions\absurd;

final readonly class SignInTokenGenerator
{
    public const string PermittedFor = 'https://cdn77.com';

    private Configuration $configuration;

    /**
     * @param non-empty-string $issuedBy
     * @param non-empty-string $tokenSecret
     */
    public function __construct(private string $issuedBy, string $tokenSecret)
    {
        $this->configuration = Configuration::forSymmetricSigner(new Sha256(), InMemory::plainText($tokenSecret));
    }

    public function generate(
        CustomerId $customerId,
        CustomerUuid $customerUuid,
        DateTimeImmutable $expiresAt,
        DateTimeImmutable $issuedAt,
        PlainPassword $sessionToken,
        SessionId $sessionId,
        TokenType $tokenType = TokenType::SignIn,
    ): SignInToken {
        try {
            $token = $this->configuration->builder()
                ->expiresAt($expiresAt)
                ->issuedBy($this->issuedBy)
                ->issuedAt($issuedAt)
                ->canOnlyBeUsedAfter($issuedAt)
                ->permittedFor(self::PermittedFor)
                ->withClaim(SignInToken::ClaimSessionId, $sessionId->toString())
                ->withClaim(SignInToken::ClaimSessionToken, $sessionToken->toString())
                ->withClaim(SignInToken::ClaimCustomerId, $customerId->toString())
                ->withClaim(SignInToken::ClaimCustomerUuid, $customerUuid->toString())
                ->withClaim(SignInToken::ClaimTokenType, $tokenType->value)
                ->getToken($this->configuration->signer(), $this->configuration->signingKey());
        } catch (Exception) {
            absurd();
        }

        return new SignInToken($token);
    }
}
