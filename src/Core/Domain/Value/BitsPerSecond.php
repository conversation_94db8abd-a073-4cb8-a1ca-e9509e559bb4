<?php

declare(strict_types=1);

namespace Cdn77\Api\Core\Domain\Value;

use Brick\Math\BigDecimal;
use Brick\Math\BigInteger;
use Brick\Math\Exception\MathException;
use Brick\Math\RoundingMode;
use Cdn77\Api\Core\Domain\Exception\Absurd;
use Cdn77\Api\Core\Domain\Exception\InvalidBpsValue;
use InvalidArgumentException;

final readonly class BitsPerSecond
{
    private const int GbpsInBps = 10 ** 9;

    /** @throws InvalidBpsValue */
    private function __construct(
        public int $value,
    ) {
        if ($value < 0) {
            throw InvalidBpsValue::negative($value);
        }
    }

    /** @throws InvalidBpsValue */
    public static function create(int $value): self
    {
        return new self($value);
    }

    public static function zero(): self
    {
        try {
            return new self(0);
        } catch (InvalidBpsValue $exception) {
            throw Absurd::fromThrowable($exception);
        }
    }

    /**
     * @throws InvalidBpsValue
     * @throws MathException
     */
    public static function fromBigInt(BigInteger $value): self
    {
        return new self($value->toInt());
    }

    /**
     * @throws MathException
     * @throws InvalidArgumentException
     */
    public function toGbps(int $scale = 0): BigDecimal
    {
        return BigDecimal::of($this->value)
            ->dividedBy(self::GbpsInBps, $scale, RoundingMode::HALF_UP);
    }
}
