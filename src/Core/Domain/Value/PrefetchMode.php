<?php

declare(strict_types=1);

namespace Cdn77\Api\Core\Domain\Value;

enum PrefetchMode: string
{
    case Enabled = 'enabled'; // visible and functional
    case Disabled = 'disabled'; // visible but sent to a black hole
    case Hidden = 'hidden'; // not visible, not functional, API returning 404, default for new customers

    public function isEnabled(): bool
    {
        return $this === self::Enabled;
    }

    public function isVisible(): bool
    {
        return match ($this) {
            self::Enabled, self::Disabled => true,
            self::Hidden => false,
        };
    }
}
