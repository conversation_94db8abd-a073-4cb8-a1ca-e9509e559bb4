<?php

declare(strict_types=1);

namespace Cdn77\Api\Core\Domain\Value\ObjectStorage;

use Cdn77\Api\Core\Domain\Entity\Customer\CustomerUuid;
use Cdn77\Api\Core\Domain\Entity\ObjectStorage\AccessKeyId;
use Cdn77\Api\Core\Domain\Entity\Origin\OriginId;
use Cdn77\ValueObject\Cache\Cache;
use Cdn77\ValueObject\Cache\HasCache;
use Ds\Set;
use Webmozart\Assert\Assert;

use function is_array;
use function sprintf;
use function str_replace;

final readonly class ObjectStorageUserName implements HasCache
{
    use Cache;

    private const string ObjectStorageUserPrefix = 'cdn77';
    private const string RealTimeLogsUserPrefix = 'cdn77-logs';

    private function __construct(public string $value)
    {
    }

    /** @param array<string, mixed> $data */
    public static function fromArray(array $data): self
    {
        Assert::keyExists($data, 'AWS');
        /** @var Set<string> $aws */
        $aws = is_array($data['AWS']) ? new Set($data['AWS']) : new Set([$data['AWS']]);
        $users = $aws->map(
            static fn (string $iam) => str_replace('arn:aws:iam:::user/', '', $iam),
        );
        Assert::minCount($users, 1);

        return self::cached($users->toArray()[0]);
    }

    public static function forBucketName(string $bucketName): self
    {
        return self::cached(sprintf('%s-%s', self::ObjectStorageUserPrefix, $bucketName));
    }

    public static function forRealTimeLog(OriginId $bucketId): self
    {
        return self::cached(sprintf('%s-%s', self::RealTimeLogsUserPrefix, $bucketId->toString()));
    }

    public static function forAccessKeyId(AccessKeyId $accessKeyId): self
    {
        return self::cached($accessKeyId->toString());
    }

    public static function fromString(string $userName): self
    {
        return self::cached($userName);
    }

    public static function fromCustomerId(CustomerUuid $customerId): self
    {
        return self::cached($customerId->toString());
    }
}
