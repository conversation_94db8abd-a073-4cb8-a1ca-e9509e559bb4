<?php

declare(strict_types=1);

namespace Cdn77\Api\Core\Domain\Value\Customer;

use Cdn77\Api\Inquiry\Domain\Value\Department;
use Ds\Hashable;
use Stringable;
use Webmozart\Assert\Assert;

use function Psl\Regex\matches;
use function sprintf;
use function strtolower;

final readonly class EmailAddress implements Hashable, Stringable
{
    public const string SignatureTravis = 'Travis from CDN77';

    public const string PatternTestEmailLikeQuery = '<EMAIL>';

    private const string PatternCdnEmail = '~^.*@cdn77\.com$~';

    private const string EmailAutomated = '<EMAIL>';
    private const string EmailClientSolutions = '<EMAIL>';
    private const string EmailSales = '<EMAIL>';
    private const string EmailSalesInternal = '<EMAIL>';
    private const string EmailSupport = '<EMAIL>';
    private const string EmailTech = '<EMAIL>';

    /** @param non-empty-string $emailAddress */
    private function __construct(private string $emailAddress, private string|null $displayName = null)
    {
    }

    public static function fromString(string $emailAddress): self
    {
        Assert::email($emailAddress);

        return new self($emailAddress);
    }

    public static function internal(string|null $displayName = null): self
    {
        return new self(self::EmailAutomated, $displayName ?? 'CDN77 Team');
    }

    public static function sales(): self
    {
        return new self(self::EmailSales, 'CDN77 Sales');
    }

    public static function salesInternal(): self
    {
        return new self(self::EmailSalesInternal, 'CDN77 Sales Internal');
    }

    public static function clientSolutions(string|null $displayName = null): self
    {
        return new self(self::EmailClientSolutions, $displayName ?? 'CDN77 Client Solutions');
    }

    public static function support(string|null $displayName = null): self
    {
        return new self(self::EmailSupport, $displayName ?? 'CDN77 Support');
    }

    public static function tech(): self
    {
        return new self(self::EmailTech, 'CDN77 Tech');
    }

    public static function getForDepartment(Department $department): self
    {
        return match ($department) {
            Department::Support => self::support(),
            Department::Sales => self::sales(),
            Department::Tech => self::tech(),
        };
    }

    public function __toString(): string
    {
        return $this->emailAddress;
    }

    public function getWithName(): string
    {
        return $this->displayName === null ?
            $this->emailAddress :
            sprintf('%s <%s>', $this->displayName, $this->emailAddress);
    }

    /** @return non-empty-string */
    public function get(): string
    {
        return $this->emailAddress;
    }

    public function toLowerCase(): string
    {
        return strtolower($this->emailAddress);
    }

    public function equals(mixed $obj): bool
    {
        return $obj instanceof self && $this->toLowerCase() === $obj->toLowerCase();
    }

    public function hash(): string
    {
        return $this->toLowerCase();
    }

    public function isCdnEmail(): bool
    {
        return matches($this->emailAddress, self::PatternCdnEmail);
    }
}
