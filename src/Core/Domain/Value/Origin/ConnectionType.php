<?php

declare(strict_types=1);

namespace Cdn77\Api\Core\Domain\Value\Origin;

use Cdn77\Api\Core\Domain\Value\Enums\EnumValues;
use cebe\openapi\exceptions\TypeErrorException;
use cebe\openapi\spec\Reference;
use cebe\openapi\spec\Schema;
use cebe\openapi\spec\Type;

use function Psl\Str\join;

enum ConnectionType: string
{
    public const string Name = 'connectionType';

    case Aws = 'aws';
    case ObjectStorage = 'object-storage';
    case Storage = 'storage';
    case Url = 'url';

    /** @throws TypeErrorException */
    public static function getSchemaSpec(): Schema
    {
        $enumValues = EnumValues::get(self::class);

        return new Schema([
            'type' => Type::STRING,
            'enum' => $enumValues,
            //phpcs:ignore Generic.PHP.ForbiddenFunctions.FoundWithAlternative
            'example' => join($enumValues, '|'),
        ]);
    }

    /** @throws TypeErrorException */
    public static function getReference(): Reference
    {
        return new Reference(['$ref' => '#/components/schemas/' . self::Name]);
    }
}
