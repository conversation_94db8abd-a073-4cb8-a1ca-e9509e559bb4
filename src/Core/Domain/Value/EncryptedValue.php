<?php

declare(strict_types=1);

namespace Cdn77\Api\Core\Domain\Value;

use Cdn77\Api\Core\Domain\Encryption\ValueEncryptor;
use Defuse\Crypto\Exception\CryptoException;
use TypeError;

final readonly class EncryptedValue
{
    public function __construct(
        public string $value,
    ) {
    }

    /**
     * @throws CryptoException
     * @throws TypeError
     */
    public function decrypt(ValueEncryptor $valueEncryptor): string
    {
        return $valueEncryptor->decrypt($this);
    }
}
