<?php

declare(strict_types=1);

namespace Cdn77\Api\Core\Domain\Ceph;

use Cdn77\Api\Core\Domain\Entity\Origin\Origin;
use Cdn77\Api\Core\Domain\Exception\ObjectStorage\ObjectStorageUserNotFound;
use Cdn77\Api\Core\Domain\Exception\RgwClusterNotFound;
use Cdn77\Api\Core\Domain\Repository\ObjectStorage\RgwClusterRepository;
use Cdn77\Api\Core\Domain\Value\ObjectStorage\ObjectStorageCredentials;
use Cdn77\Api\Core\Domain\Value\ObjectStorage\ObjectStorageUserName;

final readonly class CredentialsResolver
{
    public function __construct(
        private AdminApi $adminApi,
        private RgwClusterRepository $rgwClusterRepository,
    ) {
    }

    /**
     * @throws ObjectStorageUserNotFound
     * @throws RgwClusterNotFound
     */
    public function resolveMasterForOrigin(Origin $origin): ObjectStorageCredentials
    {
        $cluster = $this->rgwClusterRepository->getForHost($origin->getHost()->value);

        return $this->adminApi->getUserCredentials(
            $cluster,
            ObjectStorageUserName::fromCustomerId($origin->getCustomerId()),
        );
    }
}
