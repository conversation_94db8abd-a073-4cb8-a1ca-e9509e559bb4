<?php

declare(strict_types=1);

namespace Cdn77\Api\Core\Domain\Ceph;

use Cdn77\Api\Core\Domain\Ceph\Exception\BucketNotFound;
use Cdn77\Api\Core\Domain\Exception\ObjectStorage\PolicyNotFound;
use Cdn77\Api\Core\Domain\Value\ObjectStorage\BucketName;
use Cdn77\Api\Core\Domain\Value\ObjectStorage\ObjectStorageCredentials;
use Cdn77\Api\ObjectStorage\Domain\Value\BucketLifecycle\BucketLifecycleConfiguration;
use Cdn77\Api\ObjectStorage\Domain\Value\Policy;
use Ds\Set;
use Psl\Json\Exception\DecodeException;

interface ClientApi
{
    /**
     * @return Set<Policy>
     *
     * @throws DecodeException
     * @throws PolicyNotFound
     */
    public function getBucketPolicy(ObjectStorageCredentials $userCredentials, string $bucketName): Set;

    /**
     * @param Set<Policy> $policies
     *
     * @throws BucketNotFound
     */
    public function putBucketPolicy(ObjectStorageCredentials $userCredentials, string $bucketName, Set $policies): void;

    /** @throws BucketNotFound */
    public function deleteBucketPolicy(ObjectStorageCredentials $userCredentials, string $bucketName): void;

    /** @throws BucketNotFound */
    public function getLifecycleConfiguration(
        ObjectStorageCredentials $credentials,
        BucketName $bucketName,
    ): BucketLifecycleConfiguration;

    /** @throws BucketNotFound */
    public function putLifecycleConfiguration(
        ObjectStorageCredentials $credentials,
        BucketName $bucketName,
        BucketLifecycleConfiguration $lifecycleConfiguration,
    ): void;

    /** @throws BucketNotFound */
    public function deleteBucketLifecycle(
        ObjectStorageCredentials $credentials,
        BucketName $bucketName,
    ): void;

    public function deleteBucket(ObjectStorageCredentials $credentials, string $s3BucketName): void;
}
