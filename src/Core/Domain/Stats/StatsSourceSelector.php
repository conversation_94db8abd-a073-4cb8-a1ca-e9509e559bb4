<?php

declare(strict_types=1);

namespace Cdn77\Api\Core\Domain\Stats;

use Cdn77\Api\Core\Domain\Dto\TimeRange;
use Cdn77\Api\Core\Domain\Entity\Customer\Customer;
use Cdn77\Api\Core\Domain\Entity\Customer\CustomerId;
use Cdn77\Api\Core\Domain\Value\AggregationUnit;
use Cdn77\Api\Stats\Domain\Value\Aggregation;
use Cdn77\Api\Stats\Domain\Value\DataSource;
use Cdn77\Api\Stats\Domain\Value\StatsType;
use DateTimeImmutable;

class StatsSourceSelector
{
    private const string ClickhouseHasDataFrom = '2022-05-06';

    public function __construct(private readonly bool $avoidClickHouse)
    {
    }

    public function getDataSource(
        DataSource|null $preferredDataSource,
        Aggregation $aggregation,
        StatsType $type,
        TimeRange $timeRange,
        Customer $customer,
        DateTimeImmutable $now,
    ): DataSource {
        if ($customer->getId()->equals(CustomerId::tikTokStreaming())) {
            return DataSource::ClickHouseLiveStreaming;
        }

        if ($preferredDataSource !== null) {
            return $preferredDataSource;
        }

        if ($type->isCosts() || $this->avoidClickHouse) {
            return DataSource::Ara;
        }

        $limitDate = match ($aggregation->getUnit()) {
            AggregationUnit::Minute,
            AggregationUnit::Hour => $now->modify('1 year ago'),
            AggregationUnit::Day,
            AggregationUnit::Month => $now->modify('5 years ago'),
        };

        if (
            $timeRange->from < $limitDate
            || $timeRange->from < new DateTimeImmutable(self::ClickhouseHasDataFrom)
        ) {
            return DataSource::Ara;
        }

        // Use ARA for specific accounts
        if ($customer->isTikTokAccount()) {
            return DataSource::Ara;
        }

        return DataSource::ClickHouse;
    }
}
