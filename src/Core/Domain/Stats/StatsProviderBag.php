<?php

declare(strict_types=1);

namespace Cdn77\Api\Core\Domain\Stats;

use Cdn77\Api\Core\Infrastructure\Ara\Provider\AraStatsProvider;
use Cdn77\Api\Core\Infrastructure\ClickHouse\Provider\ClickHouseLiveStreamingStatsProvider;
use Cdn77\Api\Core\Infrastructure\ClickHouse\Provider\ClickHouseStatsProvider;
use Cdn77\Api\Stats\Domain\Value\DataSource;
use Ds\Map;
use Ds\Pair;

use function Cdn77\Functions\mapFromIterable;

final readonly class StatsProviderBag
{
    /** @var Map<DataSource, StatsProvider> $providers */
    private Map $providers;

    public function __construct(
        AraStatsProvider $araStatsProvider,
        ClickHouseStatsProvider $clickHouseStatsProvider,
        ClickHouseLiveStreamingStatsProvider $clickHouseLiveStreamingStatsProvider,
    ) {
        /** @var Map<DataSource, StatsProvider> $providers */
        $providers = mapFromIterable(
            [$araStatsProvider, $clickHouseStatsProvider, $clickHouseLiveStreamingStatsProvider],
            static fn (mixed $_, StatsProvider $statsProvider): Pair => new Pair(
                $statsProvider::dataSource(),
                $statsProvider,
            ),
        );

        $this->providers = $providers;
    }

    public function get(DataSource $dataSource): StatsProvider
    {
        return $this->providers->get($dataSource);
    }
}
