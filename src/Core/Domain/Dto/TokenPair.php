<?php

declare(strict_types=1);

namespace Cdn77\Api\Core\Domain\Dto;

use DateTimeImmutable;
use Facile\OpenIDClient\Token\TokenSetInterface;
use Webmozart\Assert\Assert;

use function sprintf;

final readonly class TokenPair
{
    public function __construct(
        public string $accessToken,
        public string $refreshToken,
        public DateTimeImmutable $expireAt,
    ) {
    }

    public static function fromTokenSet(TokenSetInterface $token, DateTimeImmutable $now): self
    {
        $accessToken = $token->getAccessToken();
        $refreshToken = $token->getRefreshToken();
        $expireInSec = $token->getExpiresIn();

        Assert::string($accessToken);
        Assert::string($refreshToken);
        Assert::integer($expireInSec);

        return new self($accessToken, $refreshToken, $now->modify(sprintf('+%d seconds', $expireInSec)));
    }
}
