<?php

declare(strict_types=1);

namespace Cdn77\Api\Core\Domain\Entity\Datacenter;

use Cdn77\Api\Core\Domain\Value\Identifier\UuidIdentifier;

final class RegionId extends UuidIdentifier
{
    private const string DsaLegacyRegionId = 'f952afa2-e12c-4b4f-9113-ea68c2957d1f';
    private const string DsaRegionId = '2e1e264c-aa5d-4807-8133-876e946ceb3c';
    private const string Latam1RegionId = 'acef7e22-3226-4d30-89b9-************';
    private const string Latam2RegionId = 'b3f48e2d-373c-49c5-bebf-23866e2da443';
    private const string UsRegionId = '51ef3669-b1b4-452e-af63-106298d94acc';

    public static function latam1(): self
    {
        return self::fromString(self::Latam1RegionId);
    }

    public static function latam2(): self
    {
        return self::fromString(self::Latam2RegionId);
    }

    public static function us(): self
    {
        return self::fromString(self::UsRegionId);
    }

    public function isDsa(): bool
    {
        return match ($this->toString()) {
            self::DsaRegionId, self::DsaLegacyRegionId => true,
            default => false
        };
    }
}
