<?php

declare(strict_types=1);

namespace Cdn77\Api\Core\Domain\Entity\Datacenter;

use Cdn77\Api\Core\Domain\Entity\Customer\CustomerUuid;
use Cdn77\Api\Stats\Domain\Value\RegionName;
use Cdn77\Api\Stats\Domain\Value\RegionRate;
use DateTimeImmutable;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Bridge\Doctrine\Types\UuidType;
use Symfony\Component\Uid\Uuid;

#[ORM\Entity]
#[ORM\Table(name: 'regions', schema: 'pricing')]
class Region
{
    #[ORM\Id]
    #[ORM\Column(type: UuidType::NAME)]
    private Uuid $id;

    #[ORM\Column(name: 'ts_from', type: 'datetime_immutable')]
    private DateTimeImmutable $activeFrom;

    #[ORM\Column(name: 'ts_to', type: 'datetime_immutable', nullable: true)]
    private DateTimeImmutable|null $activeTo = null;

    #[ORM\Column(type: 'string')]
    private string $name;

    #[ORM\Column(type: 'float')]
    private float $price;

    #[ORM\Column(type: 'integer')]
    private int $weight;

    #[ORM\Column(type: UuidType::NAME)]
    private Uuid $customerId;

    private function __construct(
        DateTimeImmutable $activeFrom,
        DateTimeImmutable|null $activeTo,
        RegionName $name,
        RegionRate $price,
        int $weight,
        CustomerUuid $customerId,
    ) {
        $this->id = RegionId::new()->toUid();
        $this->activeFrom = $activeFrom;
        $this->activeTo = $activeTo;
        $this->name = $name->get();
        $this->price = $price->get();
        $this->weight = $weight;
        $this->customerId = $customerId->toUid();
    }

    public function getId(): RegionId
    {
        return RegionId::fromUuid($this->id);
    }

    public function getName(): RegionName
    {
        return new RegionName($this->name);
    }

    public function getPrice(): RegionRate
    {
        return new RegionRate($this->price);
    }

    public function getCustomerId(): CustomerUuid
    {
        return CustomerUuid::fromUuid($this->customerId);
    }
}
