<?php

declare(strict_types=1);

namespace Cdn77\Api\Core\Domain\Entity\Plan;

use Brick\Money\RationalMoney;
use Cdn77\Api\Core\Domain\Entity\Datacenter\Location;
use Cdn77\Api\CoreLibrary\Money\CurrencyCode;
use DateTimeImmutable;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity]
#[ORM\Table(schema: 'pricing', name: 'plans_locations')]
class PlanLocation
{
    #[ORM\Id]
    #[ORM\Column(type: 'integer')]
    #[ORM\GeneratedValue(strategy: 'SEQUENCE')]
    #[ORM\SequenceGenerator(sequenceName: 'pricing.plans_locations_id_seq')]
    private int $id;

    #[ORM\ManyToOne(targetEntity: Plan::class)]
    #[ORM\JoinColumn(name: 'fk_plans_id', referencedColumnName: 'id', nullable: false)]
    private Plan $plan;

    #[ORM\Column(name: 'ts_from', type: 'datetime_immutable')]
    private DateTimeImmutable $validFrom;

    #[ORM\Column(type: 'float')]
    private float $price;

    #[ORM\ManyToOne(targetEntity: Location::class)]
    #[ORM\JoinColumn(name: 'fk_locations_id', referencedColumnName: 'id', nullable: false)]
    private Location $location;

    private function __construct(Plan $plan, DateTimeImmutable $validFrom, RationalMoney $price, Location $location)
    {
        $this->plan = $plan;
        $this->validFrom = $validFrom;
        $this->price = $price->getAmount()->toFloat();
        $this->location = $location;
    }

    public static function newFromPlan(Plan $plan, Location $location, RationalMoney $price): self
    {
        return new self(
            $plan,
            $plan->getValidFrom(),
            $price,
            $location,
        );
    }

    public function getPlan(): Plan
    {
        return $this->plan;
    }

    public function pricePerTiB(): RationalMoney
    {
        return RationalMoney::of($this->price, CurrencyCode::USD->toCurrency());
    }

    public function getLocation(): Location
    {
        return $this->location;
    }
}
