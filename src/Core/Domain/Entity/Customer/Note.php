<?php

declare(strict_types=1);

namespace Cdn77\Api\Core\Domain\Entity\Customer;

use DateTimeImmutable;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Bridge\Doctrine\Types\UuidType;
use Symfony\Component\Uid\Uuid;

#[ORM\Entity]
#[ORM\Table(name: Note::TableName)]
class Note
{
    public const string FieldCreatedAt = 'createdAt';
    public const string FieldEditorId = 'editorId';
    public const string FieldMessage = 'message';

    public const string TableName = 'account_comments';

    #[ORM\Id]
    #[ORM\Column(type: 'integer')]
    #[ORM\GeneratedValue(strategy: 'SEQUENCE')]
    #[ORM\SequenceGenerator(sequenceName: 'account_comments_id_seq', allocationSize: 1, initialValue: 1)]
    protected int $id;

    #[ORM\Column(type: 'text')]
    protected string $message;

    #[ORM\Column(type: 'integer', name: 'account_id')]
    protected int $customerId;

    #[ORM\Column(type: Types::DATETIME_IMMUTABLE, name: 'commented', nullable: true)]
    protected DateTimeImmutable|null $createdAt = null;

    #[ORM\Column(type: 'integer', name: 'account_id_editor', nullable: true)]
    protected int|null $editorId = null;

    #[ORM\Column(name: 'pinned', type: Types::BOOLEAN)]
    protected bool $pinned = false;

    #[ORM\Column(type: UuidType::NAME)]
    protected Uuid $uuid;

    public function __construct(
        NoteId $id,
        CustomerId $customerId,
        string $message,
        DateTimeImmutable $createdAt,
        CustomerId|null $editorId,
        bool $isPinned,
    ) {
        $this->customerId = $customerId->toInt();
        $this->message = $message;
        $this->createdAt = $createdAt;
        $this->editorId = $editorId?->toInt();
        $this->uuid = $id->get();
        $this->pinned = $isPinned;
    }

    public function id(): Uuid
    {
        return $this->uuid;
    }

    public function updateMessage(string $message): void
    {
        $this->message = $message;
    }

    public function pin(): void
    {
        $this->pinned = true;
    }

    public function unpin(): void
    {
        $this->pinned = false;
    }
}
