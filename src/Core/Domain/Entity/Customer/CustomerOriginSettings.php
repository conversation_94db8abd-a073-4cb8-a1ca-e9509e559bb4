<?php

declare(strict_types=1);

namespace Cdn77\Api\Core\Domain\Entity\Customer;

use Cdn77\Api\Core\Domain\Entity\Origin\OriginId;
use Cdn77\Api\Core\Domain\Value\ObjectStorage\ObjectStorageUserName;
use DateTimeImmutable;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Bridge\Doctrine\Types\UuidType;
use Symfony\Component\Uid\Uuid;

#[ORM\Entity]
#[ORM\Table(name: 'account_origin_settings')]
class CustomerOriginSettings
{
    public const string FieldMaxOrigins = 'maxOrigins';
    public const string FieldDefaultRealTimeLogOriginId = 'defaultRealTimeLogOriginId';

    #[ORM\Id]
    #[ORM\Column(type: UuidType::NAME)]
    protected Uuid $id;

    #[ORM\Column(type: UuidType::NAME)]
    protected Uuid $customerId;

    #[ORM\Column(type: Types::INTEGER)]
    protected int $maxAccessKeysPerCluster = 10;

    #[ORM\Column(type: Types::DATETIME_IMMUTABLE, nullable: true)]
    protected DateTimeImmutable|null $objectStorageTrialValidTo = null;

    #[ORM\Column(type: Types::INTEGER)]
    protected int $maxOrigins = 100;

    #[ORM\Column(name: 'default_real_time_log_origin_id', type: UuidType::NAME, nullable: true)]
    protected Uuid|null $defaultRealTimeLogOriginId = null;

    #[ORM\Column(name: 'default_real_time_log_access_key_id', type: Types::STRING, nullable: true)]
    protected string|null $defaultRealTimeLogUserName = null;

    public function __construct(CustomerUuid $customerId, int $maxOrigins = 100)
    {
        $this->id = Uuid::v4();
        $this->customerId = $customerId->toUid();
        $this->maxOrigins = $maxOrigins;
    }

    public function maxAccessKeysPerCluster(): int
    {
        return $this->maxAccessKeysPerCluster;
    }

    public function objectStorageTrialValidTo(): DateTimeImmutable|null
    {
        return $this->objectStorageTrialValidTo;
    }

    public function activeOriginCountQuota(): int
    {
        return $this->maxOrigins;
    }

    public function changeDefaultRealTimeLogObjectStorage(OriginId $originId): void
    {
        $this->defaultRealTimeLogOriginId = $originId->toUid();
    }

    public function unsetDefaultRealTimeLogObjectStorageId(): void
    {
        $this->defaultRealTimeLogOriginId = null;
    }

    public function unsetDefaultRealTimeLogUserName(): void
    {
        $this->defaultRealTimeLogUserName = null;
    }

    public function changeDefaultRealTimeLogUserName(ObjectStorageUserName $userName): void
    {
        if ($this->defaultRealTimeLogUserName === $userName->value) {
            return;
        }

        $this->defaultRealTimeLogUserName = $userName->value;
    }

    public function defaultRealTimeLogObjectStorageId(): OriginId|null
    {
        return $this->defaultRealTimeLogOriginId === null
            ? null
            : OriginId::fromUuid($this->defaultRealTimeLogOriginId);
    }

    public function defaultRealTimeLogUserName(): ObjectStorageUserName|null
    {
        return $this->defaultRealTimeLogUserName === null
            ? null
            : ObjectStorageUserName::fromString($this->defaultRealTimeLogUserName);
    }
}
