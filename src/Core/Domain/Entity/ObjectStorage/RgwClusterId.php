<?php

declare(strict_types=1);

namespace Cdn77\Api\Core\Domain\Entity\ObjectStorage;

use Cdn77\Api\Core\Domain\Value\Identifier\UuidIdentifier;

final class RgwClusterId extends UuidIdentifier
{
    public const string EU1 = '842b5641-b641-4723-ac81-f8cc286e288f';
    public const string EU3 = 'c9725385-e5af-4938-a94a-1c55c1a20eaa';
    public const string US1 = '4427a875-8111-4901-be4d-9bcbbf6b9b47';

    public static function EU1(): self
    {
        return self::fromString(self::EU1);
    }

    public static function EU3(): self
    {
        return self::fromString(self::EU3);
    }

    public static function US1(): self
    {
        return self::fromString(self::US1);
    }
}
