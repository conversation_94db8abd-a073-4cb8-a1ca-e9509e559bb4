<?php

declare(strict_types=1);

namespace Cdn77\Api\Core\Domain\Entity\ObjectStorage;

use Cdn77\Api\Core\Domain\Entity\Customer\CustomerUuid;
use Cdn77\Api\Core\Domain\Value\Label;
use Cdn77\Api\Core\Domain\Value\Note;
use Cdn77\Api\Core\Domain\Value\ObjectStorage\ObjectStorageUserName;
use Cdn77\Api\Origin\Domain\Value\S3AccessKey;
use DateTimeImmutable;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Bridge\Doctrine\Types\UuidType;
use Symfony\Component\Uid\Uuid;
use Webmozart\Assert\Assert;
use Webmozart\Assert\InvalidArgumentException;

use function assert;
use function is_string;

#[ORM\Entity]
#[ORM\Table(name: 'users', schema: 'storages')]
class AccessKey
{
    public const string FieldAccessKeyId = 'accessKeyId';
    public const string FieldClusterId = 'clusterId';
    public const string FieldCreatedAt = 'createdAt';
    public const string FieldCreatedBy = 'createdBy';
    public const string FieldCustomerId = 'customerId';
    public const string FieldId = 'id';
    public const string FieldLabel = 'label';
    public const string FieldNote = 'note';

    private const string LabelMaster = 'Master';

    #[ORM\Id]
    #[ORM\Column(type: UuidType::NAME)]
    protected Uuid $id;

    #[ORM\Column(type: Types::STRING)]
    protected string $accessKeyId;

    #[ORM\Column(type: Types::DATETIME_IMMUTABLE)]
    protected DateTimeImmutable $createdAt;

    #[ORM\Column(type: UuidType::NAME)]
    protected Uuid $clusterId;

    #[ORM\Column(type: UuidType::NAME)]
    protected Uuid $customerId;

    #[ORM\Column(type: UuidType::NAME)]
    protected Uuid $createdBy;

    #[ORM\Column(type: Types::STRING)]
    protected string $label;

    #[ORM\Column(type: Types::TEXT, nullable: true)]
    protected string|null $note;

    public function __construct(
        AccessKeyId $id,
        S3AccessKey $s3AccessKey,
        DateTimeImmutable $createdAt,
        RgwClusterId $clusterId,
        CustomerUuid $customerId,
        CustomerUuid $createdBy,
        Label $label,
        Note $note,
    ) {
        assert(is_string($s3AccessKey->value));
        $this->id = $id->toUid();
        $this->accessKeyId = $s3AccessKey->value;
        $this->customerId = $customerId->toUid();
        $this->createdBy = $createdBy->toUid();
        $this->clusterId = $clusterId->toUid();
        $this->createdAt = $createdAt;
        $this->label = $label->value;
        $this->note = $note->get();
    }

    public static function master(
        CustomerUuid $customerId,
        RgwClusterId $clusterId,
        S3AccessKey $s3AccessKey,
        DateTimeImmutable $now,
    ): self {
        return new self(
            AccessKeyId::new(),
            $s3AccessKey,
            $now,
            $clusterId,
            $customerId,
            $customerId,
            Label::fromString(self::LabelMaster),
            Note::empty(),
        );
    }

    public function id(): AccessKeyId
    {
        return AccessKeyId::fromUuid($this->id);
    }

    public function label(): Label
    {
        return Label::fromString($this->label);
    }

    public function accessKey(): S3AccessKey
    {
        return new S3AccessKey($this->accessKeyId);
    }

    public function createdAt(): DateTimeImmutable
    {
        return $this->createdAt;
    }

    public function createdBy(): CustomerUuid
    {
        return CustomerUuid::fromUuid($this->createdBy);
    }

    public function clusterId(): RgwClusterId
    {
        return RgwClusterId::fromUuid($this->clusterId);
    }

    public function customerId(): CustomerUuid
    {
        return CustomerUuid::fromUuid($this->customerId);
    }

    public function note(): Note
    {
        return Note::fromNullableString($this->note);
    }

    public function isMaster(): bool
    {
        return $this->label === self::LabelMaster;
    }

    public function userName(): ObjectStorageUserName
    {
        return $this->isMaster()
            ? ObjectStorageUserName::fromCustomerId($this->customerId())
            : ObjectStorageUserName::fromString($this->id()->toString());
    }

    /** @throws InvalidArgumentException */
    public function refreshKey(S3AccessKey $key): void
    {
        Assert::notNull($key->value);
        $this->accessKeyId = $key->value;
    }
}
