<?php

declare(strict_types=1);

namespace Cdn77\Api\Core\Domain\Entity\Cdn;

use DateTimeImmutable;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity]
#[ORM\Table(name: 'ignored_query_params')]
class IgnoredQueryParameter
{
    public const string FieldId = 'id';
    public const string FieldName = 'name';

    #[ORM\Id]
    #[ORM\Column(type: 'integer')]
    #[ORM\GeneratedValue(strategy: 'SEQUENCE')]
    #[ORM\SequenceGenerator(sequenceName: 'ignored_query_params_id_seq')]
    private int $id;

    #[ORM\Column(type: 'integer', name: 'cdn_id')]
    private int $cdnLegacyId;

    #[ORM\Column(type: 'string', length: 100, name: 'parameter')]
    private string $name;

    #[ORM\Column(type: 'datetime_immutable', name: 'created')]
    private DateTimeImmutable $createdAt;

    public function __construct(CdnLegacyId $cdnLegacyId, string $name, DateTimeImmutable $createdAt)
    {
        $this->cdnLegacyId = $cdnLegacyId->toInt();
        $this->name = $name;
        $this->createdAt = $createdAt;
    }

    public function getName(): string
    {
        return $this->name;
    }
}
