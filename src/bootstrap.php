<?php

declare(strict_types=1);

use Paygate\Commands\CompleteTestWireTransfer;
use Paygate\Commands\CreateTestWireTransfer;
use Symfony\Component\Console\Application;

$autoloadCandidates = [
    __DIR__ . '/../../../autoload.php',
    __DIR__ . '/../vendor/autoload.php',
];

$autoloadFound = false;

foreach ($autoloadCandidates as $candidate) {
    if (file_exists($candidate)) {
        require_once $candidate;
        $autoloadFound = true;

        break;
    }
}

if (! $autoloadFound) {
    echo "Fatal error: Composer autoloader not found\n";
    exit(-1);
}

if (! class_exists(Application::class)) {
    echo "Fatal error: the Symfony console component could not be found, please install symfony/console via Composer\n";
    exit(-1);
}

define('ARGV0', $_SERVER['argv'][0]);

$app = new Symfony\Component\Console\Application('paygate');
$app->add(new CreateTestWireTransfer());
$app->add(new CompleteTestWireTransfer());

return $app;
