<?php

declare(strict_types=1);

namespace Cdn77\Api\Tariff\Domain\Query;

use Cdn77\Api\Core\Domain\Entity\Customer\CustomerId;
use Cdn77\Api\Core\Domain\Messaging\Query;
use Cdn77\Api\Tariff\Domain\Dto\CreditDetail;

/** @implements Query<CreditDetail, GetAvailableCreditHandler> */
final class GetAvailableCredit implements Query
{
    public function __construct(public CustomerId $customerId)
    {
    }
}
