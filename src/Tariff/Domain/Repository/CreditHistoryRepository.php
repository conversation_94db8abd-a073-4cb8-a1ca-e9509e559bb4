<?php

declare(strict_types=1);

namespace Cdn77\Api\Tariff\Domain\Repository;

use Cdn77\Api\Core\Domain\Dto\TimeRange;
use Cdn77\Api\Core\Domain\Entity\Customer\CustomerUuid;
use Cdn77\Api\Core\Domain\Entity\Tariff\CreditHistory;
use Generator;

interface CreditHistoryRepository
{
    /** @return Generator<CreditHistory> */
    public function findAllForCustomerInRange(CustomerUuid $customerId, TimeRange $timeRange): Generator;
}
