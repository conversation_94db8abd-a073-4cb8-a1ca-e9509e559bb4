<?php

declare(strict_types=1);

namespace Cdn77\Api\Tariff\Infrastructure\Repository;

use Cdn77\Api\Core\Domain\Entity\Customer\CustomerUuid;
use Cdn77\Api\Core\Domain\Entity\Tariff\Tariff;
use Cdn77\Api\Core\Infrastructure\EntityManagerConstructor;
use Cdn77\Api\Tariff\Domain\Repository\TariffRepository;
use DateTimeImmutable;
use Generator;

final class DoctrineTariffRepository implements TariffRepository
{
    use EntityManagerConstructor;

    public function add(Tariff $tariff): void
    {
        $this->entityManager->persist($tariff);
    }

    public function findAllForCustomerSince(CustomerUuid $customerId, DateTimeImmutable $since): Generator
    {
        /** @var Generator<Tariff> $tariffs */
        $tariffs = $this->entityManager->createQueryBuilder()
            ->select('tariff')
            ->from(Tariff::class, 'tariff')
            ->join('tariff.customer', 'customer')
            ->where('customer.newId = :customerId')
            ->setParameter('customerId', $customerId->toString())
            ->andWhere('tariff.deactivationTime IS NULL OR tariff.deactivationTime >= :since')
            ->setParameter('since', $since)
            ->getQuery()
            ->toIterable();

        return $tariffs;
    }
}
