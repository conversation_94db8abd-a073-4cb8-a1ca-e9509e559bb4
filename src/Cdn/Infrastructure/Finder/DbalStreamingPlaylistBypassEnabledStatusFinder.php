<?php

declare(strict_types=1);

namespace Cdn77\Api\Cdn\Infrastructure\Finder;

use Cdn77\Api\Cdn\Domain\Finder\StreamingPlaylistBypassEnabledStatusFinder;
use Cdn77\Api\Core\Domain\Entity\Cdn\CdnId;
use Cdn77\Api\Core\Domain\Value\Identifier\IdentifierMapper;
use Cdn77\Api\Core\Infrastructure\DbalConnectionConstructor;
use Doctrine\DBAL\Exception;
use Ds\Map;
use Ds\Pair;
use Ds\Set;
use Psl\Type\Exception\AssertException;

use function Cdn77\Functions\mapFromIterable;
use function Psl\Type\bool;
use function Psl\Type\dict;
use function Psl\Type\int;
use function Psl\Type\shape;

final readonly class DbalStreamingPlaylistBypassEnabledStatusFinder
    implements StreamingPlaylistBypassEnabledStatusFinder
{
    use DbalConnectionConstructor;

    /**
     * @throws AssertException
     * @throws Exception
     */
    public function findForCdns(Set $cdnIds): Map
    {
        if ($cdnIds->isEmpty()) {
            return new Map();
        }

        $format = dict(int(), shape([
            'cdn_id' => int(),
            'streaming_playlist_bypass' => bool(),
        ]));
        $result = $this->connection->fetchAllAssociative(
            <<<'PSQL'
            SELECT cdn.resource_id AS cdn_id, http.streaming_playlist_bypass
            FROM cdn_http http
            JOIN cdn cdn ON http.cdn_id = cdn.id
            WHERE cdn.resource_id IN (:cdnIds)
            PSQL,
            ['cdnIds' => IdentifierMapper::toIntegers($cdnIds)],
        );

        $format->assert($result);

        return mapFromIterable(
            $result,
            static fn (mixed $_, array $row) => new Pair(
                CdnId::fromInteger($row['cdn_id']),
                $row['streaming_playlist_bypass'],
            ),
        );
    }
}
