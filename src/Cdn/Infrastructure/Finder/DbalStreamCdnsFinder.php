<?php

declare(strict_types=1);

namespace Cdn77\Api\Cdn\Infrastructure\Finder;

use Cdn77\Api\Cdn\Domain\Finder\StreamCdnFinder;
use Cdn77\Api\Cdn\Domain\Query\FindStreamCdns\Input\Filter;
use Cdn77\Api\Cdn\Domain\Query\FindStreamCdns\Input\SelectionField;
use Cdn77\Api\Cdn\Domain\Query\FindStreamCdns\Output\StreamCdn;
use Cdn77\Api\Cdn\Domain\Query\FindStreamCdns\Output\StreamCdns;
use Cdn77\Api\Core\Domain\Value\Identifier\IdentifierMapper;
use Cdn77\Api\GraphQL\Domain\Schema\Type\Dto\DefaultDtoMapperProvider;
use CuyZ\Valinor\Mapper\MappingError;
use Doctrine\DBAL\ArrayParameterType;
use Doctrine\DBAL\Connection;
use Doctrine\DBAL\Exception;
use Doctrine\DBAL\Query\QueryBuilder;
use Ds\Set;
use Ds\Vector;

final readonly class DbalStreamCdnsFinder implements StreamCdnFinder
{
    public function __construct(
        private Connection $connection,
    ) {
    }

    /**
     * @throws MappingError
     * @throws Exception
     */
    public function find(
        Set $selectionFields,
        Filter $filter,
    ): StreamCdns {
        $queryBuilder = $this->connection->createQueryBuilder()->from('public.cdn_stream', 'source');

        $queryBuilder
            ->join('source', 'public.cdn', 'cdn', 'source.cdn_id = cdn.id')
            ->join('cdn', 'public.accounts', 'customer', 'cdn.account_id = customer.id');

        $this->applySelectionFields($queryBuilder, $selectionFields);
        $this->applyFilter($queryBuilder, $filter);

        $mapper = DefaultDtoMapperProvider::get();
        $streamCdns = new Vector($mapper->map(StreamCdn::class . '[]', $queryBuilder->fetchAllAssociative()));

        return new StreamCdns(
            streamCdns: $streamCdns,
        );
    }

    private function applyFilter(QueryBuilder $queryBuilder, Filter $filter): void
    {
        if ($filter->cdnIds->isEmpty()) {
            return;
        }

        $queryBuilder->andWhere('cdn.resource_id IN (:cdnIds)')
            ->setParameter('cdnIds', IdentifierMapper::toIntegers($filter->cdnIds), ArrayParameterType::INTEGER);
    }

    /** @param Set<SelectionField> $selectionFields */
    private function applySelectionFields(QueryBuilder $queryBuilder, Set $selectionFields): void
    {
        $queryBuilder->select('cdn.resource_id as "id"');

        foreach ($selectionFields as $selectionField) {
            $sqlSelect = match ($selectionField) {
                SelectionField::Key => 'source.key as "key"',
                SelectionField::StreamOriginId => 'source.origin_id as "streamOriginId"',
                SelectionField::Password => 'source.password as "password"',
                SelectionField::Path => 'source.path as "path"',
                SelectionField::Port => 'source.port as "port"',
                SelectionField::Protocol => 'source.protocol as "streamProtocol"',
                SelectionField::Customer => 'customer.new_id as "customerId"',
                SelectionField::Id => null,
            };

            if ($sqlSelect === null) {
                continue;
            }

            $queryBuilder->addSelect($sqlSelect);
        }
    }
}
