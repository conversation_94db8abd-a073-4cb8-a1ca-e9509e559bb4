<?php

declare(strict_types=1);

namespace Cdn77\Api\Cdn\Infrastructure\Forman;

use Cdn77\Api\Cdn\Domain\Dto\Log\LogRecords;
use Cdn77\Api\Cdn\Domain\Forman\FormanApi;
use Cdn77\Api\Cdn\Domain\Value\Forman\RealTimeLogConfiguration;
use Cdn77\Api\Core\Domain\Entity\RealTimeLog\RealTimeLogId;
use Ds\Pair;
use Ds\Vector;

final readonly class FakeFormanApi implements FormanApi
{
    /** @param Vector<Pair<RealTimeLogId, RealTimeLogConfiguration>> $enableCalls */
    public function __construct(
        private Vector $enableCalls = new Vector(),
    ) {
    }

    public function enable(
        RealTimeLogId $realTimeLogId,
        RealTimeLogConfiguration $config,
    ): void {
        $this->enableCalls->push(new Pair($realTimeLogId, $config));
    }

    public function disable(RealTimeLogId $realTimeLogId): void
    {
    }

    public function peek(
        RealTimeLogId $realTimeLogId,
        int $limit,
    ): LogRecords {
        return new LogRecords('fake log records');
    }

    /** @return Vector<Pair<RealTimeLogId, RealTimeLogConfiguration>> */
    public function enabledCalls(): Vector
    {
        return $this->enableCalls;
    }
}
