<?php

declare(strict_types=1);

namespace Cdn77\Api\Cdn\Infrastructure\Repository;

use Cdn77\Api\Cdn\Domain\Exception\CdnHttpProtectionIsNotSetup;
use Cdn77\Api\Cdn\Domain\Repository\CdnHttpProtectionRepository;
use Cdn77\Api\Core\Domain\Entity\Cdn\Cdn;
use Cdn77\Api\Core\Domain\Entity\Cdn\CdnHttpProtection;
use Cdn77\Api\Core\Domain\Entity\Cdn\CdnId;
use Cdn77\Api\Core\Infrastructure\EntityManagerConstructor;
use Doctrine\ORM\Query\Expr\Join;

final readonly class DoctrineCdnHttpProtectionRepository implements CdnHttpProtectionRepository
{
    use EntityManagerConstructor;

    public function add(CdnHttpProtection $cdnHttpProtection): void
    {
        $this->entityManager->persist($cdnHttpProtection);
    }

    public function getForCdn(CdnId $cdnId): CdnHttpProtection
    {
        /** @var CdnHttpProtection|null $cdnHttpProtection */
        $cdnHttpProtection = $this->entityManager->createQueryBuilder()
            ->select('cdnHttpProtection')
            ->from(CdnHttpProtection::class, 'cdnHttpProtection')
            ->innerJoin(Cdn::class, 'cdn', Join::WITH, 'cdn.legacyId = cdnHttpProtection.cdnLegacyId')
            ->where('cdn.id = :cdnId')
            ->setParameter('cdnId', $cdnId->toInt())
            ->getQuery()
            ->getOneOrNullResult();

        if ($cdnHttpProtection === null) {
            throw CdnHttpProtectionIsNotSetup::fromCdnId($cdnId);
        }

        return $cdnHttpProtection;
    }
}
