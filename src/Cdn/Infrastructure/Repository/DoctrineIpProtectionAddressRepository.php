<?php

declare(strict_types=1);

namespace Cdn77\Api\Cdn\Infrastructure\Repository;

use Cdn77\Api\Cdn\Domain\Repository\IpProtectionAddressRepository;
use Cdn77\Api\Core\Domain\Entity\Cdn\Cdn;
use Cdn77\Api\Core\Domain\Entity\Cdn\CdnHttpProtection;
use Cdn77\Api\Core\Domain\Entity\Cdn\CdnId;
use Cdn77\Api\Core\Domain\Entity\Cdn\IpProtectionAddress;
use Cdn77\Api\Core\Infrastructure\EntityManagerConstructor;
use Doctrine\ORM\Query\Expr\Join;

final readonly class DoctrineIpProtectionAddressRepository implements IpProtectionAddressRepository
{
    use EntityManagerConstructor;

    public function add(IpProtectionAddress $ipProtectionAddress): void
    {
        $this->entityManager->persist($ipProtectionAddress);
    }

    public function remove(IpProtectionAddress $ipProtectionAddress): void
    {
        $this->entityManager->remove($ipProtectionAddress);
    }

    /** @return array<IpProtectionAddress> */
    public function findForCdn(CdnId $cdnId): array
    {
        /** @var array<IpProtectionAddress> $ips */
        $ips = $this->entityManager->createQueryBuilder()
            ->select('ippAddress')
            ->from(IpProtectionAddress::class, 'ippAddress')
            ->innerJoin(
                CdnHttpProtection::class,
                'cdnHttpProtection',
                Join::WITH,
                'cdnHttpProtection.id = ippAddress.cdnHttpProtectionId',
            )
            ->innerJoin(Cdn::class, 'cdn', Join::WITH, 'cdnHttpProtection.cdnLegacyId = cdn.legacyId')
            ->where('cdn.id = :cdnId')
            ->setParameter('cdnId', $cdnId->toInt())
            ->getQuery()
            ->getResult();

        return $ips;
    }
}
