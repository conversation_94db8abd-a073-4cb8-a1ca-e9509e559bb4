<?php

declare(strict_types=1);

namespace Cdn77\Api\Cdn\Application\Docs\HttpsRedirect;

use Cdn77\Api\Cdn\Application\Payload\HttpsRedirect\HttpsRedirectSchema;
use Cdn77\Api\Cdn\Domain\Value\HttpsRedirectCode;
use Cdn77\Api\Core\Application\Payload\ComponentSchema;
use Cdn77\Api\Core\Application\Payload\OASchema;
use cebe\openapi\spec\Schema;
use cebe\openapi\spec\Type;

final readonly class HttpsRedirectSchemaSpec implements ComponentSchema, OASchema
{
    public const string Name = 'httpsRedirect';

    public static function name(): string
    {
        return self::Name;
    }

    public static function getSchemaSpec(): Schema
    {
        return new Schema([
            'type' => Type::OBJECT,
            'description' =>
                'If enabled, all requests via HTTP are redirected to HTTPS. Verify HTTPS availability of CNAMEs ' .
                'before activating, if applicable.',
            'properties' => [
                HttpsRedirectSchema::FieldCode => HttpsRedirectCode::getReference(),
                HttpsRedirectSchema::FieldEnabled => new Schema(['type' => Type::BOOLEAN]),
            ],
            'required' => [HttpsRedirectSchema::FieldEnabled],
        ]);
    }
}
