<?php

declare(strict_types=1);

namespace Cdn77\Api\Cdn\Application\Docs;

use Cdn77\Api\Cdn\Application\Payload\AccessProtection\GeoProtectionSchema;
use Cdn77\Api\Cdn\Application\Payload\AccessProtection\HotlinkProtectionSchema;
use Cdn77\Api\Cdn\Application\Payload\AccessProtection\IpProtectionSchema;
use Cdn77\Api\Cdn\Application\Payload\Cache\CacheSchema;
use Cdn77\Api\Cdn\Application\Payload\CdnSchema;
use Cdn77\Api\Cdn\Application\Payload\Headers\HeadersSchema;
use Cdn77\Api\Cdn\Application\Payload\HttpsRedirect\HttpsRedirectSchema;
use Cdn77\Api\Cdn\Application\Payload\MP4PseudoStreaming\MP4PseudoStreamingSchema;
use Cdn77\Api\Cdn\Application\Payload\OriginHeaders\OriginHeadersSchema;
use Cdn77\Api\Cdn\Application\Payload\QueryString\QueryStringSchema;
use Cdn77\Api\Cdn\Application\Payload\RateLimitSchema;
use Cdn77\Api\Cdn\Application\Payload\ResponseHeaders\ResponseHeadersSchema;
use Cdn77\Api\Cdn\Application\Payload\SecureToken\SecureTokenSchema;
use Cdn77\Api\Cdn\Application\Payload\Ssl\SslSchema;
use Cdn77\Api\Cdn\Application\Payload\Stream\StreamSettingsSchema;
use Cdn77\Api\Cname\Application\Payload\CnamesSchema;
use Cdn77\Api\Core\Application\OpenApi\Schema\DateTimeSchema;
use Cdn77\Api\Core\Application\Payload\ComponentSchema;
use Cdn77\Api\Core\Application\Payload\OASchema;
use Cdn77\Api\Core\Application\Payload\SchemaEditor;
use Cdn77\Api\Core\Domain\Entity\Cdn\CdnId;
use Cdn77\Api\Core\Domain\Entity\Origin\OriginId;
use cebe\openapi\exceptions\TypeErrorException;
use cebe\openapi\spec\Schema;
use cebe\openapi\spec\Type;

final readonly class CdnSchemaSpec implements ComponentSchema, OASchema
{
    public const string Name = 'cdn';

    public static function name(): string
    {
        return self::Name;
    }

    public static function getSchemaSpec(): Schema
    {
        return new Schema([
            'type' => Type::OBJECT,
            'properties' => [
                CdnSchema::FieldId => CdnId::getSchemaSpec(),
                CdnSchema::FieldCnames => CnamesSchema::reference(),
                CdnSchema::FieldCreationTime => DateTimeSchema::spec('Timestamp when CDN Resource was created.'),
                //CdnSchema::FieldFollowRedirect => FollowRedirectSchema::reference(), // intentionally hidden from spec
                CdnSchema::FieldLabel => self::getLabelSchemaSpec(),
                CdnSchema::FieldNote => self::getNoteSchemaSpec(),
                CdnSchema::FieldOriginId => SchemaEditor::makeNullable(OriginId::getSchemaSpecWithSectionLink()),
                CdnSchema::FieldUrl => new Schema([
                    'description' =>
                        'URL of the CDN Resource. Automatically generated when the CDN Resource is created. ' .
                        'The number is the same as the CDN Resource ID.',
                    'example' => '1234567890.rsc.cdn77.org',
                    'type' => Type::STRING,
                ]),
                CdnSchema::FieldCache => CacheSchema::reference(),
                CdnSchema::FieldSecureToken => SecureTokenSchema::reference(),
                CdnSchema::FieldQueryString => QueryStringSchema::reference(),
                CdnSchema::FieldHeaders => HeadersSchema::reference(),
                CdnSchema::FieldHttpsRedirect => HttpsRedirectSchema::reference(),
                CdnSchema::FieldMp4PseudoStreaming => MP4PseudoStreamingSchema::reference(),
                CdnSchema::FieldSsl => SslSchema::reference(),
                CdnSchema::FieldStream => StreamSettingsSchema::reference(),
                CdnSchema::FieldHotlinkProtection => HotlinkProtectionSchema::reference(),
                CdnSchema::FieldIpProtection => IpProtectionSchema::reference(),
                CdnSchema::FieldGeoProtection => GeoProtectionSchema::reference(),
                CdnSchema::FieldRateLimit => RateLimitSchema::reference(),
                CdnSchema::FieldOriginHeaders => OriginHeadersSchema::reference(),
                CdnSchema::FieldResponseHeaders => ResponseHeadersSchema::reference(),
            ],
            'required' => [
                CdnSchema::FieldId,
                CdnSchema::FieldCnames,
                CdnSchema::FieldCreationTime,
                CdnSchema::FieldLabel,
                CdnSchema::FieldUrl,
            ],
        ]);
    }

    /** @throws TypeErrorException */
    public static function getLabelSchemaSpec(): Schema
    {
        return new Schema([
            'description' => 'The label helps you to identify your CDN Resource.',
            'example' => 'My cdn',
            'type' => Type::STRING,
        ]);
    }

    /** @throws TypeErrorException */
    public static function getNoteSchemaSpec(): Schema
    {
        return new Schema([
            'description' => 'Optional note for the CDN Resource.',
            'example' => 'Note for my CDN',
            'type' => Type::STRING,
            'nullable' => true,
        ]);
    }
}
