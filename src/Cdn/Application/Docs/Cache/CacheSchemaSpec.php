<?php

declare(strict_types=1);

namespace Cdn77\Api\Cdn\Application\Docs\Cache;

use Cdn77\Api\Cdn\Application\Payload\Cache\CacheSchema;
use Cdn77\Api\Cdn\Domain\Value\MaxAge;
use Cdn77\Api\Cdn\Domain\Value\MaxAge404;
use Cdn77\Api\Core\Application\Payload\ComponentSchema;
use Cdn77\Api\Core\Application\Payload\OASchema;
use cebe\openapi\spec\Schema;
use cebe\openapi\spec\Type;

final readonly class CacheSchemaSpec implements ComponentSchema, OASchema
{
    public const string Name = 'cache';

    public static function name(): string
    {
        return self::Name;
    }

    public static function getSchemaSpec(): Schema
    {
        return new Schema([
            'type' => Type::OBJECT,
            'description' =>
                'Your files will remain cached for the specified duration, after which your origin will be ' .
                'checked for an updated version of your files. Expiry/cache-control headers override this setting.',
            'properties' => [
                CacheSchema::FieldMaxAge => MaxAge::getReference(),
                CacheSchema::FieldMaxAge404 => MaxAge404::getReference(),
                CacheSchema::FieldRequestWithCookiesEnabled => new Schema(
                    [
                        'type' => Type::BOOLEAN,
                        'description' =>
                            'When disabled, requests with cookies will ignore changing cookie headers ' .
                            'allowing all requests to hit the cache. When enabled, requests with cookies ' .
                            'will be handled separately, so when the cookie changes it will not hit ' .
                            'the previously cached request with different cookie options.',
                    ],
                ),
            ],
            'minProperties' => 1,
        ]);
    }
}
