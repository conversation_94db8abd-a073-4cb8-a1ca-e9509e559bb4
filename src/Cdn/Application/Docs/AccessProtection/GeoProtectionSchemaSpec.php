<?php

declare(strict_types=1);

namespace Cdn77\Api\Cdn\Application\Docs\AccessProtection;

use Cdn77\Api\Cdn\Application\Payload\AccessProtection\GeoProtectionSchema;
use Cdn77\Api\Cdn\Domain\Value\AccessProtectionType;
use Cdn77\Api\Core\Application\Payload\ComponentSchema;
use Cdn77\Api\Core\Application\Payload\OASchema;
use cebe\openapi\spec\Schema;
use cebe\openapi\spec\Type;

use function sprintf;

final readonly class GeoProtectionSchemaSpec implements ComponentSchema, OASchema
{
    public const string Name = 'geoProtection';

    public static function name(): string
    {
        return self::Name;
    }

    public static function getSchemaSpec(): Schema
    {
        return new Schema([
            'type' => Type::OBJECT,
            'description' => 'Geo protection enables you to control which countries can access your content directly.',
            'properties' => [
                GeoProtectionSchema::FieldCountries => new Schema([
                    'type' => Type::ARRAY,
                    'items' => new Schema(['type' => Type::STRING, 'example' => 'UK']),
                    'description' => sprintf(
                        'We are using ISO 3166-1 alpha-2 code. For full list check ' .
                        '<a href="%s" target="_blank">Wikipedia</a>.',
                        'https://en.wikipedia.org/wiki/ISO_3166-1_alpha-2#Officially_assigned_code_elements',
                    ),
                ]),
                GeoProtectionSchema::FieldType => AccessProtectionType::getReference(),
            ],
            'required' => [GeoProtectionSchema::FieldType],
        ]);
    }
}
