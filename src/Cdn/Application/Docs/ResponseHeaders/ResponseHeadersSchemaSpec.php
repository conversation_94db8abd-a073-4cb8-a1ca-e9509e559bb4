<?php

declare(strict_types=1);

namespace Cdn77\Api\Cdn\Application\Docs\ResponseHeaders;

use Cdn77\Api\Cdn\Application\Payload\ResponseHeaders\HeaderSchema;
use Cdn77\Api\Cdn\Application\Payload\ResponseHeaders\ResponseHeadersSchema;
use Cdn77\Api\Core\Application\Payload\ComponentSchema;
use Cdn77\Api\Core\Application\Payload\OASchema;
use cebe\openapi\spec\Schema;
use cebe\openapi\spec\Type;

final class ResponseHeadersSchemaSpec implements ComponentSchema, OASchema
{
    public const string Name = 'responseHeaders';

    public static function name(): string
    {
        return self::Name;
    }

    public static function getSchemaSpec(): Schema
    {
        return new Schema([
            'type' => Type::OBJECT,
            'description' => 'Custom HTTP headers included in response sent to the client.',
            'properties' => [
                ResponseHeadersSchema::FieldHeaders => new Schema([
                    'type' => Type::ARRAY,
                    'description' => 'Custom HTTP headers included in response sent to client.',
                    'nullable' => true,
                    'items' => HeaderSchema::reference(),
                ]),
            ],
        ]);
    }
}
