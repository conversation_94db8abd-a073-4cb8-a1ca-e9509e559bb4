<?php

declare(strict_types=1);

namespace Cdn77\Api\Cdn\Application\Controller\Tsunami;

use Cdn77\Api\Cdn\Application\Payload\Tsunami\Request\GetStatusSchema;
use Cdn77\Api\Cdn\Application\Payload\Tsunami\Response\TargetsStatusSchema;
use Cdn77\Api\Cdn\Domain\Query\GetTsunamiStatus;
use Cdn77\Api\Core\Application\Controller\HasInternalOpenApiPaths;
use Cdn77\Api\Core\Application\OpenApi\Model\Tags;
use Cdn77\Api\Core\Application\OpenApi\PathGenerator;
use Cdn77\Api\Core\Application\Payload\ErrorsSchema;
use Cdn77\Api\Core\Application\Request\ControllerSchemaSerializer;
use Cdn77\Api\Core\Application\Response\ControllerQueryHandler;
use Cdn77\Api\Core\Application\Symfony\LoggedAccountProvider;
use cebe\openapi\spec\Operation;
use cebe\openapi\spec\PathItem;
use cebe\openapi\spec\RequestBody;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;

final readonly class StatusController implements HasInternalOpenApiPaths
{
    public const string RouteName = 'cdn.tsunami.status';
    public const string RouteSummary = 'Get Tsunami status.';

    public function __construct(
        private ControllerSchemaSerializer $controllerSchemaSerializer,
        private ControllerQueryHandler $controllerQueryHandler,
        private LoggedAccountProvider $loggedAccountProvider,
        private PathGenerator $pathGenerator,
    ) {
    }

    #[Route('/cdn/tsunami/status', name: self::RouteName, methods: [Request::METHOD_POST])]
    public function execute(Request $request): JsonResponse
    {
        $payload = $this->controllerSchemaSerializer->deserializeToSchema($request, GetStatusSchema::class);

        if ($payload instanceof ErrorsSchema) {
            return $this->controllerSchemaSerializer->serializeToResponse(
                $payload,
                Response::HTTP_UNPROCESSABLE_ENTITY,
            );
        }

        return $this->controllerQueryHandler->handle(
            new GetTsunamiStatus(
                $this->loggedAccountProvider->customerId(),
                $payload->toDto(),
            ),
            TargetsStatusSchema::class,
        );
    }

    public function getPathItems(): array
    {
        $post = new Operation([
            'operationId' => self::RouteName,
            'tags' => [Tags::CdnResources],
            'summary' => self::RouteSummary,
            'requestBody' => new RequestBody([
                'content' => [
                    'application/json' => [
                        'schema' => GetStatusSchema::getSchemaSpec(),
                    ],
                ],
            ]),
            'responses' => [
                Response::HTTP_OK => [
                    'description' => 'Tsunami status.',
                    'content' => [
                        'application/json' => [
                            'schema' => TargetsStatusSchema::getSchemaSpec(),
                        ],
                    ],
                ],
            ],
        ]);

        return [
            $this->pathGenerator->generate(self::RouteName) => new PathItem(['post' => $post]),
        ];
    }
}
