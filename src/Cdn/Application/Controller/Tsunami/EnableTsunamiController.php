<?php

declare(strict_types=1);

namespace Cdn77\Api\Cdn\Application\Controller\Tsunami;

use Cdn77\Api\Cdn\Application\Payload\Tsunami\Request\EnableTsunamiSchema;
use Cdn77\Api\Cdn\Application\Payload\Tsunami\Response\EnableTsunamiSchema as ResponseEnableTsunamiSchema;
use Cdn77\Api\Core\Application\Controller\HasInternalOpenApiPaths;
use Cdn77\Api\Core\Application\OpenApi\ErrorsOpenApiResponse;
use Cdn77\Api\Core\Application\OpenApi\FieldsErrorsOpenApiResponse;
use Cdn77\Api\Core\Application\OpenApi\Model\Tags;
use Cdn77\Api\Core\Application\OpenApi\PathGenerator;
use Cdn77\Api\Core\Application\Payload\ErrorsSchema;
use Cdn77\Api\Core\Application\Request\ControllerSchemaSerializer;
use Cdn77\Api\Core\Application\Response\ControllerCommandHandler;
use Cdn77\Api\Core\Application\Symfony\LoggedAccountProvider;
use cebe\openapi\spec\MediaType;
use cebe\openapi\spec\Operation;
use cebe\openapi\spec\PathItem;
use cebe\openapi\spec\RequestBody;
use cebe\openapi\spec\Response as OpenApiResponse;
use cebe\openapi\spec\Responses;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;

final readonly class EnableTsunamiController implements HasInternalOpenApiPaths
{
    public const string RouteName = 'cdn.tsunami.enable';
    private const string RouteSummary = 'Enable Tsunami';

    public function __construct(
        private ControllerCommandHandler $controllerCommandHandler,
        private ControllerSchemaSerializer $controllerSchemaSerializer,
        private LoggedAccountProvider $loggedAccountProvider,
        private PathGenerator $pathGenerator,
    ) {
    }

    #[Route(path: '/cdn/tsunami/enable', name: self::RouteName, methods: [Request::METHOD_POST])]
    public function execute(Request $request): Response
    {
        $schema = $this->controllerSchemaSerializer->deserializeToSchema($request, EnableTsunamiSchema::class);

        if ($schema instanceof ErrorsSchema) {
            return $this->controllerSchemaSerializer->serializeToResponse($schema, Response::HTTP_UNPROCESSABLE_ENTITY);
        }

        return $this->controllerCommandHandler->handleWithResult(
            $schema->toCommand($this->loggedAccountProvider->customerId()),
            ResponseEnableTsunamiSchema::class,
            Response::HTTP_OK,
        );
    }

    public function getPathItems(): array
    {
        $post = new Operation([
            'operationId' => self::RouteName,
            'tags' => [Tags::CdnResources],
            'summary' => self::RouteSummary,
            'requestBody' => new RequestBody(
                [
                    'required' => true,
                    'content' => [
                        'application/json' => new MediaType(
                            [
                                'schema' => EnableTsunamiSchema::getSchemaSpec(),
                            ],
                        ),
                    ],
                ],
            ),
            'responses' => new Responses(
                [
                    Response::HTTP_NO_CONTENT => new OpenApiResponse(
                        ['description' => 'Tsunami enabled.'],
                    ),
                    Response::HTTP_NOT_FOUND => ErrorsOpenApiResponse::spec('Failed to enable Tsunami.'),
                    Response::HTTP_UNPROCESSABLE_ENTITY => FieldsErrorsOpenApiResponse::spec('Invalid input.'),
                    'default' => ErrorsOpenApiResponse::spec('Unknown error occurred.'),
                ],
            ),
        ]);

        return [$this->pathGenerator->generate(self::RouteName) => new PathItem(['post' => $post])];
    }
}
