<?php

declare(strict_types=1);

namespace Cdn77\Api\Cdn\Application\Controller;

use Cdn77\Api\Cdn\Application\Payload\DatacenterLocationsSchema;
use Cdn77\Api\Cdn\Domain\Query\GetDatacenterLocationsForCdn;
use Cdn77\Api\Core\Application\Controller\HasOpenApiPaths;
use Cdn77\Api\Core\Application\OpenApi\ErrorsOpenApiResponse;
use Cdn77\Api\Core\Application\OpenApi\FieldsErrorsOpenApiResponse;
use Cdn77\Api\Core\Application\OpenApi\Model\Tags;
use Cdn77\Api\Core\Application\OpenApi\PathGenerator;
use Cdn77\Api\Core\Application\Response\ControllerQueryHandler;
use Cdn77\Api\Core\Application\Symfony\LoggedAccountProvider;
use Cdn77\Api\Core\Domain\Entity\Cdn\CdnId;
use cebe\openapi\spec\MediaType;
use cebe\openapi\spec\Operation;
use cebe\openapi\spec\Parameter;
use cebe\openapi\spec\PathItem;
use cebe\openapi\spec\Response as OpenApiResponse;
use cebe\openapi\spec\Responses;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;

final readonly class DatacenterLocationsController implements HasOpenApiPaths
{
    public const string RouteName = 'cdn.datacenters.list';
    private const string RouteSummary = 'List of data centers';

    public function __construct(
        private ControllerQueryHandler $controllerQueryHandler,
        private LoggedAccountProvider $loggedAccountProvider,
        private PathGenerator $pathGenerator,
    ) {
    }

    #[Route(path: '/cdn/{id}/datacenters', name: self::RouteName, methods: [Request::METHOD_GET])]
    public function execute(Request $request, CdnId $id): JsonResponse
    {
        $customerId = $this->loggedAccountProvider->legacyId();

        return $this->controllerQueryHandler->handle(
            new GetDatacenterLocationsForCdn($customerId, $id),
            DatacenterLocationsSchema::class,
        );
    }

    public function getPathItems(): array
    {
        $get = new Operation(
            [
                'operationId' => self::RouteName,
                'tags' => [Tags::CdnResources],
                'summary' => self::RouteSummary,
                'parameters' => [
                    new Parameter([
                        'name' => 'id',
                        'in' => 'path',
                        'required' => true,
                        'schema' => CdnId::getSchemaSpec(),
                    ]),
                ],
                'responses' => new Responses(
                    [
                        Response::HTTP_OK => new OpenApiResponse([
                            'description' => 'List of datacenters for CDN Resource.',
                            'content' => [
                                'application/json' => new MediaType([
                                    'schema' => DatacenterLocationsSchema::getSchemaSpec(),
                                ]),
                            ],
                        ]),
                        Response::HTTP_NOT_FOUND => ErrorsOpenApiResponse::spec('CDN Resource not found.'),
                        Response::HTTP_UNPROCESSABLE_ENTITY => FieldsErrorsOpenApiResponse::spec(
                            'Data centers list could not be resolved.',
                        ),
                        'default' => ErrorsOpenApiResponse::spec('Unknown error occurred.'),
                    ],
                ),
            ],
        );

        return [$this->pathGenerator->generate(self::RouteName) => new PathItem(['get' => $get])];
    }
}
