<?php

declare(strict_types=1);

namespace Cdn77\Api\Cdn\Application\Controller;

use Cdn77\Api\Cdn\Application\Docs\CdnSchemaSpec;
use Cdn77\Api\Cdn\Application\Mapping\CdnResponseFactory;
use Cdn77\Api\Cdn\Domain\Query\GetCdnForCustomer;
use Cdn77\Api\Core\Application\Controller\HasOpenApiPaths;
use Cdn77\Api\Core\Application\OpenApi\ErrorsOpenApiResponse;
use Cdn77\Api\Core\Application\OpenApi\Model\ComponentReference;
use Cdn77\Api\Core\Application\OpenApi\Model\Tags;
use Cdn77\Api\Core\Application\OpenApi\PathGenerator;
use Cdn77\Api\Core\Application\Symfony\LoggedAccountProvider;
use Cdn77\Api\Core\Domain\Entity\Cdn\CdnId;
use cebe\openapi\spec\MediaType;
use cebe\openapi\spec\Operation;
use cebe\openapi\spec\Parameter;
use cebe\openapi\spec\PathItem;
use cebe\openapi\spec\Response as OpenApiResponse;
use cebe\openapi\spec\Responses;
use Defuse\Crypto\Exception\CryptoException;
use JMS\Serializer\Exception\RuntimeException;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;
use TypeError;

final readonly class DetailController implements HasOpenApiPaths
{
    public const string RouteName = 'cdn.detail';
    private const string RouteSummary = 'Detail of CDN Resource.';

    public function __construct(
        private CdnResponseFactory $cdnResponseFactory,
        private LoggedAccountProvider $loggedAccountProvider,
        private PathGenerator $pathGenerator,
    ) {
    }

    /**
     * @throws CryptoException
     * @throws RuntimeException
     * @throws TypeError
     */
    #[Route(path: '/cdn/{id}', name: self::RouteName, methods: [Request::METHOD_GET])]
    public function detailAction(CdnId $id): JsonResponse
    {
        $customerId = $this->loggedAccountProvider->legacyId();

        return $this->cdnResponseFactory->fromDetailQuery(new GetCdnForCustomer($id, $customerId));
    }

    public function getPathItems(): array
    {
        $get = new Operation([
            'operationId' => self::RouteName,
            'tags' => [Tags::CdnResources],
            'summary' => self::RouteSummary,
            'parameters' => [
                new Parameter([
                    'in' => 'path',
                    'name' => 'id',
                    'required' => true,
                    'schema' => CdnId::getSchemaSpec(),
                ]),
            ],
            'responses' => new Responses([
                Response::HTTP_OK => new OpenApiResponse([
                    'description' => 'Detail of the given CDN Resource returned.',
                    'content' => [
                        'application/json' => new MediaType([
                            'schema' => ComponentReference::forSchema(CdnSchemaSpec::class),
                        ]),
                    ],
                ]),
                Response::HTTP_NOT_FOUND => ErrorsOpenApiResponse::spec('CDN Resource not found.'),
                'default' => ErrorsOpenApiResponse::spec('Unknown error occurred.'),
            ]),
        ]);

        return [$this->pathGenerator->generate(self::RouteName) => new PathItem(['get' => $get])];
    }
}
