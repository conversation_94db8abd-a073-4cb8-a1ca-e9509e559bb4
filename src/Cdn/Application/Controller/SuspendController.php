<?php

declare(strict_types=1);

namespace Cdn77\Api\Cdn\Application\Controller;

use Cdn77\Api\Cdn\Application\Payload\SuspendCdnSchema;
use Cdn77\Api\Cdn\Domain\Command\SuspendCdn;
use Cdn77\Api\Core\Application\Controller\HasInternalOpenApiPaths;
use Cdn77\Api\Core\Application\Controller\RequiresInternalTokenAuthentication;
use Cdn77\Api\Core\Application\OpenApi\ErrorsOpenApiResponse;
use Cdn77\Api\Core\Application\OpenApi\FieldsErrorsOpenApiResponse;
use Cdn77\Api\Core\Application\OpenApi\Model\Tags;
use Cdn77\Api\Core\Application\OpenApi\PathGenerator;
use Cdn77\Api\Core\Application\Payload\ErrorsSchema;
use Cdn77\Api\Core\Application\Request\ControllerSchemaSerializer;
use Cdn77\Api\Core\Application\Response\ControllerCommandHandler;
use Cdn77\Api\Core\Domain\Entity\Cdn\CdnId;
use cebe\openapi\spec\MediaType;
use cebe\openapi\spec\Operation;
use cebe\openapi\spec\Parameter;
use cebe\openapi\spec\PathItem;
use cebe\openapi\spec\RequestBody;
use cebe\openapi\spec\Response as OpenApiResponse;
use cebe\openapi\spec\Responses;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;

final readonly class SuspendController implements HasInternalOpenApiPaths, RequiresInternalTokenAuthentication
{
    public const string RouteName = 'cdn.suspend';
    private const string RouteSummary = 'Suspend CDN Resource';

    public function __construct(
        private ControllerCommandHandler $controllerCommandHandler,
        private ControllerSchemaSerializer $controllerSchemaSerializer,
        private PathGenerator $pathGenerator,
    ) {
    }

    #[Route(path: '/cdn/{id}/suspend', name: self::RouteName, methods: [Request::METHOD_POST])]
    public function execute(Request $request, CdnId $id): Response
    {
        $result = $this->controllerSchemaSerializer->deserialize($request, SuspendCdnSchema::class);

        if ($result instanceof ErrorsSchema) {
            return $this->controllerSchemaSerializer->serializeToResponse($result, Response::HTTP_UNPROCESSABLE_ENTITY);
        }

        return $this->controllerCommandHandler->handle(SuspendCdn::fromCdnSuspensionDetails($id, $result));
    }

    public function getPathItems(): array
    {
        $post = new Operation([
            'operationId' => self::RouteName,
            'tags' => [Tags::CdnResources],
            'summary' => self::RouteSummary,
            'parameters' => [
                new Parameter([
                    'name' => 'id',
                    'in' => 'path',
                    'required' => true,
                    'schema' => CdnId::getSchemaSpec(),
                ]),
            ],
            'requestBody' => new RequestBody(
                [
                    'required' => true,
                    'content' => [
                        'application/json' => new MediaType(
                            [
                                'schema' => SuspendCdnSchema::getSchemaSpec(),
                            ],
                        ),
                    ],
                ],
            ),
            'responses' => new Responses(
                [
                    Response::HTTP_NO_CONTENT => new OpenApiResponse(
                        ['description' => 'The CDN Resource was suspended.'],
                    ),
                    Response::HTTP_UNPROCESSABLE_ENTITY => FieldsErrorsOpenApiResponse::spec('Invalid input.'),
                    Response::HTTP_NOT_FOUND => ErrorsOpenApiResponse::spec(
                        'Suspension of the CDN Resource failed.',
                    ),
                    'default' => ErrorsOpenApiResponse::spec('Unknown error occurred.'),
                ],
            ),
        ]);

        return [$this->pathGenerator->generate(self::RouteName) => new PathItem(['post' => $post])];
    }
}
