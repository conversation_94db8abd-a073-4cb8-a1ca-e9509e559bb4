<?php

declare(strict_types=1);

namespace Cdn77\Api\Cdn\Application\Controller\Log;

use Cdn77\Api\Cdn\Application\Payload\Log\LoggedRequestsCountSchema;
use Cdn77\Api\Cdn\Domain\Query\Log\GetSum;
use Cdn77\Api\Core\Application\Controller\HasInternalOpenApiPaths;
use Cdn77\Api\Core\Application\OpenApi\Model\Tags;
use Cdn77\Api\Core\Application\OpenApi\PathGenerator;
use Cdn77\Api\Core\Application\OpenApi\Paths\JsonResponseSpec;
use Cdn77\Api\Core\Application\OpenApi\Schema\DateTimeSchema;
use Cdn77\Api\Core\Application\Payload\DateTimeRangeSchema;
use Cdn77\Api\Core\Application\Payload\ErrorsSchema;
use Cdn77\Api\Core\Application\Request\ControllerSchemaSerializer;
use Cdn77\Api\Core\Application\Response\ControllerQueryHandler;
use Cdn77\Api\Core\Application\Symfony\LoggedAccountProvider;
use cebe\openapi\spec\Operation;
use cebe\openapi\spec\Parameter;
use cebe\openapi\spec\PathItem;
use cebe\openapi\spec\Responses;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;

final readonly class SumController implements HasInternalOpenApiPaths
{
    public const string RouteName = 'cdn.real-time-logs.sum';

    public function __construct(
        private ControllerSchemaSerializer $controllerSchemaSerializer,
        private ControllerQueryHandler $controllerQueryHandler,
        private LoggedAccountProvider $loggedAccountProvider,
        private PathGenerator $pathGenerator,
    ) {
    }

    #[Route(path: '/cdn/real-time-logs/sum', name: self::RouteName, methods: [Request::METHOD_GET])]
    public function execute(Request $request): JsonResponse
    {
        $result = $this->controllerSchemaSerializer->deserializeQueryString($request, DateTimeRangeSchema::class);

        if ($result instanceof ErrorsSchema) {
            return $this->controllerSchemaSerializer->serializeToResponse($result, Response::HTTP_UNPROCESSABLE_ENTITY);
        }

        return $this->controllerQueryHandler->handle(
            new GetSum(
                $this->loggedAccountProvider->customerId(),
                $result,
            ),
            LoggedRequestsCountSchema::class,
        );
    }

    public function getPathItems(): array
    {
        $get = new Operation([
            'tags' => [Tags::CdnResources],
            'summary' => 'Get sum of real-time logged requests',
            'description' => 'Returns the total number of logged requests in the specified time range.',
            'operationId' => self::RouteName,
            'responses' => new Responses([
                Response::HTTP_OK => JsonResponseSpec::create(
                    'Successful response',
                    LoggedRequestsCountSchema::getSchemaSpec(),
                ),
            ]),
            'parameters' => [
                new Parameter([
                    'name' => 'from',
                    'in' => 'query',
                    'required' => true,
                    'schema' => DateTimeSchema::spec(),
                    'description' => 'Start of the time range in ISO 8601 format.',
                ]),
                new Parameter([
                    'name' => 'to',
                    'in' => 'query',
                    'required' => true,
                    'schema' => DateTimeSchema::spec(),
                    'description' => 'End of the time range in ISO 8601 format.',
                ]),
            ],
        ]);

        return [$this->pathGenerator->generate(self::RouteName) => new PathItem(['get' => $get])];
    }
}
