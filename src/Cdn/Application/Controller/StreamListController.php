<?php

declare(strict_types=1);

namespace Cdn77\Api\Cdn\Application\Controller;

use Cdn77\Api\Cdn\Application\Payload\StreamCdnsSchema;
use Cdn77\Api\Cdn\Domain\Query\FindStreamCdnsForCustomer;
use Cdn77\Api\Core\Application\Controller\HasInternalOpenApiPaths;
use Cdn77\Api\Core\Application\OpenApi\ErrorsOpenApiResponse;
use Cdn77\Api\Core\Application\OpenApi\Model\Tags;
use Cdn77\Api\Core\Application\OpenApi\PathGenerator;
use Cdn77\Api\Core\Application\Response\ControllerQueryHandler;
use Cdn77\Api\Core\Application\Symfony\LoggedAccountProvider;
use cebe\openapi\spec\MediaType;
use cebe\openapi\spec\Operation;
use cebe\openapi\spec\PathItem;
use cebe\openapi\spec\Response as OpenApiResponse;
use cebe\openapi\spec\Responses;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;

final readonly class StreamListController implements HasInternalOpenApiPaths
{
    public const string RouteName = 'cdn.stream.list';
    private const string RouteSummary = 'List of Stream CDN Resources';

    public function __construct(
        private ControllerQueryHandler $controllerQueryHandler,
        private LoggedAccountProvider $loggedAccountProvider,
        private PathGenerator $pathGenerator,
    ) {
    }

    #[Route(path: '/stream-cdn', name: self::RouteName, methods: [Request::METHOD_GET])]
    public function execute(Request $request): Response
    {
        $customerId = $this->loggedAccountProvider->legacyId();

        return $this->controllerQueryHandler->handle(
            new FindStreamCdnsForCustomer($customerId),
            StreamCdnsSchema::class,
        );
    }

    public function getPathItems(): array
    {
        $get = new Operation([
            'operationId' => self::RouteName,
            'tags' => [Tags::CdnResources],
            'summary' => self::RouteSummary,
            'responses' => new Responses(
                [
                    Response::HTTP_OK => new OpenApiResponse(
                        [
                            'description' => 'List of Stream CDN Resources',
                            'content' => [
                                'application/json' => new MediaType([
                                    'schema' => StreamCdnsSchema::getSchemaSpec(),
                                ]),
                            ],
                        ],
                    ),
                    'default' => ErrorsOpenApiResponse::spec('Unknown error occurred.'),
                ],
            ),
        ]);

        return [$this->pathGenerator->generate(self::RouteName) => new PathItem(['get' => $get])];
    }
}
