<?php

declare(strict_types=1);

namespace Cdn77\Api\Cdn\Application\Controller;

use Cdn77\Api\Cdn\Application\Payload\ChangePreferredLocationGroupSchema;
use Cdn77\Api\Cdn\Domain\Command\ChangePreferredLocationGroup;
use Cdn77\Api\Core\Application\Controller\HasInternalOpenApiPaths;
use Cdn77\Api\Core\Application\Controller\RequiresInternalTokenAuthentication;
use Cdn77\Api\Core\Application\OpenApi\ErrorsOpenApiResponse;
use Cdn77\Api\Core\Application\OpenApi\Model\Tags;
use Cdn77\Api\Core\Application\OpenApi\PathGenerator;
use Cdn77\Api\Core\Application\Payload\ErrorsSchema;
use Cdn77\Api\Core\Application\Request\ControllerSchemaSerializer;
use Cdn77\Api\Core\Application\Response\ControllerCommandHandler;
use cebe\openapi\spec\MediaType;
use cebe\openapi\spec\Operation;
use cebe\openapi\spec\PathItem;
use cebe\openapi\spec\RequestBody;
use cebe\openapi\spec\Responses;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;

final readonly class ChangePreferredLocationGroupController
    implements HasInternalOpenApiPaths, RequiresInternalTokenAuthentication
{
    public const string RouteName = 'cdn.change-preferred-location-group';

    public function __construct(
        private ControllerCommandHandler $controllerCommandHandler,
        private ControllerSchemaSerializer $controllerSchemaSerializer,
        private PathGenerator $pathGenerator,
    ) {
    }

    #[Route(path: '/cdn/change-preferred-location-group', name: self::RouteName, methods: [Request::METHOD_PUT])]
    public function execute(Request $request): Response
    {
        $result = $this->controllerSchemaSerializer->deserialize($request, ChangePreferredLocationGroupSchema::class);

        if ($result instanceof ErrorsSchema) {
            return $this->controllerSchemaSerializer->serializeToResponse($result, Response::HTTP_UNPROCESSABLE_ENTITY);
        }

        return $this->controllerCommandHandler->handle(ChangePreferredLocationGroup::fromDto($result));
    }

    public function getPathItems(): array
    {
        $put = new Operation([
            'operationId' => self::RouteName,
            'tags' => [Tags::CdnResources],
            'responses' => new Responses([
                Response::HTTP_NO_CONTENT => new \cebe\openapi\spec\Response(
                    ['description' => 'Preferred location group was changed.'],
                ),
                'default' => ErrorsOpenApiResponse::spec('Unknown error occurred.'),
            ]),
            'requestBody' => new RequestBody([
                'required' => true,
                'content' => [
                    'application/json' => new MediaType(
                        ['schema' => ChangePreferredLocationGroupSchema::getSchemaSpec()],
                    ),
                ],
            ]),
        ]);

        return [$this->pathGenerator->generate(self::RouteName) => new PathItem(['put' => $put])];
    }
}
