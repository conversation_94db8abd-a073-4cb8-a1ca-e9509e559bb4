<?php

declare(strict_types=1);

namespace Cdn77\Api\Cdn\Application\Payload\MP4PseudoStreaming;

use Cdn77\Api\Cdn\Application\Docs\MP4PseudoStreaming\MP4PseudoStreamingSchemaSpec;
use Cdn77\Api\Cdn\Domain\Dto\Mp4PseudoStreamingSettings;
use Cdn77\Api\Core\Application\OpenApi\Model\ComponentReference;
use Cdn77\Api\Core\Application\Payload\HasReference;
use Cdn77\Api\Core\Application\Payload\SchemaPropertyResolver;
use Cdn77\Api\Core\Application\Payload\SchemaPropertyValidator;
use Cdn77\Api\Core\Application\Payload\SupportsDtoDeserialization;
use cebe\openapi\exceptions\TypeErrorException;
use cebe\openapi\spec\Reference;

/** @implements SupportsDtoDeserialization<Mp4PseudoStreamingSettings> */
final class MP4PseudoStreamingSchema implements SupportsDtoDeserialization, HasReference
{
    public const string FieldEnabled = 'enabled';

    private bool|null $enabled = null;

    private function __construct(bool $isEnabled)
    {
        $this->enabled = $isEnabled;
    }

    public static function fromSettings(Mp4PseudoStreamingSettings $mp4PseudoStreamingSettings): self
    {
        return new self($mp4PseudoStreamingSettings->isEnabled);
    }

    /** @throws TypeErrorException */
    public static function reference(): Reference
    {
        return ComponentReference::forSchema(MP4PseudoStreamingSchemaSpec::class);
    }

    public function validateSchemaProperties(): iterable
    {
        yield SchemaPropertyValidator::isNotNull($this->enabled, 'mp4_pseudo_streaming.enabled');
    }

    public function toDto(): Mp4PseudoStreamingSettings
    {
        return new Mp4PseudoStreamingSettings(SchemaPropertyResolver::requireNotNull($this->enabled));
    }
}
