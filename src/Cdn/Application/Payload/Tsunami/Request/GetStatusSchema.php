<?php

declare(strict_types=1);

namespace Cdn77\Api\Cdn\Application\Payload\Tsunami\Request;

use Cdn77\Api\Cdn\Domain\Dto\Tsunami\GetTargetStatusScope;
use Cdn77\Api\Core\Application\Payload\OASchema;
use Cdn77\Api\Core\Application\Payload\SchemaPropertyResolver;
use Cdn77\Api\Core\Application\Payload\SchemaPropertyValidator;
use Cdn77\Api\Core\Application\Payload\SupportsDeserialization;
use Cdn77\Api\Core\Domain\Entity\Cdn\CdnId;
use Cdn77\Api\Core\Domain\Hurricane\Tsunami\Target;
use cebe\openapi\spec\Schema;
use cebe\openapi\spec\Type;
use JMS\Serializer\Annotation as Serializer;

use function Cdn77\Functions\setFromIterable;

final class GetStatusSchema implements OASchema, SupportsDeserialization
{
    public const string FieldCdnIds = 'cdn_ids';
    public const string FieldTargets = 'targets';

    /** @var array<int>|null */
    #[Serializer\Type('array<int>')]
    public array|null $cdnIds = null;

    /** @var list<TargetSchema>|null */
    #[Serializer\Type('array<' . TargetSchema::class . '>')]
    public array|null $targets = null;

    private function __construct()
    {
    }

    public static function getSchemaSpec(): Schema
    {
        return new Schema([
            'type' => Type::OBJECT,
            'properties' => [
                self::FieldCdnIds => [
                    'type' => Type::ARRAY,
                    'items' => ['type' => Type::INTEGER],
                ],
                self::FieldTargets => [
                    'type' => Type::ARRAY,
                    'items' => TargetSchema::getSchemaSpec(),
                ],
            ],
        ]);
    }

    public function validateSchemaProperties(): iterable
    {
        if ($this->targets !== null) {
            SchemaPropertyValidator::minCount(
                $this->targets,
                1,
                'This field must contain at least 1 Target.',
                self::FieldTargets,
            );

            foreach ($this->targets as $target) {
                yield from $target->validateSchemaProperties();
            }

            return;
        }

        yield SchemaPropertyValidator::isNotNull($this->cdnIds, self::FieldCdnIds);

        if ($this->cdnIds === null) {
            return;
        }

        yield SchemaPropertyValidator::minCount(
            $this->cdnIds,
            1,
            'This field must contain at least 1 CDN.',
            self::FieldCdnIds,
        );
    }

    public function toDto(): GetTargetStatusScope
    {
        return new GetTargetStatusScope(
            setFromIterable(
                $this->targets ?? [],
                static fn (mixed $_, TargetSchema $schema): Target => $schema->toDto(),
            ),
            CdnId::fromIntegers(SchemaPropertyResolver::requireNotNull($this->cdnIds)),
        );
    }
}
