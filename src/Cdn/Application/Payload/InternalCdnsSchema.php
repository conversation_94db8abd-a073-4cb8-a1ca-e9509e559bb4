<?php

declare(strict_types=1);

namespace Cdn77\Api\Cdn\Application\Payload;

use Cdn77\Api\Cdn\Domain\Dto\CdnReference;
use Cdn77\Api\Core\Application\Payload\OASchema;
use Cdn77\Api\Core\Application\Payload\QueryBusResultSchema;
use cebe\openapi\spec\Schema;
use cebe\openapi\spec\Type;
use Generator;
use JMS\Serializer\Annotation as Serializer;
use Webmozart\Assert\Assert;

final class InternalCdnsSchema implements OASchema, QueryBusResultSchema
{
    /** @var array<InternalCdnSchema> */
    #[Serializer\Type('array<' . InternalCdnSchema::class . '>')]
    #[Serializer\Inline]
    public array $cdns;

    /** @param list<InternalCdnSchema> $cdns */
    private function __construct(array $cdns)
    {
        $this->cdns = $cdns;
    }

    public static function fromQueryBusResult(mixed $result): self
    {
        Assert::isInstanceOf($result, Generator::class);

        return self::fromCdnReferences($result);
    }

    /** @param Generator<CdnReference> $cdnReferences */
    public static function fromCdnReferences(Generator $cdnReferences): self
    {
        $schema = new self([]);

        foreach ($cdnReferences as $cdnReference) {
            $schema->add(InternalCdnSchema::fromCdnReference($cdnReference));
        }

        return $schema;
    }

    public static function getSchemaSpec(): Schema
    {
        return new Schema([
            'type' => Type::ARRAY,
            'items' => InternalCdnSchema::getSchemaSpec(),
        ]);
    }

    public function add(InternalCdnSchema $cdn): void
    {
        $this->cdns[] = $cdn;
    }
}
