<?php

declare(strict_types=1);

namespace Cdn77\Api\Cdn\Application\Payload\Log;

use Cdn77\Api\Cdn\Domain\Query\Log\PreviewRealTimeLog;
use Cdn77\Api\Core\Application\OpenApi\Schema\IntegerSchema;
use Cdn77\Api\Core\Application\OpenApi\Schema\ObjectSchema;
use Cdn77\Api\Core\Application\Payload\OASchema;
use Cdn77\Api\Core\Application\Payload\SchemaPropertyValidator;
use Cdn77\Api\Core\Application\Payload\SupportsDeserialization;
use Cdn77\Api\Core\Domain\Entity\Customer\CustomerUuid;
use Cdn77\Api\Core\Domain\Entity\RealTimeLog\RealTimeLogId;
use cebe\openapi\spec\Schema;
use Generator;

final class PreviewRealTimeLogScope implements OASchema, SupportsDeserialization
{
    public const string FieldLimit = 'limit';

    public int|null $limit = null;

    private function __construct()
    {
    }

    public static function getSchemaSpec(): Schema
    {
        return ObjectSchema::spec([
            self::FieldLimit => IntegerSchema::spec(),
        ]);
    }

    public function validateSchemaProperties(): Generator
    {
        if ($this->limit === null) {
            return;
        }

        yield SchemaPropertyValidator::range(
            $this->limit,
            1,
            1000,
            self::FieldLimit,
        );
    }

    public function toCommand(RealTimeLogId $logId, CustomerUuid $customerId): PreviewRealTimeLog
    {
        return new PreviewRealTimeLog(
            $logId,
            $customerId,
            $this->limit,
        );
    }
}
