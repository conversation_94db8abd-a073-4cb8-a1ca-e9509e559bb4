<?php

declare(strict_types=1);

namespace Cdn77\Api\Cdn\Application\Payload\Log;

use Cdn77\Api\Cdn\Domain\Value\Log\LoggedRequestsPricing;
use Cdn77\Api\Core\Application\OpenApi\Schema\IntegerSchema;
use Cdn77\Api\Core\Application\OpenApi\Schema\ObjectSchema;
use Cdn77\Api\Core\Application\Payload\MoneySchema;
use Cdn77\Api\Core\Application\Payload\OASchema;
use cebe\openapi\spec\Schema;

final class RealTimeLoggedRequestsPricingSchema implements OASchema
{
    public const string FieldInitialPrice = 'initial_price';
    public const string FieldIncludedInPriceCount = 'included_in_price_count';
    public const string FieldPricePerMillion = 'price_per_million';

    private function __construct(
        public MoneySchema $initialPrice,
        public int $includedInPriceCount,
        public MoneySchema $pricePerMillion,
    ) {
    }

    public static function fromDto(LoggedRequestsPricing $dto): self
    {
        return new self(
            MoneySchema::fromDto($dto->initialPrice),
            $dto->includedInPriceCount->toInt(),
            MoneySchema::fromDto($dto->pricePerMillion),
        );
    }

    public static function getSchemaSpec(): Schema
    {
        return ObjectSchema::spec([
            self::FieldInitialPrice => MoneySchema::getSchemaSpec(),
            self::FieldIncludedInPriceCount => IntegerSchema::spec(),
            self::FieldPricePerMillion => MoneySchema::getSchemaSpec(),
        ]);
    }
}
