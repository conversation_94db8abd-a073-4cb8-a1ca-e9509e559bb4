<?php

declare(strict_types=1);

namespace Cdn77\Api\Cdn\Application\Payload\Log;

use Cdn77\Api\Cdn\Domain\Dto\Log\OutputDestinationConfig;
use Cdn77\Api\Cdn\Domain\Value\Log\OutputDestinationType;
use Cdn77\Api\Core\Application\OpenApi\Schema\ObjectSchema;
use Cdn77\Api\Core\Application\Payload\OASchema;
use Cdn77\Api\Core\Application\Payload\SchemaPropertyResolver;
use Cdn77\Api\Core\Application\Payload\SchemaPropertyValidator;
use Cdn77\Api\Core\Application\Payload\SupportsDtoDeserialization;
use cebe\openapi\spec\Schema;

/** @implements SupportsDtoDeserialization<OutputDestinationConfig> */
final class OutputDestinationConfigSchema implements OASchema, SupportsDtoDeserialization
{
    public const string FieldType = 'type';

    public string|null $type = null;

    public static function getSchemaSpec(): Schema
    {
        return ObjectSchema::spec([
            self::FieldType => OutputDestinationType::getReference(),
        ]);
    }

    public function validateSchemaProperties(): iterable
    {
        yield SchemaPropertyValidator::isNotNull($this->type, self::FieldType);
        yield SchemaPropertyValidator::isValidEnumCase($this->type, OutputDestinationType::class, self::FieldType);
    }

    public function toDto(): OutputDestinationConfig
    {
        return new OutputDestinationConfig(
            OutputDestinationType::from(SchemaPropertyResolver::requireNotNull($this->type)),
        );
    }
}
