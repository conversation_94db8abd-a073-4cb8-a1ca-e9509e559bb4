<?php

declare(strict_types=1);

namespace Cdn77\Api\Cdn\Application\Payload\AccessProtection;

use Cdn77\Api\Cdn\Application\Docs\AccessProtection\IpProtectionSchemaSpec;
use Cdn77\Api\Cdn\Domain\Dto\AccessProtection\IpProtection;
use Cdn77\Api\Cdn\Domain\Value\AccessProtectionType;
use Cdn77\Api\Core\Application\OpenApi\Model\ComponentReference;
use Cdn77\Api\Core\Application\Payload\HasReference;
use Cdn77\Api\Core\Application\Payload\SchemaPropertyResolver;
use Cdn77\Api\Core\Application\Payload\SchemaPropertyValidator;
use Cdn77\Api\Core\Application\Payload\SupportsDtoDeserialization;
use Cdn77\Api\Core\Domain\Value\Cidr;
use cebe\openapi\exceptions\TypeErrorException;
use cebe\openapi\spec\Reference;
use J<PERSON>\Serializer\Annotation as Serializer;

use function array_map;

/** @implements SupportsDtoDeserialization<IpProtection> */
final class IpProtectionSchema implements SupportsDtoDeserialization, HasReference
{
    public const string FieldIps = 'ips';
    public const string FieldType = 'type';

    /** @var list<string>|null */
    #[Serializer\Type('array<string>')]
    private array|null $ips = null;

    private AccessProtectionType|null $type = null;

    /** @param list<string>|null $ips */
    private function __construct(AccessProtectionType $type, array|null $ips)
    {
        $this->ips = $ips;
        $this->type = $type->toPublic();
    }

    public static function fromSettings(IpProtection $ipProtection): self
    {
        return new self(
            $ipProtection->getType(),
            $ipProtection->getIps()?->map(
                static fn (Cidr $cidr) => $cidr->value,
            )->toArray(),
        );
    }

    /** @throws TypeErrorException */
    public static function reference(): Reference
    {
        return ComponentReference::forSchema(IpProtectionSchemaSpec::class);
    }

    public function validateSchemaProperties(): iterable
    {
        yield SchemaPropertyValidator::isNotNull($this->type, 'ip_protection.' . self::FieldType);

        if ($this->ips === null) {
            return;
        }

        yield SchemaPropertyValidator::allCidr($this->ips, 'ip_protection.' . self::FieldIps);
    }

    public function toDto(): IpProtection
    {
        return new IpProtection(
            SchemaPropertyResolver::requireNotNull($this->type),
            $this->ips === null
                ? null
                : array_map(
                    static fn (string $ippAddress): Cidr => Cidr::fromString($ippAddress),
                    $this->ips,
                ),
        );
    }
}
