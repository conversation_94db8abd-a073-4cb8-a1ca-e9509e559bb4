<?php

declare(strict_types=1);

namespace Cdn77\Api\Cdn\Application\Payload\Cache;

use Cdn77\Api\Cdn\Domain\Dto\MaxAgeValues;
use Cdn77\Api\Cdn\Domain\Value\MaxAge;
use Cdn77\Api\Cdn\Domain\Value\MaxAge404;
use Cdn77\Api\Core\Application\Payload\OASchema;
use Cdn77\Api\Core\Application\Payload\QueryBusResultSchema;
use cebe\openapi\spec\Schema;
use cebe\openapi\spec\Type;
use JMS\Serializer\Annotation as Serializer;
use Webmozart\Assert\Assert;

final readonly class MaxAgeValuesListSchema implements QueryBusResultSchema, OASchema
{
    public const string FieldMaxAge = 'max_age';
    public const string FieldMaxAge404 = 'max_age_404';

    /**
     * @param list<int> $maxAge
     * @param list<int> $maxAge404
     */
    private function __construct(
        #[Serializer\SerializedName(self::FieldMaxAge)]
        public array $maxAge,
        #[Serializer\SerializedName(self::FieldMaxAge404)]
        public array $maxAge404,
    ) {
    }

    public static function fromDto(MaxAgeValues $maxAgeValues): self
    {
        return new self(
            $maxAgeValues->maxAge->map(static fn (MaxAge $maxAge): int => $maxAge->minutes)->toArray(),
            $maxAgeValues->maxAge404->map(static fn (MaxAge404 $maxAge404): int => $maxAge404->seconds)->toArray(),
        );
    }

    public static function fromQueryBusResult(mixed $result): self
    {
        Assert::isInstanceOf($result, MaxAgeValues::class);

        return self::fromDto($result);
    }

    public static function getSchemaSpec(): Schema
    {
        return new Schema([
            'type' => Type::OBJECT,
            'properties' => [
                self::FieldMaxAge => new Schema(
                    [
                        'description' => 'In minutes.',
                        'items' => ['type' => Type::INTEGER],
                        'type' => Type::ARRAY,
                    ],
                ),
                self::FieldMaxAge404 => new Schema(
                    [
                        'description' => 'In seconds.',
                        'items' => ['type' => Type::INTEGER],
                        'type' => Type::ARRAY,
                    ],
                ),
            ],
        ]);
    }
}
