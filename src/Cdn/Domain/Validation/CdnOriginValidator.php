<?php

declare(strict_types=1);

namespace Cdn77\Api\Cdn\Domain\Validation;

use Cdn77\Api\Core\Domain\Dto\Origin\CdnOrigin;
use Cdn77\Api\Core\Domain\Dto\Origin\ObjectStorageOrigin;
use Cdn77\Api\Core\Domain\Entity\Customer\CustomerId;
use Cdn77\Api\Core\Domain\Origin\Exception\OriginIsInvalid;
use Cdn77\Api\Core\Domain\Origin\Exception\OriginUrlIsForbidden;
use Cdn77\Api\Core\Domain\Origin\Exception\OriginUrlIsInvalid;
use Cdn77\Api\Core\Domain\Validation\OriginUrlValidator;
use Cdn77\Api\Core\Domain\Value\Origin\ObjectStorageType;

final readonly class CdnOriginValidator
{
    public function __construct(
        private OriginUrlValidator $originUrlValidator,
    ) {
    }

    /**
     * @throws OriginIsInvalid
     * @throws OriginUrlIsForbidden
     * @throws OriginUrlIsInvalid
     */
    public function validate(CdnOrigin $origin, CustomerId $customerId): void
    {
        if ($origin instanceof ObjectStorageOrigin && $origin->objectStorageType === ObjectStorageType::RealTimeLog) {
            throw OriginIsInvalid::realTimeLogOrigin();
        }

        $this->originUrlValidator->validate($origin, $customerId);
    }
}
