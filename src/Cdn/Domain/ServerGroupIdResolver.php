<?php

declare(strict_types=1);

namespace Cdn77\Api\Cdn\Domain;

use Cdn77\Api\Core\Domain\Entity\Customer\CustomerId;
use Cdn77\Api\Core\Domain\Entity\Datacenter\ServerGroupId;
use Cdn77\Api\Core\Domain\Repository\Customer\AccountSettingsRepository;

final readonly class ServerGroupIdResolver
{
    public function __construct(private AccountSettingsRepository $accountSettingsRepository)
    {
    }

    public function resolve(CustomerId $customerId): ServerGroupId
    {
        $accountSettings = $this->accountSettingsRepository->getForCustomer($customerId);

        return $accountSettings->getPreferredServerGroupId();
    }
}
