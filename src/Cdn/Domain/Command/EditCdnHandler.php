<?php

declare(strict_types=1);

namespace Cdn77\Api\Cdn\Domain\Command;

use Cdn77\Api\Cdn\Domain\AccessProtection\SetupGeoProtection;
use Cdn77\Api\Cdn\Domain\AccessProtection\SetupHotlinkProtection;
use Cdn77\Api\Cdn\Domain\AccessProtection\SetupIpProtection;
use Cdn77\Api\Cdn\Domain\Configuration\CdnCnamesConfigurator;
use Cdn77\Api\Cdn\Domain\Configuration\CdnHttpConfigurator;
use Cdn77\Api\Cdn\Domain\Configuration\CdnSslConfigurator;
use Cdn77\Api\Cdn\Domain\Exception\CdnAccessNotAllowed;
use Cdn77\Api\Cdn\Domain\Exception\CdnUpdateFailed;
use Cdn77\Api\Cdn\Domain\Exception\InstantSslToggleFailed;
use Cdn77\Api\Cdn\Domain\Exception\InvalidAccessProtectionSetup;
use Cdn77\Api\Cdn\Domain\QueryString\SetupIgnoredQueryParams;
use Cdn77\Api\Cdn\Domain\Validation\CdnLabelValidator;
use Cdn77\Api\Cname\Domain\Exception\CnameIsForbidden;
use Cdn77\Api\Cname\Domain\Exception\CnameLimitReached;
use Cdn77\Api\Cname\Domain\Exception\CnamesAreInUse;
use Cdn77\Api\Core\Domain\Cdn\Configuration\OriginConfigurator;
use Cdn77\Api\Core\Domain\ChangeLog;
use Cdn77\Api\Core\Domain\Entity\TikTok\ChangeRequest;
use Cdn77\Api\Core\Domain\Exception\Cdn\CdnOriginConfigurationFailed;
use Cdn77\Api\Core\Domain\Exception\Customer\CustomerNotFound;
use Cdn77\Api\Core\Domain\Messaging\CommandHandler;
use Cdn77\Api\Core\Domain\NxgApi\Exception\NxgRequestFailed;
use Cdn77\Api\Core\Domain\NxgApi\NxgApi;
use Cdn77\Api\Core\Domain\Origin\Exception\OriginIsInvalid;
use Cdn77\Api\Core\Domain\Origin\Exception\OriginNotFound;
use Cdn77\Api\Core\Domain\Origin\Exception\OriginUrlIsForbidden;
use Cdn77\Api\Core\Domain\Origin\Exception\OriginUrlIsInvalid;
use Cdn77\Api\Core\Domain\Origin\OriginResolver;
use Cdn77\Api\Core\Domain\Repository\Cdn\CdnHttpRepository;
use Cdn77\Api\Core\Domain\Repository\Cdn\CdnRepository;
use Cdn77\Api\Core\Domain\Repository\CustomerRepository;
use Cdn77\Api\Core\Domain\Validation\CdnAccessValidator;
use Cdn77\Api\Core\Domain\Value\AccessRestriction\AccessType;
use Cdn77\Api\Ssl\Domain\Exception\SslNotFound;
use Cdn77\Api\TikTok\Domain\Value\Action;
use Psl\Json\Exception\EncodeException;
use Psr\Clock\ClockInterface;
use Webmozart\Assert\Assert;

use function Psl\Json\encode;

final readonly class EditCdnHandler implements CommandHandler
{
    public function __construct(
        private CdnAccessValidator $cdnAccessValidator,
        private CdnCnamesConfigurator $cdnCnamesConfigurator,
        private CdnHttpConfigurator $cdnHttpConfigurator,
        private CdnHttpRepository $cdnHttpRepository,
        private CdnLabelValidator $cdnLabelValidator,
        private CdnRepository $cdnRepository,
        private CdnSslConfigurator $cdnSslConfigurator,
        private ClockInterface $clock,
        private ChangeLog $changeLog,
        private CustomerRepository $customerRepository,
        private NxgApi $nxgApi,
        private OriginConfigurator $originConfigurator,
        private OriginResolver $originResolver,
        private SetupGeoProtection $setupGeoProtection,
        private SetupHotlinkProtection $setupHotlinkProtection,
        private SetupIgnoredQueryParams $setupIgnoredQueryParams,
        private SetupIpProtection $setupIpProtection,
    ) {
    }

    /**
     * @throws CdnAccessNotAllowed
     * @throws CdnOriginConfigurationFailed
     * @throws CdnUpdateFailed
     * @throws CnamesAreInUse
     * @throws CnameIsForbidden
     * @throws CnameLimitReached
     * @throws CustomerNotFound
     * @throws EncodeException
     * @throws InstantSslToggleFailed
     * @throws InvalidAccessProtectionSetup
     * @throws NxgRequestFailed
     * @throws OriginNotFound
     * @throws OriginIsInvalid
     * @throws OriginUrlIsForbidden
     * @throws OriginUrlIsInvalid
     * @throws SslNotFound
     */
    public function handle(EditCdn $command): void
    {
        $now = $this->clock->now();
        $cdnId = $command->cdnId;
        $customer = $this->customerRepository->get($command->customerId);
        $cdn = $this->cdnRepository->get($cdnId);
        $customerId = $command->customerId;

        if (! $this->cdnAccessValidator->isAllowed($cdn, $customerId, AccessType::CdnEdit)) {
            throw CdnAccessNotAllowed::forCdnId($cdn->getId());
        }

        $affectedCustomerId = $cdn->getOwner()->getNewId();
        $cdnHttp = $this->cdnHttpRepository->getForCdn($cdnId);

        $originsByPriority = $command->originId === null
            ? $this->originResolver->resolveForCdnWithFallbacks($cdn->getId())
            : $this->originConfigurator->configure($cdn, $cdnHttp, $command->originId, $customer);
        $primaryOrigin = $originsByPriority->primary();

        $this->cdnHttpConfigurator->configure(
            $cdnId,
            $cdnHttp,
            $primaryOrigin,
            $command->httpSettings,
            $now,
            $affectedCustomerId,
        );

        $hasUpdatedLabel = $command->label !== null;
        $hasUpdatedNote = $command->note !== null;
        $requiresCnameUpdate = $command->cnames !== null;
        $requiresGeoProtectionSetup = $command->geoProtection !== null;
        $requiresHotlinkProtectionSetup = $command->hotlinkProtection !== null;
        $requiresIpProtectionSetup = $command->ipProtection !== null;
        $requiresQueryStringSetup = $command->httpSettings->queryStringSettings !== null;
        $requiresSslSetup = $command->ssl !== null;

        if ($hasUpdatedLabel) {
            Assert::notNull($command->label);
            $cdn->rename($command->label, $this->cdnLabelValidator);
        }

        if ($hasUpdatedNote) {
            Assert::notNull($command->note);
            $cdn->updateNote($command->note);
        }

        if ($requiresCnameUpdate) {
            Assert::notNull($command->cnames);
            $this->cdnCnamesConfigurator->configure($command->cnames, $cdn);
        }

        if ($requiresGeoProtectionSetup) {
            Assert::notNull($command->geoProtection);
            $this->setupGeoProtection->execute($cdn, $command->geoProtection);
        }

        if ($requiresHotlinkProtectionSetup) {
            Assert::notNull($command->hotlinkProtection);
            $this->setupHotlinkProtection->execute($cdn, $command->hotlinkProtection, $now);
        }

        if ($requiresIpProtectionSetup) {
            Assert::notNull($command->ipProtection);
            $this->setupIpProtection->execute($cdn, $command->ipProtection);
        }

        if ($requiresQueryStringSetup) {
            Assert::notNull($command->httpSettings->queryStringSettings);
            $this->setupIgnoredQueryParams->execute($cdn, $now, $command->httpSettings->queryStringSettings);
        }

        $ssl = null;
        if ($requiresSslSetup) {
            Assert::notNull($command->ssl);

            $ssl = $this->cdnSslConfigurator->configure($cdn, $customerId, $command->ssl, $now);
        }

        $this->changeLog->putForCustomer(
            $customer,
            new ChangeRequest(
                Action::Update,
                $now,
                $customer->getNewId(),
                $cdnId,
                encode($command),
            ),
        );

        $this->nxgApi->updateCdn(
            $cdn,
            $cdnHttp,
            $originsByPriority,
            $command->cnames,
            $command->geoProtection,
            $command->hotlinkProtection,
            $command->ipProtection,
            $command->httpSettings->queryStringSettings,
            $ssl,
        );
    }
}
