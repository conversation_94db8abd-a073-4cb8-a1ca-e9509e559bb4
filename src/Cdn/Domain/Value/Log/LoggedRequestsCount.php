<?php

declare(strict_types=1);

namespace Cdn77\Api\Cdn\Domain\Value\Log;

use Brick\Math\BigInteger;
use Brick\Math\BigRational;
use Brick\Math\Exception\MathException;

use function Cdn77\Functions\absurd;

final readonly class LoggedRequestsCount
{
    public function __construct(
        public BigInteger $value,
    ) {
    }

    public static function zero(): self
    {
        return new self(BigInteger::zero());
    }

    public static function fromBillions(int $billions): self
    {
        try {
            return new self(BigInteger::of($billions * 1_000_000_000));
        } catch (MathException) {
            absurd();
        }
    }

    public static function fromThousands(int $thousands): self
    {
        try {
            return new self(BigInteger::of($thousands * 1_000));
        } catch (MathException) {
            absurd();
        }
    }

    public function minus(self $loggedRequestsCount): self
    {
        try {
            return new self($this->value->minus($loggedRequestsCount->value));
        } catch (MathException) {
            absurd();
        }
    }

    public function toMillions(): BigRational
    {
        try {
            return BigRational::nd($this->value, BigInteger::of(1_000_000));
        } catch (MathException) {
            absurd();
        }
    }

    public function toInt(): int
    {
        try {
            return $this->value->toInt();
        } catch (MathException) {
            absurd();
        }
    }

    public function isLessThan(LoggedRequestsCount $that): bool
    {
        return $this->value->isLessThan($that->value);
    }
}
