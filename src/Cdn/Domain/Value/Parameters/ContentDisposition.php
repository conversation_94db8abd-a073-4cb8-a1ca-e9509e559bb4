<?php

declare(strict_types=1);

namespace Cdn77\Api\Cdn\Domain\Value\Parameters;

final readonly class ContentDisposition
{
    public function __construct(public ContentDispositionType $type)
    {
    }

    public static function none(): self
    {
        return new self(ContentDispositionType::None);
    }

    public function isEnabledByParam(): bool
    {
        return $this->type === ContentDispositionType::Parameter;
    }
}
