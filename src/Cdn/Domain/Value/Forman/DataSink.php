<?php

declare(strict_types=1);

namespace Cdn77\Api\Cdn\Domain\Value\Forman;

use Cdn77\Api\Cdn\Domain\Value\Log\OutputDestinationType;
use Cdn77\Api\Core\Domain\Exception\ObjectStorage\InvalidBucketName;
use Psl\Json\Exception\DecodeException;
use Psl\Json\Exception\EncodeException;

use function array_key_first;
use function Cdn77\Functions\absurd;
use function Psl\Json\typed;
use function Psl\Type\shape;
use function Psl\Type\union;

final readonly class DataSink
{
    private function __construct(
        public OutputDestinationType $type,
        public AzureSinkConfig|S3SinkConfig|NoopSinkConfig $config,
    ) {
    }

    public static function azure(AzureSinkConfig $config): self
    {
        return new self(OutputDestinationType::Azure, $config);
    }

    public static function s3(S3SinkConfig $config): self
    {
        return new self(OutputDestinationType::S3, $config);
    }

    public static function noop(): self
    {
        return new self(OutputDestinationType::Noop, new NoopSinkConfig());
    }

    /**
     * @throws DecodeException
     * @throws InvalidBucketName
     */
    public static function fromJson(string $json): self
    {
        if ($json === '{}') {
            return self::noop();
        }

        $sink = typed(
            $json,
            union(
                shape([
                    OutputDestinationType::S3->value => S3SinkConfig::shape(),
                ]),
                shape([
                    OutputDestinationType::Azure->value => AzureSinkConfig::shape(),
                ]),
            ),
        );

        $key = array_key_first($sink);

        return match ($key) {
            OutputDestinationType::S3->value => self::s3(
                S3SinkConfig::fromArray($sink[OutputDestinationType::S3->value] ?? absurd()),
            ),
            OutputDestinationType::Azure->value => self::azure(
                AzureSinkConfig::fromArray($sink[OutputDestinationType::Azure->value] ?? absurd()),
            ),
        };
    }

    public function toJson(): string
    {
        try {
            return $this->config->toJson();
        } catch (EncodeException) {
            absurd();
        }
    }

    /** @return array<string, mixed> */
    public function toArray(): array
    {
        return [
            'type' => $this->type->value,
            'config' => $this->config->toArray(),
        ];
    }
}
