<?php

declare(strict_types=1);

namespace Cdn77\Api\Cdn\Domain\Value\Forman;

use Cdn77\Api\Cdn\Domain\Value\Log\LogPath;
use Cdn77\Api\Cdn\Domain\Value\Log\OutputDestinationType;
use Cdn77\Api\Core\Domain\Encryption\Sodium\Value\EncryptedValue;
use Cdn77\Api\Core\Domain\Exception\ObjectStorage\InvalidBucketName;
use Cdn77\Api\Core\Domain\Value\ObjectStorage\BucketName;
use Cdn77\Api\Core\Domain\Value\Origin\Host;
use Psl\Json\Exception\EncodeException;
use Psl\Type\TypeInterface;

use function Psl\Json\encode;
use function Psl\Type\nullable;
use function Psl\Type\optional;
use function Psl\Type\shape;
use function Psl\Type\string;

/**
 * @phpstan-type S3SinkConfigShape array{
 *     access_key: string,
 *     path: array{
 *         directory?: string|null,
 *         root: string,
 *     },
 *     name: string,
 *     secret_key: string,
 *     url?: string|null,
 *     region?: string|null,
 *     time_path_format?: string|null,
 * }
 */
final readonly class S3SinkConfig
{
    public const string FieldAccessKey = 'access_key';
    public const string FieldDirectory = 'directory';
    public const string FieldName = 'name';
    public const string FieldPath = 'path';
    public const string FieldRegion = 'region';
    public const string FieldRoot = 'root';
    public const string FieldSecretKey = 'secret_key';
    public const string FieldTimePathFormat = 'time_path_format';
    public const string FieldUrl = 'url';

    private function __construct(
        public BucketName $name,
        public EncryptedValue $accessKey,
        public EncryptedValue $secret,
        public Host|null $url,
        public LogPath $logPath,
        public string|null $region,
        public string|null $timePathFormat,
    ) {
    }

    public static function s3(
        BucketName $name,
        EncryptedValue $accessKey,
        EncryptedValue $secret,
        LogPath $logPath,
        Host $url,
        string|null $timePathFormat,
    ): self {
        return new self(
            $name,
            $accessKey,
            $secret,
            $url,
            $logPath,
            null,
            $timePathFormat,
        );
    }

    public static function aws(
        BucketName $name,
        EncryptedValue $accessKey,
        EncryptedValue $secret,
        LogPath $logPath,
        string $region,
        string|null $timePathFormat,
    ): self {
        return new self(
            $name,
            $accessKey,
            $secret,
            null,
            $logPath,
            $region,
            $timePathFormat,
        );
    }

    /**
     * @param S3SinkConfigShape $config
     *
     * @throws InvalidBucketName
     */
    public static function fromArray(array $config): self
    {
        $url = $config[self::FieldUrl] ?? null;

        return new self(
            BucketName::fromString($config[self::FieldName]),
            EncryptedValue::fromString($config[self::FieldAccessKey]),
            EncryptedValue::fromString($config[self::FieldSecretKey]),
            $url === null ? null : Host::fromString($url),
            LogPath::fromRootAndDirectory(
                $config[self::FieldPath][self::FieldRoot],
                $config[self::FieldPath][self::FieldDirectory] ?? null,
            ),
            $config[self::FieldRegion] ?? null,
            $config[self::FieldTimePathFormat] ?? null,
        );
    }

    /** @return TypeInterface<S3SinkConfigShape> */
    public static function shape(): TypeInterface
    {
        return shape([
            self::FieldAccessKey => string(),
            self::FieldPath => shape([
                self::FieldDirectory => optional(nullable(string())),
                self::FieldRoot => string(),
            ]),
            self::FieldName => string(),
            self::FieldSecretKey => string(),
            self::FieldUrl => optional(nullable(string())),
            self::FieldRegion => optional(nullable(string())),
            self::FieldTimePathFormat => optional(nullable(string())),
        ]);
    }

    /** @return array<string, mixed> */
    public function toArray(): array
    {
        return [
            self::FieldAccessKey => $this->accessKey->value,
            self::FieldDirectory => $this->logPath->toString(),
            self::FieldName => $this->name->value,
            self::FieldSecretKey => $this->secret->value,
            self::FieldUrl => $this->url?->value,
            self::FieldRegion => $this->region,
            self::FieldTimePathFormat => $this->timePathFormat,
        ];
    }

    /** @throws EncodeException */
    public function toJson(): string
    {
        return encode([
            OutputDestinationType::S3->value => [
                self::FieldName => $this->name->value,
                self::FieldUrl => $this->url?->value,
                self::FieldAccessKey => $this->accessKey->value,
                self::FieldSecretKey => $this->secret->value,
                self::FieldPath => [
                    self::FieldRoot => $this->logPath->root,
                    self::FieldDirectory => $this->logPath->directory,
                ],
                self::FieldRegion => $this->region,
                self::FieldTimePathFormat => $this->timePathFormat,
            ],
        ]);
    }
}
