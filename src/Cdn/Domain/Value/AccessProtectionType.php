<?php

declare(strict_types=1);

namespace Cdn77\Api\Cdn\Domain\Value;

use cebe\openapi\exceptions\TypeErrorException;
use cebe\openapi\spec\Reference;
use cebe\openapi\spec\Schema;
use cebe\openapi\spec\Type;

enum AccessProtectionType: string
{
    public const string Name = 'accessProtectionType';

    case Blacklist = 'blacklist';
    case BlockList = 'blocklist';
    case Disabled = 'disabled';
    case PassList = 'passlist';
    case Whitelist = 'whitelist';

    public static function fromNullableString(string|null $value): self
    {
        return match ($value) {
            null => self::Disabled,
            default => self::from($value),
        };
    }

    /** @throws TypeErrorException */
    public static function getSchemaSpec(): Schema
    {
        return new Schema([
            'type' => Type::STRING,
            'example' => AccessProtectionType::PassList->value,
            'description' => // TODO: trochu debilni description? (Hotlink, Ip, Geo Protections)
                'With "type": "blocklist" all values set are not allowed. ' .
                'With "type": "passlist" only values set are allowed.',
            'enum' => AccessProtectionType::getSchemaSpecValues(),
        ]);
    }

    /** @throws TypeErrorException */
    public static function getReference(): Reference
    {
        return new Reference(['$ref' => '#/components/schemas/' . self::Name]);
    }

    /** @return list<string> */
    public static function getSchemaSpecValues(): array
    {
        return [self::BlockList->value, self::Disabled->value, self::PassList->value];
    }

    public function getSupportedValue(): string
    {
        return match ($this) {
            self::PassList => self::Whitelist->value,
            self::BlockList => self::Blacklist->value,
            self::Disabled,
            self::Whitelist,
            self::Blacklist => $this->value,
        };
    }

    public function getPublicValue(): string
    {
        return match ($this) {
            self::Whitelist => self::PassList->value,
            self::Blacklist => self::BlockList->value,
            self::Disabled,
            self::PassList,
            self::BlockList => $this->value,
        };
    }

    public function toPublic(): self
    {
        return match ($this) {
            self::Whitelist => self::PassList,
            self::Blacklist => self::BlockList,
            self::Disabled,
            self::PassList,
            self::BlockList => $this,
        };
    }

    public function isPassList(): bool
    {
        return $this === self::PassList || $this === self::Whitelist;
    }

    public function isDisabled(): bool
    {
        return $this === self::Disabled;
    }
}
