<?php

declare(strict_types=1);

namespace Cdn77\Api\Cdn\Domain\Value\Cache;

use Brick\Math\BigInteger;
use Brick\Math\Exception\MathException;
use Webmozart\Assert\Assert;

final readonly class ContentLengthLimit
{
    /** @param positive-int|null $value */
    public function __construct(
        public int|null $value,
    ) {
    }

    public static function fromInt(int $value): self
    {
        Assert::positiveInteger($value);

        return new self($value);
    }

    /** @throws MathException */
    public static function fromBigInteger(BigInteger $value): self
    {
        return self::fromInt($value->toInt());
    }

    public static function none(): self
    {
        return new self(null);
    }
}
