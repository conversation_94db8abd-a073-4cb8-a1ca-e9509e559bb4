<?php

declare(strict_types=1);

namespace Cdn77\Api\Cdn\Domain\Value;

use cebe\openapi\exceptions\TypeErrorException;
use cebe\openapi\spec\Reference;
use cebe\openapi\spec\Schema;
use cebe\openapi\spec\Type;
use Ds\Hashable;
use Ds\Set;

final readonly class MaxAge implements Hashable
{
    public const int Expiration10Minutes = 10;
    public const int Expiration30Minutes = 30;
    public const int Expiration1Hour = 60;
    public const int Expiration4Hours = 240;
    public const int Expiration12Hours = 720;
    public const int Expiration1Day = 1440;
    public const int Expiration36Hours = 2160;
    public const int Expiration2Days = 2880;
    public const int Expiration3Days = 4320;
    public const int Expiration4Days = 5760;
    public const int Expiration5Days = 7200;
    public const int Expiration6Days = 8640;
    public const int Expiration7DaysAnd12Hours = 10800;
    public const int Expiration8Days = 11520;
    public const int Expiration9Days = 12960;
    public const int Expiration10Days = 14400;
    public const int Expiration11Days = 15840;
    public const int Expiration12Days = 17280;

    public const string Name = 'maxAge';

    private const int Expiration5Years = 2_628_000;

    private function __construct(public int $minutes)
    {
    }

    public static function default(): self
    {
        return new self(self::Expiration12Days);
    }

    public static function fromMinutes(int $minutes): self
    {
        return new self($minutes);
    }

    public static function fiveYears(): self
    {
        return new self(self::Expiration5Years);
    }

    /** @throws TypeErrorException */
    public static function getReference(): Reference
    {
        return new Reference(['$ref' => '#/components/schemas/' . self::Name]);
    }

    /** @throws TypeErrorException */
    public static function getSchemaSpec(): Schema
    {
        return new Schema([
            'type' => Type::INTEGER,
            'description' => 'In minutes.',
            'enum' => self::public()->map(static fn (MaxAge $maxAge): int => $maxAge->minutes)->toArray(),
            'example' => self::Expiration1Day,
        ]);
    }

    /** @return Set<self> */
    public static function public(): Set
    {
        return new Set([
            new self(self::Expiration10Minutes),
            new self(self::Expiration30Minutes),
            new self(self::Expiration1Hour),
            new self(self::Expiration4Hours),
            new self(self::Expiration12Hours),
            new self(self::Expiration1Day),
            new self(self::Expiration36Hours),
            new self(self::Expiration2Days),
            new self(self::Expiration3Days),
            new self(self::Expiration4Days),
            new self(self::Expiration5Days),
            new self(self::Expiration6Days),
            new self(self::Expiration7DaysAnd12Hours),
            new self(self::Expiration8Days),
            new self(self::Expiration9Days),
            new self(self::Expiration10Days),
            new self(self::Expiration11Days),
            new self(self::Expiration12Days),
        ]);
    }

    public function hash(): int
    {
        return $this->minutes;
    }

    public function equals(mixed $obj): bool
    {
        return $obj instanceof self && $obj->minutes === $this->minutes;
    }
}
