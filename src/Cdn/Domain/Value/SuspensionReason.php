<?php

declare(strict_types=1);

namespace Cdn77\Api\Cdn\Domain\Value;

final readonly class SuspensionReason
{
    public const string ClientRequest = 'client_request';
    public const string Fraud = 'fraud';
    public const string Abuse = 'abuse';
    public const string Scam = 'Scam';
    public const string Malware = 'Malware';
    public const string OnApp = 'on_app_end';
    public const string NewMedia = 'new_media_end';

    private function __construct(private string $reason)
    {
    }

    public static function fromString(string $reason): self
    {
        return new self($reason);
    }

    public static function clientRequest(): self
    {
        return new self(self::ClientRequest);
    }

    public function get(): string
    {
        return $this->reason;
    }

    public function isFraud(): bool
    {
        return $this->get() === self::Fraud;
    }
}
