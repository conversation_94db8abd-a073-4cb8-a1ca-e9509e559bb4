<?php

declare(strict_types=1);

namespace Cdn77\Api\Cdn\Domain\Value;

use Cdn77\Api\Core\Domain\Value\Enums\EnumSchema;
use cebe\openapi\exceptions\TypeErrorException;
use cebe\openapi\spec\Reference;
use cebe\openapi\spec\Schema;
use cebe\openapi\spec\Type;

enum SecureTokenType: string
{
    public const string Name = 'secureTokenType';

    case Highwinds = 'highwinds';
    case None = 'none';
    case Parameter = 'parameter';
    case Path = 'path';

    /** @throws TypeErrorException */
    public static function getSchemaSpec(): Schema
    {
        return EnumSchema::getSchemaSpec(self::class, Type::STRING, [
            'example' => SecureTokenType::Parameter->value,
            'description' =>
                <<<'HTML'
 <ol>
        <li>parameter - Token will be in the query string - e.g.:/video.mp4?secure=MY_SECURE_TOKEN.</li>
        <li>path - Token will be in the path - e.g.:/MY_SECURE_TOKEN/video.mp4.</li>
        <li>none - Use to disable secure token.</li>
        <li>highwinds</li>
</ol>
HTML,
        ]);
    }

    /** @throws TypeErrorException */
    public static function getReference(): Reference
    {
        return EnumSchema::getReference(self::Name);
    }
}
