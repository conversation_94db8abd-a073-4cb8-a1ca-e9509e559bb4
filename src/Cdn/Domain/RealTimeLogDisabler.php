<?php

declare(strict_types=1);

namespace Cdn77\Api\Cdn\Domain;

use Cdn77\Api\Cdn\Domain\Forman\FormanApi;
use Cdn77\Api\Cdn\Domain\Forman\FormanApiError;
use Cdn77\Api\Core\Domain\Entity\RealTimeLog\RealTimeLog;
use DateTimeImmutable;
use Psr\Log\LoggerInterface;

final readonly class RealTimeLogDisabler
{
    public function __construct(
        private FormanApi $formanApi,
        private LoggerInterface $logger,
    ) {
    }

    public function disable(
        RealTimeLog $realTimeLog,
        DateTimeImmutable $now,
    ): void {
        if ($realTimeLog->isDisabled($now)) {
            return;
        }

        try {
            $this->formanApi->disable($realTimeLog->id());
        } catch (FormanApiError $ex) {
            $this->logger->error($ex->getMessage(), ['exception' => $ex]);
        }

        $realTimeLog->deactivate($now);
    }
}
