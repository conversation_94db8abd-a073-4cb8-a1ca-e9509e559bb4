<?php

declare(strict_types=1);

namespace Cdn77\Api\Cdn\Domain\Query\FindCdns;

use Cdn77\Api\Cdn\Domain\Query\FindCdns\Input\Filter;
use Cdn77\Api\Cdn\Domain\Query\FindCdns\Input\SelectionField;
use Cdn77\Api\Cdn\Domain\Query\FindCdns\Output\Cdns;
use Cdn77\Api\Core\Domain\Messaging\Query;
use Cdn77\GraphQLUtils\Pagination\OffsetPagination\Value\OffsetPagination;
use Cdn77\GraphQLUtils\Pagination\OffsetPagination\Value\OrderDirection;
use Ds\Map;
use Ds\Set;

/** @implements Query<Cdns, FindCdnsHandler> */
final readonly class FindCdns implements Query
{
    /**
     * @param Set<SelectionField> $selectionFields
     * @param Map<OrderField, OrderDirection> $orderings
     */
    public function __construct(
        public Set $selectionFields,
        public Filter $filter,
        public OffsetPagination $pagination,
        public Map $orderings = new Map(),
    ) {
    }
}
