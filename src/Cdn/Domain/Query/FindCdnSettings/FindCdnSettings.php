<?php

declare(strict_types=1);

namespace Cdn77\Api\Cdn\Domain\Query\FindCdnSettings;

use Cdn77\Api\Cdn\Domain\Query\FindCdnSettings\Input\Filter;
use Cdn77\Api\Cdn\Domain\Query\FindCdnSettings\Input\SelectionField;
use Cdn77\Api\Cdn\Domain\Query\FindCdnSettings\Output\CdnSettingsCollection;
use Cdn77\Api\Core\Domain\Messaging\Query;
use Ds\Set;

/** @implements Query<CdnSettingsCollection, FindCdnSettingsHandler> */
final readonly class FindCdnSettings implements Query
{
    /** @param Set<SelectionField> $selectionFields */
    public function __construct(
        public Set $selectionFields,
        public Filter $filter,
    ) {
    }
}
