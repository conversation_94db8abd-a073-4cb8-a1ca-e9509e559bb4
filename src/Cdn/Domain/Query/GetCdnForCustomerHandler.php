<?php

declare(strict_types=1);

namespace Cdn77\Api\Cdn\Domain\Query;

use Cdn77\Api\Cdn\Domain\Dto\CdnDetail;
use Cdn77\Api\Cdn\Domain\Exception\CdnNotFound;
use Cdn77\Api\Cdn\Domain\Factory\CdnDetailFactory;
use Cdn77\Api\Core\Domain\Messaging\QueryHandler;
use Cdn77\Api\Core\Domain\Repository\Cdn\CdnRepository;
use Cdn77\Api\Core\Domain\Validation\CdnAccessValidator;
use Cdn77\Api\Core\Domain\Value\AccessRestriction\AccessType;

final readonly class GetCdnForCustomerHandler implements QueryHandler
{
    public function __construct(
        private CdnAccessValidator $cdnAccessValidator,
        private CdnDetailFactory $cdnDetailFactory,
        private CdnRepository $cdnRepository,
    ) {
    }

    /** @throws CdnNotFound */
    public function handle(GetCdnForCustomer $query): CdnDetail
    {
        $cdnId = $query->cdnId;
        $cdn = $this->cdnRepository->get($cdnId);

        if (! $this->cdnAccessValidator->isAllowed($cdn, $query->customerId, AccessType::CdnRead)) {
            throw CdnNotFound::forId($cdn->getId());
        }

        return $this->cdnDetailFactory->forCdn($cdn);
    }
}
