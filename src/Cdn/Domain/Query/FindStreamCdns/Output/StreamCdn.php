<?php

declare(strict_types=1);

namespace Cdn77\Api\Cdn\Domain\Query\FindStreamCdns\Output;

use Cdn77\Api\Cdn\Domain\Value\StreamProtocol;
use Cdn77\Api\Core\Domain\Entity\Cdn\CdnId;
use Cdn77\Api\Core\Domain\Entity\Customer\CustomerUuid;
use Cdn77\Api\Core\Domain\Entity\Origin\LegacyStreamOriginId;
use Cdn77\Api\Core\Domain\Value\EncryptedValue;
use Cdn77\Api\Core\Domain\Value\Origin\Port;

final readonly class StreamCdn
{
    public function __construct(
        public CdnId $id,
        public LegacyStreamOriginId|null $streamOriginId = null,
        public string|null $key = null,
        public EncryptedValue|null $password = null,
        public string|null $path = null,
        public Port|null $port = null,
        public StreamProtocol|null $streamProtocol = null,
        public CustomerUuid|null $customerId = null,
    ) {
    }
}
