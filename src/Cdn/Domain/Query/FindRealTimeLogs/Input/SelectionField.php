<?php

declare(strict_types=1);

namespace Cdn77\Api\Cdn\Domain\Query\FindRealTimeLogs\Input;

enum SelectionField: string
{
    case ActiveUntil = 'activeUntil';
    case BucketName = 'bucketName';
    case Cdns = 'cdns';
    case Id = 'id';
    case IsActive = 'isActive';
    case LoggingFields = 'loggingFields';
    case LogPath = 'logPath';
    case OriginId = 'originId';
    case OutputFormat = 'outputFormat';
    case Scope = 'scope';
    case TtlConfig = 'ttlConfig';
}
