<?php

declare(strict_types=1);

namespace Cdn77\Api\Cdn\Domain\Query\Log;

use Cdn77\Api\Cdn\Domain\Value\Log\LoggedRequestsCount;
use Cdn77\Api\Core\Domain\Authorization\Context\AccessContext;
use Cdn77\Api\Core\Domain\Authorization\Context\BillingAccessContext;
use Cdn77\Api\Core\Domain\Dto\TimeRange;
use Cdn77\Api\Core\Domain\Entity\Customer\CustomerUuid;
use Cdn77\Api\Core\Domain\Messaging\AuthorizedCommand;
use Cdn77\Api\Core\Domain\Messaging\Query;

/** @implements Query<LoggedRequestsCount, GetSumHandler> */
final readonly class GetSum implements Query, AuthorizedCommand
{
    public function __construct(
        public CustomerUuid $customerId,
        public TimeRange $timeRange,
    ) {
    }

    public function accessContext(): AccessContext
    {
        return new BillingAccessContext($this->customerId);
    }
}
