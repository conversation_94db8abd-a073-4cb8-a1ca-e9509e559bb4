<?php

declare(strict_types=1);

namespace Cdn77\Api\Cdn\Domain\Query;

use Cdn77\Api\Cdn\Domain\Dto\MaxAgeValues;
use Cdn77\Api\Core\Domain\Entity\Customer\CustomerUuid;
use Cdn77\Api\Core\Domain\Messaging\Query;

/** @implements Query<MaxAgeValues, GetCustomerMaxAgeValuesHandler> */
final readonly class GetCustomerMaxAgeValues implements Query
{
    public function __construct(public CustomerUuid $customerId)
    {
    }
}
