<?php

declare(strict_types=1);

namespace Cdn77\Api\Cdn\Domain\Query;

use Cdn77\Api\Cdn\Domain\Dto\CdnDetail;
use Cdn77\Api\Cdn\Domain\Factory\CdnDetailFactory;
use Cdn77\Api\Cdn\Domain\Repository\CdnRepository;
use Cdn77\Api\Core\Domain\Entity\Cdn\Cdn;
use Cdn77\Api\Core\Domain\Messaging\QueryHandler;
use Cdn77\Api\Core\Domain\Validation\CdnAccessValidator;
use Cdn77\Api\Core\Domain\Value\AccessRestriction\AccessType;

use function array_filter;

final readonly class GetCdnDetailsHandler implements QueryHandler
{
    public function __construct(
        private CdnAccessValidator $cdnAccessValidator,
        private CdnDetailFactory $cdnDetailFactory,
        private CdnRepository $cdnRepository,
    ) {
    }

    /** @return iterable<CdnDetail> */
    public function handle(GetCdnDetails $query): iterable
    {
        $customerId = $query->customerId;
        $cdns = array_filter(
            $customerId->isCRA()
                ? $this->cdnRepository->findAllForCustomer($customerId)
                : $this->cdnRepository->findAllForCustomerAndParent($customerId),
            fn (Cdn $cdn): bool => $this->cdnAccessValidator->isAllowed($cdn, $customerId, AccessType::CdnRead),
        );

        foreach ($cdns as $cdn) {
            yield $this->cdnDetailFactory->forCdn($cdn);
        }
    }
}
