<?php

declare(strict_types=1);

namespace Cdn77\Api\Cdn\Domain\Logger;

use Cdn77\Api\Core\Domain\Entity\Cdn\CdnId;
use Cdn77\Api\Datacenter\Domain\Value\CityCode;
use Ds\Set;
use Psr\Log\LoggerInterface;

use function sprintf;

final readonly class DatacenterLocationsDifferencesLogger
{
    public function __construct(private LoggerInterface $logger)
    {
    }

    /**
     * @param Set<CityCode> $nxgLocationsCityCodes
     * @param Set<CityCode> $clientLocationsCityCodes
     */
    public function log(Set $nxgLocationsCityCodes, Set $clientLocationsCityCodes, CdnId $cdnId): void
    {
        $clientMissing = $nxgLocationsCityCodes->diff($clientLocationsCityCodes);

        if ($clientMissing->isEmpty()) {
            return;
        }

        $this->logger->error(sprintf(
            'Missing client locations (%s) for CDN Resource with ID: "%d".',
            $clientMissing->join(','),
            $cdnId->toInt(),
        ));
    }
}
