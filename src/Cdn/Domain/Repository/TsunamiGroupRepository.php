<?php

declare(strict_types=1);

namespace Cdn77\Api\Cdn\Domain\Repository;

use Cdn77\Api\Core\Domain\Entity\Cdn\TsunamiGroup;
use Cdn77\Api\Core\Domain\Entity\Customer\CustomerUuid;
use Cdn77\Api\Core\Domain\Entity\Datacenter\ServerGroupId;
use Ds\Set;
use Generator;

interface TsunamiGroupRepository
{
    /**
     * @param Set<ServerGroupId> $serverGroupIds
     *
     * @return Generator<TsunamiGroup>
     */
    public function findForServerGroups(Set $serverGroupIds): Generator;

    /** @return Generator<TsunamiGroup> */
    public function findAllEnabledForCustomer(CustomerUuid $customerId): Generator;

    public function findForCustomerAndServerGroup(
        CustomerUuid $customerId,
        ServerGroupId $serverGroupId,
    ): TsunamiGroup|null;
}
