<?php

declare(strict_types=1);

namespace Cdn77\Api\Cdn\Domain\Finder\Log;

use Cdn77\Api\Cdn\Domain\Query\FindRealTimeLogs\Input\Filter;
use Cdn77\Api\Cdn\Domain\Query\FindRealTimeLogs\Input\SelectionField;
use Cdn77\Api\Cdn\Domain\Query\FindRealTimeLogs\Output\RealTimeLogs;
use DateTimeImmutable;
use Ds\Set;

interface RealTimeLogFinder
{
    /** @param Set<SelectionField> $selectionFields */
    public function find(
        Set $selectionFields,
        Filter $filter,
        DateTimeImmutable $now,
    ): RealTimeLogs;
}
