<?php

declare(strict_types=1);

namespace Cdn77\Api\Cdn\Domain\Finder\Log;

use Cdn77\Api\Core\Domain\Dto\TimeRange;
use Cdn77\Api\Core\Domain\Entity\Customer\CustomerUuid;
use Cdn77\Api\Core\Domain\Entity\RealTimeLog\RealTimeLogId;
use Ds\Set;

interface RealTimeLogIdFinder
{
    /** @return Set<RealTimeLogId> */
    public function findForCustomerAndRange(
        CustomerUuid $customerId,
        TimeRange $timeRange,
    ): Set;
}
