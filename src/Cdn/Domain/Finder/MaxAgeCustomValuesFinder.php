<?php

declare(strict_types=1);

namespace Cdn77\Api\Cdn\Domain\Finder;

use Cdn77\Api\Cdn\Domain\Value\MaxAge;
use Cdn77\Api\Cdn\Domain\Value\MaxAge404;
use Cdn77\Api\Cdn\Domain\Value\Parameters\MaxAgeType;
use Cdn77\Api\Core\Domain\Entity\Customer\CustomerUuid;
use Ds\Map;
use Ds\Set;

interface MaxAgeCustomValuesFinder
{
    /** @return Map<MaxAgeType, Set<MaxAge|MaxAge404>> */
    public function findForCustomer(CustomerUuid $customerId): Map;
}
