<?php

declare(strict_types=1);

namespace Cdn77\Api\Cdn\Domain\Dto\Log;

use Cdn77\Api\Cdn\Domain\Value\Log\LogPath;
use Cdn77\Api\Cdn\Domain\Value\Log\LogScope;
use Cdn77\Api\Cdn\Domain\Value\Log\OutputFormat;
use Cdn77\Api\Core\Domain\Entity\RealTimeLog\RealTimeLog;
use Cdn77\Api\Core\Domain\Entity\RealTimeLog\RealTimeLogId;
use DateTimeImmutable;
use Ds\Set;

final readonly class CreatedRealTimeLog
{
    /** @param Set<CdnReference>|null $cdns */
    private function __construct(
        public RealTimeLogId $realTimeLogId,
        public LogScope $scope,
        public OutputFormat $outputFormat,
        public LogPath|null $path,
        public DateTimeImmutable|null $activeUntil,
        public bool $isActive,
        public ObjectStorageReference|null $objectStorage,
        public Set|null $cdns,
    ) {
    }

    /** @param Set<CdnReference>|null $cdns */
    public static function fromEntity(
        RealTimeLog $realTimeLog,
        DateTimeImmutable $now,
        ObjectStorageReference|null $objectStorage,
        Set|null $cdns,
    ): self {
        return new self(
            $realTimeLog->id(),
            $realTimeLog->scope(),
            $realTimeLog->format(),
            $realTimeLog->directory(),
            $realTimeLog->activeUntil(),
            $realTimeLog->isActive($now),
            $objectStorage,
            $cdns,
        );
    }
}
