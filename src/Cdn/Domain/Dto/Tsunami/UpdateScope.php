<?php

declare(strict_types=1);

namespace Cdn77\Api\Cdn\Domain\Dto\Tsunami;

use Cdn77\Api\Core\Domain\Entity\Cdn\CdnId;
use Cdn77\Api\Core\Domain\Hurricane\Tsunami\ProtectionLevel;
use Cdn77\Api\Core\Domain\Hurricane\Tsunami\Target;
use Ds\Map;
use Ds\Set;

final readonly class UpdateScope
{
    /**
     * @param Set<Target> $targets
     * @param Set<CdnId> $cdnIds
     * @param Map<CdnId, covariant ProtectionLevel> $cdnProtections
     */
    public function __construct(
        public Set $targets,
        public Set $cdnIds,
        public ProtectionLevel $protectionLevel,
        public Map $cdnProtections,
    ) {
    }
}
