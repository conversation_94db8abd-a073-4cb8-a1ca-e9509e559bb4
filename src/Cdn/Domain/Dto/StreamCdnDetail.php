<?php

declare(strict_types=1);

namespace Cdn77\Api\Cdn\Domain\Dto;

use Cdn77\Api\Core\Domain\Entity\Cdn\StreamCdn;
use Cdn77\Api\Core\Domain\Entity\Cname\Cname;

final readonly class StreamCdnDetail
{
    /** @param list<Cname> $cnames */
    public function __construct(
        public StreamCdn $streamCdn,
        public array $cnames,
        public bool $streamingPlaylistBypassEnabled,
    ) {
    }
}
