<?php

declare(strict_types=1);

namespace Cdn77\Api\Cdn\Domain\Dto\AccessProtection;

use Cdn77\Api\Cdn\Domain\Value\AccessProtectionType;
use Cdn77\Api\Invoice\Domain\Value\CountryIsoCode;
use Ds\Set;
use JsonSerializable;

final readonly class GeoProtection implements JsonSerializable
{
    /** @param array<CountryIsoCode>|null $countries */
    public function __construct(private AccessProtectionType $type, private array|null $countries)
    {
    }

    public static function disabled(): self
    {
        return new self(AccessProtectionType::Disabled, null);
    }

    public function getType(): AccessProtectionType
    {
        return $this->type;
    }

    /** @return Set<CountryIsoCode>|null */
    public function getCountries(): Set|null
    {
        if ($this->type->isDisabled()) {
            return null;
        }

        return new Set($this->countries ?? []);
    }

    /** @return array<string, string|list<string>|null> */
    public function jsonSerialize(): array
    {
        return [
            'type' => $this->type->value,
            'countries' => $this->getCountries()
                ?->map(static fn (CountryIsoCode $iso): string => $iso->get())
                ->toArray(),
        ];
    }
}
