<?php

declare(strict_types=1);

namespace Cdn77\Api\Cdn\Domain\AccessProtection;

use Cdn77\Api\Cdn\Domain\Dto\AccessProtection\IpProtection;
use Cdn77\Api\Cdn\Domain\Exception\InvalidAccessProtectionSetup;
use Cdn77\Api\Cdn\Domain\Repository\CdnHttpProtectionRepository;
use Cdn77\Api\Cdn\Domain\Repository\IpProtectionAddressRepository;
use Cdn77\Api\Core\Domain\Entity\Cdn\Cdn;
use Cdn77\Api\Core\Domain\Entity\Cdn\CdnHttpProtectionId;
use Cdn77\Api\Core\Domain\Entity\Cdn\CdnId;
use Cdn77\Api\Core\Domain\Entity\Cdn\IpProtectionAddress;
use Cdn77\Api\Core\Domain\Entity\Customer\CustomerId;
use Cdn77\Api\Core\Domain\Repository\Customer\CustomerCdnSettingsRepository;

final readonly class SetupIpProtection
{
    public function __construct(
        private CustomerCdnSettingsRepository $customerCdnSettingsRepository,
        private CdnHttpProtectionRepository $cdnHttpProtectionRepository,
        private IpProtectionAddressRepository $ipProtectionAddressRepository,
    ) {
    }

    public function execute(Cdn $cdn, IpProtection $ipProtection): void
    {
        $cdnId = $cdn->getId();
        $customerId = $cdn->getOwner()->getId();

        $cdnHttpProtection = $this->cdnHttpProtectionRepository->getForCdn($cdnId);
        if ($cdnHttpProtection->getIppType()->isDisabled() && $ipProtection->getType()->isDisabled()) {
            return;
        }

        $ipCount = $ipProtection->getIps()?->count() ?? 0;
        if ($ipProtection->getType()->isPassList() && $ipCount === 0) {
            throw InvalidAccessProtectionSetup::noIpsGiven();
        }

        if ($ipCount > 0) {
            $this->validateIpProtectionLimit($customerId, $ipCount);
        }

        $this->purgeIpProtectionAddresses($cdnId);
        $this->addIpProtectionAddresses($cdnHttpProtection->getId(), $ipProtection);

        $cdnHttpProtection->setupIpProtection($ipProtection);
    }

    private function validateIpProtectionLimit(CustomerId $customerId, int $count): void
    {
        $max = $this->customerCdnSettingsRepository->getForCustomer($customerId)->maxIpProtectionAddresses();

        if ($count <= $max) {
            return;
        }

        throw InvalidAccessProtectionSetup::overLimitIps($max);
    }

    private function addIpProtectionAddresses(
        CdnHttpProtectionId $cdnHttpProtectionId,
        IpProtection $ipProtection,
    ): void {
        foreach ($ipProtection->getIps() ?? [] as $ip) {
            $this->ipProtectionAddressRepository->add(
                new IpProtectionAddress($cdnHttpProtectionId, $ip),
            );
        }
    }

    private function purgeIpProtectionAddresses(CdnId $cdnId): void
    {
        $ipProtectionAddresses = $this->ipProtectionAddressRepository->findForCdn($cdnId);

        foreach ($ipProtectionAddresses as $ipProtectionAddress) {
            $this->ipProtectionAddressRepository->remove($ipProtectionAddress);
        }
    }
}
