<?php

declare(strict_types=1);

namespace Cdn77\Api\Cdn\Domain;

use Cdn77\Api\Cdn\Domain\Finder\Log\RealTimeLoggedRequestsCountFinder;
use Cdn77\Api\Cdn\Domain\Finder\Log\RealTimeLogIdFinder;
use Cdn77\Api\Cdn\Domain\Value\Log\LoggedRequestsCount;
use Cdn77\Api\Cdn\Domain\Value\Log\LoggedRequestsPricing;
use Cdn77\Api\Cdn\Domain\Value\Log\LogPrice;
use Cdn77\Api\Core\Domain\Dto\TimeRange;
use Cdn77\Api\Core\Domain\Entity\Customer\CustomerUuid;

final readonly class RealTimeLogPriceCalculator
{
    public function __construct(
        private RealTimeLogIdFinder $realTimeLogIdFinder,
        private RealTimeLoggedRequestsCountFinder $realTimeLogRecordsCountFinder,
    ) {
    }

    public function calculate(
        CustomerUuid $customerId,
        TimeRange $range,
        LoggedRequestsPricing $pricing,
    ): LogPrice {
        $realTimeLogIds = $this->realTimeLogIdFinder->findForCustomerAndRange($customerId, $range);
        $loggedRequestsCount = $this->realTimeLogRecordsCountFinder->findForRange($range, $realTimeLogIds);

        if ($loggedRequestsCount->isLessThan(LoggedRequestsCount::fromThousands(100))) {
            return LogPrice::zero();
        }

        return $pricing->price($loggedRequestsCount);
    }
}
