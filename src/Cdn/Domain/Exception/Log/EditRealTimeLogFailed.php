<?php

declare(strict_types=1);

namespace Cdn77\Api\Cdn\Domain\Exception\Log;

use Cdn77\Api\Core\Domain\Entity\RealTimeLog\RealTimeLogId;
use Cdn77\Api\Core\Domain\Exception\ClapDomainException;
use Cdn77\Api\Core\Domain\Exception\SupportsHttpResponse;
use DomainException;
use Symfony\Component\HttpFoundation\Response;

use function sprintf;

final class EditRealTimeLogFailed extends DomainException implements ClapDomainException, SupportsHttpResponse
{
    public static function invalidAccess(RealTimeLogId $logId): self
    {
        return new self(sprintf('Could not access Real-time Log with ID "%s".', $logId->toString()));
    }

    public static function ttlUpdateNotAllowed(): self
    {
        return new self('TTL configuration can only be updated for Real-time Logs stored in CDN77 Object Storage.');
    }

    public static function fromInvalidFieldException(InvalidLogFields $exception): self
    {
        return new self($exception->getMessage());
    }

    public function getResponseCode(): int
    {
        return Response::HTTP_UNPROCESSABLE_ENTITY;
    }
}
