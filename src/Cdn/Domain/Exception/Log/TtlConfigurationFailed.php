<?php

declare(strict_types=1);

namespace Cdn77\Api\Cdn\Domain\Exception\Log;

use Cdn77\Api\Core\Domain\Exception\ClapDomainException;
use Cdn77\Api\Core\Domain\Value\ObjectStorage\BucketName;
use DomainException;

use function sprintf;

final class TtlConfigurationFailed extends DomainException implements ClapDomainException
{
    public static function createForBucket(BucketName $bucketName): self
    {
        return new self(sprintf('TTL configuration failed for bucket "%s".', $bucketName->value));
    }
}
