<?php

declare(strict_types=1);

namespace Cdn77\Api\Cdn\Domain\Exception;

use Cdn77\Api\Core\Domain\Entity\Customer\CustomerId;
use Cdn77\Api\Core\Domain\Exception\ClapDomainException;
use DomainException;

use function sprintf;

final class CdnSettingsNotFound extends DomainException implements ClapDomainException
{
    public static function forCustomer(CustomerId $customerId): self
    {
        return new self(sprintf('CDN settings for customer "%s" not found.', $customerId->toString()));
    }
}
