<?php

declare(strict_types=1);

namespace Cdn77\Api\Cdn\Domain\Exception;

use Cdn77\Api\Core\Domain\Exception\ClapDomainException;
use Cdn77\Api\Core\Domain\Exception\SupportsHttpResponse;
use Cdn77\Api\Core\Domain\Value\Label;
use DomainException;
use Symfony\Component\HttpFoundation\Response;

use function sprintf;

final class CdnCouldNotBeCreated
    extends DomainException
    implements ClapDomainException, SupportsHttpResponse
{
    public static function notAllowed(): self
    {
        return new self('Could not create new CDN Resource. Forbidden.');
    }

    public static function temporarilyDisabled(): self
    {
        return new self('It is currently not possible to create a new CDN Resource. Please contact support.');
    }

    public static function activeCdnCountQuotaExceeded(int $activeCdnCountQuota): self
    {
        return new self(sprintf(
            'You have reached your maximum quota of "%d" active CDN Resources.',
            $activeCdnCountQuota,
        ));
    }

    public static function originConfigurationFailed(): self
    {
        return new self('Could not create new CDN Resource. Origin configuration failed.');
    }

    public static function duplicateLabel(Label $label): self
    {
        return new self(sprintf('The label "%s" is already being used.', $label->value));
    }

    public function getResponseCode(): int
    {
        return Response::HTTP_UNPROCESSABLE_ENTITY;
    }
}
