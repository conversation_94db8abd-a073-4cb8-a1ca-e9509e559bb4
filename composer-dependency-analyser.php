<?php

declare(strict_types=1);

use Cdn77\Api\Kernel;
use Ds\Set;
use ShipMonk\ComposerDependencyAnalyser\Config\Configuration;
use ShipMonk\ComposerDependencyAnalyser\Config\ErrorType;
use <PERSON>ymfony\Component\Finder\Finder;

use function Psl\Regex\capture_groups;
use function Psl\Regex\every_match;

$config = new Configuration();

$files = Finder::create()
    ->files()
    ->in(Kernel::getSrcDir())
    ->name(['*.yaml', '*.yml', '*.xml']);

$classNameRegex = '[a-zA-Z_\x80-\xff][a-zA-Z0-9_\x80-\xff]*';

$matches = new Set();

foreach ($files as $file) {
    $fileMatches = every_match(
        $file->getContents(),
        <<<REGEX
        ~$classNameRegex(?:\\\\$classNameRegex)+~
        REGEX,
        capture_groups([0]),
    );

    foreach ($fileMatches ?? [] as $match) {
        if (str_starts_with($match[0], 'Cdn77\Api')) {
            continue;
        }

        $matches->add($match[0]);
    }
}

$config->addForceUsedSymbols($matches->toArray());

return $config
    ->enableAnalysisOfUnusedDevDependencies()
    ->addPathToScan(__DIR__ . '/bin', false)
    ->addPathToScan(__DIR__ . '/graphql', false)
    ->addPathToScan(__DIR__ . '/migrations', false)
    ->addPathToScan(__DIR__ . '/public', false)
    ->addPathToScan(__DIR__ . '/bootstrap.php', false)
    ->addPathToScan(__DIR__ . '/composer-dependency-analyser.php', true)
    ->addPathToScan(__DIR__ . '/rector.php', true)
    ->addPathToScan(__DIR__ . '/sailor.php', false)
    ->ignoreErrorsOnExtensions(
        [
            'ext-intl',
            'ext-pcov',
            'ext-pdo',
            'ext-pdo_pgsql',
        ],
        [ErrorType::UNUSED_DEPENDENCY],
    )
    ->ignoreErrorsOnPackagesAndPaths(
        [
            'dama/doctrine-test-bundle',
            'hautelook/alice-bundle',
            'nelmio/alice',
            'theofidry/alice-data-fixtures',
        ],
        ['src/Kernel.php'],
        [ErrorType::DEV_DEPENDENCY_IN_PROD],
    )
    ->ignoreErrorsOnPackagesAndPaths(
        ['nelmio/alice'],
        ['src/Core/Config/packages/test/app.php'],
        [ErrorType::DEV_DEPENDENCY_IN_PROD],
    )
    ->ignoreErrorsOnPackages(
        [
            'cdn77/coding-standard', // CLI tool
            'cdn77/phpstan-extension', // PHPStan
            'ergebnis/composer-normalize', // CLI tool
            'ergebnis/phpunit-slow-test-detector', // CLI tool
            'goetas/jms-serializer-phpstan-extension', // PHPStan
            'php-standard-library/phpstan-extension', // PHPStan
            'php-standard-library/phpstan-extension', // PHPStan
            'phpstan/extension-installer', // PHPStan
            'phpstan/phpstan', // PHPStan
            'phpstan/phpstan-deprecation-rules', // PHPStan
            'phpstan/phpstan-doctrine', // PHPStan
            'phpstan/phpstan-mockery', // PHPStan
            'phpstan/phpstan-phpunit', // PHPStan
            'phpstan/phpstan-strict-rules', // PHPStan
            'phpstan/phpstan-symfony', // PHPStan
            'phpstan/phpstan-webmozart-assert', // PHPStan
            'qossmic/deptrac', // CI CLI command
            'roave/security-advisories', // CI CLI command
            'shipmonk/phpstan-rules', // PHPStan
            'spomky-labs/aes-key-wrap', // missing dependency in facile-it/php-openid-client
            'symfony/browser-kit', // Missing functions support
            'symfony/yaml', // Missing functions support
            'thecodingmachine/phpstan-safe-rule', // PHPStan
            'ticketswap/phpstan-error-formatter', // PHPStan
            'tomasvotruba/cognitive-complexity', // PHPStan
        ],
        [ErrorType::UNUSED_DEPENDENCY],
    );
