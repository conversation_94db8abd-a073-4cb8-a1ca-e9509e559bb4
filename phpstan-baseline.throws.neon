parameters:
	ignoreErrors:
		-
			message: '#^Method Cdn77\\Api\\Migrations\\Version20220420150600\:\:up\(\) throws checked exception Safe\\Exceptions\\FilesystemException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: migrations/2022/04/Version20220420150600.php

		-
			message: '#^Method Cdn77\\Api\\Api\\EventListener\\RequestListener\:\:checkRestriction\(\) throws checked exception JMS\\Serializer\\Exception\\RuntimeException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Api/EventListener/RequestListener.php

		-
			message: '#^Method Cdn77\\Api\\Api\\Infrastructure\\Finder\\DbalBlockedIpFinder\:\:isBlocked\(\) throws checked exception Doctrine\\DBAL\\Exception but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Api/Infrastructure/Finder/DbalBlockedIpFinder.php

		-
			message: '#^Method Cdn77\\Api\\Authentication\\Domain\\Command\\RemovePersonalTokenHandler\:\:handle\(\) throws checked exception Cdn77\\Api\\Authentication\\Domain\\Exception\\TokenNotFound but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Authentication/Domain/Command/RemovePersonalTokenHandler.php

		-
			message: '#^Method Cdn77\\Api\\Authentication\\Domain\\Command\\RemovePersonalTokenHandler\:\:handle\(\) throws checked exception Cdn77\\Api\\Core\\Domain\\Exception\\Customer\\CustomerNotFound but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Authentication/Domain/Command/RemovePersonalTokenHandler.php

		-
			message: '#^Method Cdn77\\Api\\Authentication\\Domain\\Query\\GetTwoFactorAuthenticationSetupHandler\:\:handle\(\) throws checked exception Cdn77\\Api\\Core\\Domain\\Exception\\Customer\\CustomerNotFound but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Authentication/Domain/Query/GetTwoFactorAuthenticationSetupHandler.php

		-
			message: '#^Method Cdn77\\Api\\Authentication\\Domain\\Value\\Token\:\:fromArray\(\) throws checked exception Safe\\Exceptions\\UrlException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 3
			path: src/Authentication/Domain/Value/Token.php

		-
			message: '#^Method Cdn77\\Api\\Authentication\\Infrastructure\\Repository\\DoctrineApplicationTokenRepository\:\:findForApp\(\) throws checked exception Doctrine\\ORM\\NonUniqueResultException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Authentication/Infrastructure/Repository/DoctrineApplicationTokenRepository.php

		-
			message: '#^Method Cdn77\\Api\\Authentication\\Infrastructure\\Repository\\DoctrinePersonalTokenRepository\:\:findForCustomerAndLabel\(\) throws checked exception Doctrine\\ORM\\NonUniqueResultException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Authentication/Infrastructure/Repository/DoctrinePersonalTokenRepository.php

		-
			message: '#^Method Cdn77\\Api\\Authentication\\Infrastructure\\Repository\\DoctrinePersonalTokenRepository\:\:findForId\(\) throws checked exception Doctrine\\ORM\\NonUniqueResultException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Authentication/Infrastructure/Repository/DoctrinePersonalTokenRepository.php

		-
			message: '#^Method Cdn77\\Api\\Authentication\\Infrastructure\\Repository\\DoctrinePersonalTokenRepository\:\:getAllForCustomer\(\) throws checked exception Cdn77\\Api\\Authentication\\Domain\\Exception\\TokenNotFound but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Authentication/Infrastructure/Repository/DoctrinePersonalTokenRepository.php

		-
			message: '#^Method Cdn77\\Api\\Authentication\\Infrastructure\\Repository\\DoctrinePersonalTokenRepository\:\:getForId\(\) throws checked exception Cdn77\\Api\\Authentication\\Domain\\Exception\\TokenNotFound but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Authentication/Infrastructure/Repository/DoctrinePersonalTokenRepository.php

		-
			message: '#^Method Cdn77\\Api\\Cdn\\Domain\\AccessProtection\\SetupGeoProtection\:\:execute\(\) throws checked exception Cdn77\\Api\\Cdn\\Domain\\Exception\\InvalidAccessProtectionSetup but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Cdn/Domain/AccessProtection/SetupGeoProtection.php

		-
			message: '#^Method Cdn77\\Api\\Cdn\\Domain\\AccessProtection\\SetupGeoProtection\:\:validateCountryProtectionLimit\(\) throws checked exception Cdn77\\Api\\Cdn\\Domain\\Exception\\InvalidAccessProtectionSetup but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Cdn/Domain/AccessProtection/SetupGeoProtection.php

		-
			message: '#^Method Cdn77\\Api\\Cdn\\Domain\\AccessProtection\\SetupIpProtection\:\:execute\(\) throws checked exception Cdn77\\Api\\Cdn\\Domain\\Exception\\InvalidAccessProtectionSetup but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Cdn/Domain/AccessProtection/SetupIpProtection.php

		-
			message: '#^Method Cdn77\\Api\\Cdn\\Domain\\AccessProtection\\SetupIpProtection\:\:validateIpProtectionLimit\(\) throws checked exception Cdn77\\Api\\Cdn\\Domain\\Exception\\InvalidAccessProtectionSetup but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Cdn/Domain/AccessProtection/SetupIpProtection.php

		-
			message: '#^Method Cdn77\\Api\\Cdn\\Domain\\Command\\CreateStreamCdnHandler\:\:handle\(\) throws checked exception Cdn77\\Api\\Core\\Domain\\Exception\\Customer\\CustomerNotFound but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Cdn/Domain/Command/CreateStreamCdnHandler.php

		-
			message: '#^Method Cdn77\\Api\\Cdn\\Domain\\Command\\EditStreamCdnHandler\:\:handle\(\) throws checked exception Cdn77\\Api\\Core\\Domain\\NxgApi\\Exception\\NxgRequestFailed but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Cdn/Domain/Command/EditStreamCdnHandler.php

		-
			message: '#^Method Cdn77\\Api\\Cdn\\Domain\\Command\\EnableDatacentersForCdnHandler\:\:handle\(\) throws checked exception Cdn77\\Api\\Cdn\\Domain\\Exception\\CdnDatacentersCouldNotBeUpdated but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 2
			path: src/Cdn/Domain/Command/EnableDatacentersForCdnHandler.php

		-
			message: '#^Method Cdn77\\Api\\Cdn\\Domain\\Command\\EnableDatacentersForCdnHandler\:\:handle\(\) throws checked exception Cdn77\\Api\\Cdn\\Domain\\Exception\\CdnNotFound but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Cdn/Domain/Command/EnableDatacentersForCdnHandler.php

		-
			message: '#^Method Cdn77\\Api\\Cdn\\Domain\\Command\\EnableDatacentersForCdnHandler\:\:handle\(\) throws checked exception Cdn77\\Api\\Core\\Domain\\NxgApi\\Exception\\NxgRequestFailed but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Cdn/Domain/Command/EnableDatacentersForCdnHandler.php

		-
			message: '#^Method Cdn77\\Api\\Cdn\\Domain\\Command\\RemoveCdnHandler\:\:handle\(\) throws checked exception Cdn77\\Api\\Cdn\\Domain\\Exception\\CdnAccessNotAllowed but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Cdn/Domain/Command/RemoveCdnHandler.php

		-
			message: '#^Method Cdn77\\Api\\Cdn\\Domain\\Command\\UpdateCdnSettingsHandler\:\:handle\(\) throws checked exception Cdn77\\Api\\Core\\Domain\\NxgApi\\Exception\\NxgRequestFailed but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Cdn/Domain/Command/UpdateCdnSettingsHandler.php

		-
			message: '#^Method Cdn77\\Api\\Cdn\\Domain\\Query\\FindStreamCdnsForCustomerHandler\:\:handle\(\) throws checked exception OutOfBoundsException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Cdn/Domain/Query/FindStreamCdnsForCustomerHandler.php

		-
			message: '#^Method Cdn77\\Api\\Cdn\\Domain\\Query\\GetDatacenterLocationsForCdnHandler\:\:handle\(\) throws checked exception Cdn77\\Api\\Cdn\\Domain\\Exception\\CdnNotFound but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Cdn/Domain/Query/GetDatacenterLocationsForCdnHandler.php

		-
			message: '#^Method Cdn77\\Api\\Cdn\\Domain\\Query\\GetDatacenterLocationsForCdnHandler\:\:handle\(\) throws checked exception Cdn77\\Api\\Core\\Domain\\NxgApi\\Exception\\NxgRequestFailed but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Cdn/Domain/Query/GetDatacenterLocationsForCdnHandler.php

		-
			message: '#^Method Cdn77\\Api\\Cdn\\Domain\\QueryString\\SetupIgnoredQueryParams\:\:execute\(\) throws checked exception Cdn77\\Api\\Cdn\\Domain\\Exception\\QueryStringSetupFailed but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Cdn/Domain/QueryString/SetupIgnoredQueryParams.php

		-
			message: '#^Method Cdn77\\Api\\Cdn\\Domain\\QueryString\\SetupIgnoredQueryParams\:\:execute\(\) throws checked exception OutOfBoundsException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Cdn/Domain/QueryString/SetupIgnoredQueryParams.php

		-
			message: '#^Method Cdn77\\Api\\Cdn\\Domain\\QueryString\\SetupIgnoredQueryParams\:\:validateParametersLimitAndLength\(\) throws checked exception Cdn77\\Api\\Cdn\\Domain\\Exception\\QueryStringSetupFailed but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 2
			path: src/Cdn/Domain/QueryString/SetupIgnoredQueryParams.php

		-
			message: '#^Method Cdn77\\Api\\Cdn\\Infrastructure\\Ceph\\S3ClientApi\:\:getBucketWebsite\(\) throws checked exception Exception but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Cdn/Infrastructure/Ceph/S3ClientApi.php

		-
			message: '#^Method Cdn77\\Api\\Cdn\\Infrastructure\\Ceph\\S3ClientApi\:\:putBucketWebsite\(\) throws checked exception Exception but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Cdn/Infrastructure/Ceph/S3ClientApi.php

		-
			message: '#^Method Cdn77\\Api\\Cdn\\Infrastructure\\Factory\\DbalSequenceCdnLegacyIdFactory\:\:get\(\) throws checked exception Doctrine\\DBAL\\Exception but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Cdn/Infrastructure/Factory/DbalSequenceCdnLegacyIdFactory.php

		-
			message: '#^Method Cdn77\\Api\\Cdn\\Infrastructure\\Finder\\DbalCdnIdFinder\:\:getAccessibleIdsForCustomer\(\) throws checked exception Doctrine\\DBAL\\Exception but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Cdn/Infrastructure/Finder/DbalCdnIdFinder.php

		-
			message: '#^Method Cdn77\\Api\\Cdn\\Infrastructure\\Finder\\DbalCdnIdFinder\:\:getAccessibleIdsForTeamMember\(\) throws checked exception Doctrine\\DBAL\\Exception but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Cdn/Infrastructure/Finder/DbalCdnIdFinder.php

		-
			message: '#^Method Cdn77\\Api\\Cdn\\Infrastructure\\Finder\\DbalCdnIdFinder\:\:getResourceIdsForCustomer\(\) throws checked exception Doctrine\\DBAL\\Exception but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Cdn/Infrastructure/Finder/DbalCdnIdFinder.php

		-
			message: '#^Method Cdn77\\Api\\Cdn\\Infrastructure\\Finder\\DbalStreamCdnIdFinder\:\:findForCustomer\(\) throws checked exception Doctrine\\DBAL\\Exception but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Cdn/Infrastructure/Finder/DbalStreamCdnIdFinder.php

		-
			message: '#^Method Cdn77\\Api\\Cdn\\Infrastructure\\Finder\\DoctrineCdnLabelFinder\:\:findForCustomer\(\) throws checked exception Doctrine\\DBAL\\Exception but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Cdn/Infrastructure/Finder/DoctrineCdnLabelFinder.php

		-
			message: '#^Method Cdn77\\Api\\Cdn\\Infrastructure\\Finder\\DoctrineCustomerCdnCountFinder\:\:findActive\(\) throws checked exception Doctrine\\ORM\\NonUniqueResultException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Cdn/Infrastructure/Finder/DoctrineCustomerCdnCountFinder.php

		-
			message: '#^Method Cdn77\\Api\\Cdn\\Infrastructure\\Finder\\DoctrineCustomerCdnCountFinder\:\:findDeletedFrom\(\) throws checked exception Doctrine\\ORM\\NonUniqueResultException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Cdn/Infrastructure/Finder/DoctrineCustomerCdnCountFinder.php

		-
			message: '#^Method Cdn77\\Api\\Cdn\\Infrastructure\\Finder\\DoctrineCustomerCdnCountFinder\:\:findForStorage\(\) throws checked exception Doctrine\\DBAL\\Exception but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Cdn/Infrastructure/Finder/DoctrineCustomerCdnCountFinder.php

		-
			message: '#^Method Cdn77\\Api\\Cdn\\Infrastructure\\Quota\\DatabaseCreatedCdnQuotaChecker\:\:checkForCustomer\(\) throws checked exception Cdn77\\Api\\Cdn\\Domain\\Exception\\CdnCouldNotBeCreated but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Cdn/Infrastructure/Quota/DatabaseCreatedCdnQuotaChecker.php

		-
			message: '#^Method Cdn77\\Api\\Cdn\\Infrastructure\\Quota\\DatabaseDeletedCdnQuotaChecker\:\:checkForCustomer\(\) throws checked exception Cdn77\\Api\\Cdn\\Domain\\Exception\\CdnRemovalFailed but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Cdn/Infrastructure/Quota/DatabaseDeletedCdnQuotaChecker.php

		-
			message: '#^Method Cdn77\\Api\\Cdn\\Infrastructure\\Repository\\DoctrineCdnHttpProtectionRepository\:\:getForCdn\(\) throws checked exception Cdn77\\Api\\Cdn\\Domain\\Exception\\CdnHttpProtectionIsNotSetup but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Cdn/Infrastructure/Repository/DoctrineCdnHttpProtectionRepository.php

		-
			message: '#^Method Cdn77\\Api\\Cdn\\Infrastructure\\Repository\\DoctrineCdnHttpProtectionRepository\:\:getForCdn\(\) throws checked exception Doctrine\\ORM\\NonUniqueResultException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Cdn/Infrastructure/Repository/DoctrineCdnHttpProtectionRepository.php

		-
			message: '#^Method Cdn77\\Api\\Cdn\\Infrastructure\\Repository\\DoctrineCdnSslRepository\:\:findCurrentlyAssignedForCdnAndSslType\(\) throws checked exception Doctrine\\ORM\\NonUniqueResultException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Cdn/Infrastructure/Repository/DoctrineCdnSslRepository.php

		-
			message: '#^Method Cdn77\\Api\\Cdn\\Infrastructure\\Repository\\DoctrineSslRepository\:\:findForCdnAndType\(\) throws checked exception Doctrine\\ORM\\NonUniqueResultException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Cdn/Infrastructure/Repository/DoctrineSslRepository.php

		-
			message: '#^Method Cdn77\\Api\\Cdn\\Infrastructure\\Repository\\DoctrineStreamCdnRepository\:\:findForCdn\(\) throws checked exception Doctrine\\ORM\\NonUniqueResultException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Cdn/Infrastructure/Repository/DoctrineStreamCdnRepository.php

		-
			message: '#^Method Cdn77\\Api\\Cdn\\Infrastructure\\Repository\\DoctrineStreamCdnRepository\:\:getForCdn\(\) throws checked exception Cdn77\\Api\\Cdn\\Domain\\Exception\\CdnNotFound but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Cdn/Infrastructure/Repository/DoctrineStreamCdnRepository.php

		-
			message: '#^Method Cdn77\\Api\\Cdn\\Infrastructure\\Repository\\DoctrineStreamOriginRepository\:\:get\(\) throws checked exception Cdn77\\Api\\Cdn\\Domain\\Exception\\OriginCouldNotBeResolved but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Cdn/Infrastructure/Repository/DoctrineStreamOriginRepository.php

		-
			message: '#^Method Cdn77\\Api\\Cname\\Domain\\Query\\FindCdnCnamesHandler\:\:handle\(\) throws checked exception Cdn77\\Api\\Cdn\\Domain\\Exception\\CdnNotFound but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Cname/Domain/Query/FindCdnCnamesHandler.php

		-
			message: '#^Method Cdn77\\Api\\Cname\\Infrastructure\\Finder\\DbalCnameCountFinder\:\:findForCdn\(\) throws checked exception Doctrine\\DBAL\\Exception but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Cname/Infrastructure/Finder/DbalCnameCountFinder.php

		-
			message: '#^Method Cdn77\\Api\\Cname\\Infrastructure\\Finder\\DbalSslCertificateWithActiveCdnsFinder\:\:findAllForTypeAndRating\(\) throws checked exception Doctrine\\DBAL\\Exception but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Cname/Infrastructure/Finder/DbalSslCertificateWithActiveCdnsFinder.php

		-
			message: '#^Method Cdn77\\Api\\Core\\Application\\Container\\ExceptionResponseHandlerPass\:\:process\(\) throws checked exception Symfony\\Component\\DependencyInjection\\Exception\\InvalidArgumentException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Core/Application/Container/ExceptionResponseHandlerPass.php

		-
			message: '#^Method Cdn77\\Api\\Core\\Application\\Container\\ExceptionResponseHandlerPass\:\:process\(\) throws checked exception Symfony\\Component\\DependencyInjection\\Exception\\ServiceNotFoundException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Core/Application/Container/ExceptionResponseHandlerPass.php

		-
			message: '#^Method Cdn77\\Api\\Core\\Application\\Container\\MockedHttpClientPass\:\:process\(\) throws checked exception Symfony\\Component\\DependencyInjection\\Exception\\BadMethodCallException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Core/Application/Container/MockedHttpClientPass.php

		-
			message: '#^Method Cdn77\\Api\\Core\\Application\\Container\\MockedHttpClientPass\:\:process\(\) throws checked exception Symfony\\Component\\DependencyInjection\\Exception\\OutOfBoundsException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Core/Application/Container/MockedHttpClientPass.php

		-
			message: '#^Method Cdn77\\Api\\Core\\Application\\Container\\MockedHttpClientPass\:\:process\(\) throws checked exception Symfony\\Component\\DependencyInjection\\Exception\\ServiceNotFoundException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Core/Application/Container/MockedHttpClientPass.php

		-
			message: '#^Method Cdn77\\Api\\Core\\Application\\Container\\OpenApiPathsCompilerPass\:\:process\(\) throws checked exception Symfony\\Component\\DependencyInjection\\Exception\\InvalidArgumentException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Core/Application/Container/OpenApiPathsCompilerPass.php

		-
			message: '#^Method Cdn77\\Api\\Core\\Application\\Container\\OpenApiPathsCompilerPass\:\:process\(\) throws checked exception Symfony\\Component\\DependencyInjection\\Exception\\ServiceNotFoundException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Core/Application/Container/OpenApiPathsCompilerPass.php

		-
			message: '#^Method Cdn77\\Api\\Core\\Application\\Controller\\DocumentationJsonController\:\:execute\(\) throws checked exception InvalidArgumentException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 2
			path: src/Core/Application/Controller/DocumentationJsonController.php

		-
			message: '#^Method Cdn77\\Api\\Core\\Application\\Controller\\InternalDocumentationJsonController\:\:execute\(\) throws checked exception InvalidArgumentException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 2
			path: src/Core/Application/Controller/InternalDocumentationJsonController.php

		-
			message: '#^Method Cdn77\\Api\\Core\\Application\\Controller\\PingController\:\:pingAction\(\) throws checked exception InvalidArgumentException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Core/Application/Controller/PingController.php

		-
			message: '#^Method Cdn77\\Api\\Core\\Application\\Controller\\PingInternalController\:\:pingInternalAction\(\) throws checked exception InvalidArgumentException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Core/Application/Controller/PingInternalController.php

		-
			message: '#^Method Cdn77\\Api\\Core\\Application\\EventHandler\\ExceptionListener\:\:onKernelException\(\) throws checked exception JMS\\Serializer\\Exception\\RuntimeException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Core/Application/EventHandler/ExceptionListener.php

		-
			message: '#^Method Cdn77\\Api\\Core\\Application\\EventHandler\\Handler\\DomainExceptionHandler\:\:handle\(\) throws checked exception JMS\\Serializer\\Exception\\RuntimeException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Core/Application/EventHandler/Handler/DomainExceptionHandler.php

		-
			message: '#^Method Cdn77\\Api\\Core\\Application\\EventHandler\\Handler\\JsonExceptionHandler\:\:handle\(\) throws checked exception JMS\\Serializer\\Exception\\RuntimeException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Core/Application/EventHandler/Handler/JsonExceptionHandler.php

		-
			message: '#^Method Cdn77\\Api\\Core\\Application\\EventHandler\\Handler\\MethodNotAllowedHttpExceptionHandler\:\:handle\(\) throws checked exception JMS\\Serializer\\Exception\\RuntimeException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Core/Application/EventHandler/Handler/MethodNotAllowedHttpExceptionHandler.php

		-
			message: '#^Method Cdn77\\Api\\Core\\Application\\EventHandler\\Handler\\NotFoundHttpExceptionHandler\:\:handle\(\) throws checked exception JMS\\Serializer\\Exception\\RuntimeException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Core/Application/EventHandler/Handler/NotFoundHttpExceptionHandler.php

		-
			message: '#^Method Cdn77\\Api\\Core\\Application\\EventHandler\\Handler\\SerializerRuntimeExceptionHandler\:\:handle\(\) throws checked exception JMS\\Serializer\\Exception\\RuntimeException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Core/Application/EventHandler/Handler/SerializerRuntimeExceptionHandler.php

		-
			message: '#^Method Cdn77\\Api\\Core\\Application\\OpenApi\\ErrorsOpenApiResponse\:\:spec\(\) throws checked exception cebe\\openapi\\exceptions\\TypeErrorException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Core/Application/OpenApi/ErrorsOpenApiResponse.php

		-
			message: '#^Method Cdn77\\Api\\Core\\Application\\OpenApi\\FieldsErrorsOpenApiResponse\:\:spec\(\) throws checked exception cebe\\openapi\\exceptions\\TypeErrorException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Core/Application/OpenApi/FieldsErrorsOpenApiResponse.php

		-
			message: '#^Method Cdn77\\Api\\Core\\Application\\OpenApi\\OpenApiSpec\:\:getSpecForConfig\(\) throws checked exception Exception but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Core/Application/OpenApi/OpenApiSpec.php

		-
			message: '#^Method Cdn77\\Api\\Core\\Application\\OpenApi\\PathGenerator\:\:generate\(\) throws checked exception Symfony\\Component\\Routing\\Exception\\InvalidParameterException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Core/Application/OpenApi/PathGenerator.php

		-
			message: '#^Method Cdn77\\Api\\Core\\Application\\OpenApi\\PathGenerator\:\:generate\(\) throws checked exception Symfony\\Component\\Routing\\Exception\\MissingMandatoryParametersException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Core/Application/OpenApi/PathGenerator.php

		-
			message: '#^Method Cdn77\\Api\\Core\\Application\\OpenApi\\PathGenerator\:\:generate\(\) throws checked exception Symfony\\Component\\Routing\\Exception\\RouteNotFoundException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Core/Application/OpenApi/PathGenerator.php

		-
			message: '#^Method Cdn77\\Api\\Core\\Application\\Payload\\SchemaPropertyResolver\:\:requireNotNull\(\) throws checked exception Cdn77\\Api\\Core\\Application\\Exception\\InvalidType but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Core/Application/Payload/SchemaPropertyResolver.php

		-
			message: '#^Method Cdn77\\Api\\Core\\Application\\Request\\ControllerSchemaSerializer\:\:serializeToResponse\(\) throws checked exception JMS\\Serializer\\Exception\\RuntimeException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Core/Application/Request/ControllerSchemaSerializer.php

		-
			message: '#^Method Cdn77\\Api\\Core\\Application\\Response\\AccessDeniedExceptionHandler\:\:handle\(\) throws checked exception JMS\\Serializer\\Exception\\RuntimeException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Core/Application/Response/AccessDeniedExceptionHandler.php

		-
			message: '#^Method Cdn77\\Api\\Core\\Application\\Response\\ControllerCommandHandler\:\:handle\(\) throws checked exception InvalidArgumentException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Core/Application/Response/ControllerCommandHandler.php

		-
			message: '#^Method Cdn77\\Api\\Core\\Application\\Response\\DomainExceptionResponseResolver\:\:resolve\(\) throws checked exception JMS\\Serializer\\Exception\\RuntimeException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 5
			path: src/Core/Application/Response/DomainExceptionResponseResolver.php

		-
			message: '#^Method Cdn77\\Api\\Core\\Application\\Response\\ExceptionHandler\:\:handle\(\) throws checked exception JMS\\Serializer\\Exception\\RuntimeException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 5
			path: src/Core/Application/Response/ExceptionHandler.php

		-
			message: '#^Method Cdn77\\Api\\Core\\Console\\PruneCommand\:\:execute\(\) throws checked exception Brick\\Math\\Exception\\MathException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Core/Console/PruneCommand.php

		-
			message: '#^Method Cdn77\\Api\\Core\\Console\\SaveDisabledRateLimitedTokenIdsCommand\:\:execute\(\) throws checked exception Safe\\Exceptions\\FilesystemException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Core/Console/SaveDisabledRateLimitedTokenIdsCommand.php

		-
			message: '#^Method Cdn77\\Api\\Core\\Domain\\Billing\\Converter\\VatRateDbConverter\:\:convertToDatabaseValue\(\) throws checked exception Brick\\Math\\Exception\\MathException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Core/Domain/Billing/Converter/VatRateDbConverter.php

		-
			message: '#^Method Cdn77\\Api\\Core\\Domain\\Billing\\Converter\\VatRateDbConverter\:\:convertToPHPValue\(\) throws checked exception Brick\\Math\\Exception\\DivisionByZeroException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Core/Domain/Billing/Converter/VatRateDbConverter.php

		-
			message: '#^Method Cdn77\\Api\\Core\\Domain\\Billing\\Converter\\VatRateDbConverter\:\:convertToPHPValue\(\) throws checked exception Brick\\Math\\Exception\\MathException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Core/Domain/Billing/Converter/VatRateDbConverter.php

		-
			message: '#^Method Cdn77\\Api\\Core\\Domain\\Billing\\Converter\\VatRateDbConverter\:\:convertToPHPValue\(\) throws checked exception Brick\\Math\\Exception\\NumberFormatException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Core/Domain/Billing/Converter/VatRateDbConverter.php

		-
			message: '#^Method Cdn77\\Api\\Core\\Domain\\Billing\\Converter\\VatRateDbConverter\:\:convertToPHPValue\(\) throws checked exception Brick\\Math\\Exception\\RoundingNecessaryException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Core/Domain/Billing/Converter/VatRateDbConverter.php

		-
			message: '#^Method Cdn77\\Api\\Core\\Domain\\Billing\\Converter\\VatRateDbConverter\:\:convertToPHPValue\(\) throws checked exception InvalidArgumentException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Core/Domain/Billing/Converter/VatRateDbConverter.php

		-
			message: '#^Method Cdn77\\Api\\Core\\Domain\\Billing\\Formatter\\VatRateFormatter\:\:format\(\) throws checked exception Brick\\Math\\Exception\\MathException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 2
			path: src/Core/Domain/Billing/Formatter/VatRateFormatter.php

		-
			message: '#^Method Cdn77\\Api\\Core\\Domain\\Billing\\Value\\VatRate\:\:fromDecimal\(\) throws checked exception Brick\\Math\\Exception\\DivisionByZeroException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Core/Domain/Billing/Value/VatRate.php

		-
			message: '#^Method Cdn77\\Api\\Core\\Domain\\Billing\\Value\\VatRate\:\:fromDecimal\(\) throws checked exception Brick\\Math\\Exception\\NumberFormatException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Core/Domain/Billing/Value/VatRate.php

		-
			message: '#^Method Cdn77\\Api\\Core\\Domain\\Billing\\Value\\VatRate\:\:fromDecimal\(\) throws checked exception Brick\\Math\\Exception\\RoundingNecessaryException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Core/Domain/Billing/Value/VatRate.php

		-
			message: '#^Method Cdn77\\Api\\Core\\Domain\\Billing\\Value\\VatRate\:\:fromXeroInvoice\(\) throws checked exception Brick\\Math\\Exception\\DivisionByZeroException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Core/Domain/Billing/Value/VatRate.php

		-
			message: '#^Method Cdn77\\Api\\Core\\Domain\\Billing\\Value\\VatRate\:\:fromXeroInvoice\(\) throws checked exception Brick\\Math\\Exception\\MathException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Core/Domain/Billing/Value/VatRate.php

		-
			message: '#^Method Cdn77\\Api\\Core\\Domain\\Billing\\Value\\VatRate\:\:fromXeroInvoice\(\) throws checked exception Brick\\Math\\Exception\\NumberFormatException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Core/Domain/Billing/Value/VatRate.php

		-
			message: '#^Method Cdn77\\Api\\Core\\Domain\\Billing\\Value\\VatRate\:\:fromXeroInvoice\(\) throws checked exception Brick\\Math\\Exception\\RoundingNecessaryException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Core/Domain/Billing/Value/VatRate.php

		-
			message: '#^Method Cdn77\\Api\\Core\\Domain\\Billing\\Value\\VatRate\:\:fromXeroInvoice\(\) throws checked exception InvalidArgumentException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Core/Domain/Billing/Value/VatRate.php

		-
			message: '#^Method Cdn77\\Api\\Core\\Domain\\Entity\\Cdn\\Cdn\:\:activateRawLogs\(\) throws checked exception Cdn77\\Api\\Core\\Domain\\NxgApi\\Exception\\NxgRequestFailed but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Core/Domain/Entity/Cdn/Cdn.php

		-
			message: '#^Method Cdn77\\Api\\Core\\Domain\\Entity\\Cdn\\Cdn\:\:changeLocationGroup\(\) throws checked exception Cdn77\\Api\\Core\\Domain\\NxgApi\\Exception\\NxgRequestFailed but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Core/Domain/Entity/Cdn/Cdn.php

		-
			message: '#^Method Cdn77\\Api\\Core\\Domain\\Entity\\Cdn\\Cdn\:\:deactivateRawLogs\(\) throws checked exception Cdn77\\Api\\Core\\Domain\\NxgApi\\Exception\\NxgRequestFailed but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Core/Domain/Entity/Cdn/Cdn.php

		-
			message: '#^Method Cdn77\\Api\\Core\\Domain\\Entity\\Cdn\\Cdn\:\:remove\(\) throws checked exception Cdn77\\Api\\Core\\Domain\\NxgApi\\Exception\\NxgRequestFailed but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Core/Domain/Entity/Cdn/Cdn.php

		-
			message: '#^Method Cdn77\\Api\\Core\\Domain\\Entity\\Cdn\\Cdn\:\:rename\(\) throws checked exception Cdn77\\Api\\Cdn\\Domain\\Exception\\CdnUpdateFailed but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Core/Domain/Entity/Cdn/Cdn.php

		-
			message: '#^Method Cdn77\\Api\\Core\\Domain\\Entity\\Cdn\\Cdn\:\:suspend\(\) throws checked exception Cdn77\\Api\\Core\\Domain\\NxgApi\\Exception\\NxgRequestFailed but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Core/Domain/Entity/Cdn/Cdn.php

		-
			message: '#^Method Cdn77\\Api\\Core\\Domain\\Entity\\Cdn\\Cdn\:\:unsuspend\(\) throws checked exception Cdn77\\Api\\Core\\Domain\\NxgApi\\Exception\\NxgRequestFailed but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Core/Domain/Entity/Cdn/Cdn.php

		-
			message: '#^Method Cdn77\\Api\\Core\\Domain\\Entity\\Cdn\\CdnId\:\:getSchemaSpec\(\) throws checked exception cebe\\openapi\\exceptions\\TypeErrorException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Core/Domain/Entity/Cdn/CdnId.php

		-
			message: '#^Method Cdn77\\Api\\Core\\Domain\\Entity\\Currency\\ExchangeRate\:\:getUsdToBrl\(\) throws checked exception Brick\\Math\\Exception\\DivisionByZeroException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Core/Domain/Entity/Currency/ExchangeRate.php

		-
			message: '#^Method Cdn77\\Api\\Core\\Domain\\Entity\\Currency\\ExchangeRate\:\:getUsdToBrl\(\) throws checked exception Brick\\Math\\Exception\\NumberFormatException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Core/Domain/Entity/Currency/ExchangeRate.php

		-
			message: '#^Method Cdn77\\Api\\Core\\Domain\\Entity\\Currency\\ExchangeRate\:\:getUsdToBrl\(\) throws checked exception Brick\\Math\\Exception\\RoundingNecessaryException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Core/Domain/Entity/Currency/ExchangeRate.php

		-
			message: '#^Method Cdn77\\Api\\Core\\Domain\\Entity\\Currency\\ExchangeRate\:\:getUsdToCzk\(\) throws checked exception Brick\\Math\\Exception\\DivisionByZeroException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Core/Domain/Entity/Currency/ExchangeRate.php

		-
			message: '#^Method Cdn77\\Api\\Core\\Domain\\Entity\\Currency\\ExchangeRate\:\:getUsdToCzk\(\) throws checked exception Brick\\Math\\Exception\\NumberFormatException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Core/Domain/Entity/Currency/ExchangeRate.php

		-
			message: '#^Method Cdn77\\Api\\Core\\Domain\\Entity\\Currency\\ExchangeRate\:\:getUsdToCzk\(\) throws checked exception Brick\\Math\\Exception\\RoundingNecessaryException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Core/Domain/Entity/Currency/ExchangeRate.php

		-
			message: '#^Method Cdn77\\Api\\Core\\Domain\\Entity\\Currency\\ExchangeRate\:\:getUsdToEur\(\) throws checked exception Brick\\Math\\Exception\\DivisionByZeroException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Core/Domain/Entity/Currency/ExchangeRate.php

		-
			message: '#^Method Cdn77\\Api\\Core\\Domain\\Entity\\Currency\\ExchangeRate\:\:getUsdToEur\(\) throws checked exception Brick\\Math\\Exception\\NumberFormatException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Core/Domain/Entity/Currency/ExchangeRate.php

		-
			message: '#^Method Cdn77\\Api\\Core\\Domain\\Entity\\Currency\\ExchangeRate\:\:getUsdToEur\(\) throws checked exception Brick\\Math\\Exception\\RoundingNecessaryException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Core/Domain/Entity/Currency/ExchangeRate.php

		-
			message: '#^Method Cdn77\\Api\\Core\\Domain\\Entity\\Currency\\ExchangeRate\:\:getUsdToGbp\(\) throws checked exception Brick\\Math\\Exception\\DivisionByZeroException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Core/Domain/Entity/Currency/ExchangeRate.php

		-
			message: '#^Method Cdn77\\Api\\Core\\Domain\\Entity\\Currency\\ExchangeRate\:\:getUsdToGbp\(\) throws checked exception Brick\\Math\\Exception\\NumberFormatException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Core/Domain/Entity/Currency/ExchangeRate.php

		-
			message: '#^Method Cdn77\\Api\\Core\\Domain\\Entity\\Currency\\ExchangeRate\:\:getUsdToGbp\(\) throws checked exception Brick\\Math\\Exception\\RoundingNecessaryException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Core/Domain/Entity/Currency/ExchangeRate.php

		-
			message: '#^Method Cdn77\\Api\\Core\\Domain\\Entity\\CustomPlan\\Contract\:\:getPlanVolume\(\) throws checked exception Brick\\Math\\Exception\\DivisionByZeroException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Core/Domain/Entity/CustomPlan/Contract.php

		-
			message: '#^Method Cdn77\\Api\\Core\\Domain\\Entity\\CustomPlan\\Contract\:\:getPlanVolume\(\) throws checked exception Brick\\Math\\Exception\\NumberFormatException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Core/Domain/Entity/CustomPlan/Contract.php

		-
			message: '#^Method Cdn77\\Api\\Core\\Domain\\Entity\\CustomPlan\\Contract\:\:getPlanVolume\(\) throws checked exception Brick\\Math\\Exception\\RoundingNecessaryException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Core/Domain/Entity/CustomPlan/Contract.php

		-
			message: '#^Method Cdn77\\Api\\Core\\Domain\\Entity\\CustomPlan\\Contract\:\:getPrice\(\) throws checked exception Brick\\Math\\Exception\\NumberFormatException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Core/Domain/Entity/CustomPlan/Contract.php

		-
			message: '#^Method Cdn77\\Api\\Core\\Domain\\Entity\\CustomPlan\\Contract\:\:getPrice\(\) throws checked exception Brick\\Math\\Exception\\RoundingNecessaryException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Core/Domain/Entity/CustomPlan/Contract.php

		-
			message: '#^Method Cdn77\\Api\\Core\\Domain\\Entity\\CustomPlan\\Contract\:\:getPrice\(\) throws checked exception Brick\\Money\\Exception\\UnknownCurrencyException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Core/Domain/Entity/CustomPlan/Contract.php

		-
			message: '#^Method Cdn77\\Api\\Core\\Domain\\Entity\\Datacenter\\Location\:\:getLatitude\(\) throws checked exception Brick\\Math\\Exception\\DivisionByZeroException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Core/Domain/Entity/Datacenter/Location.php

		-
			message: '#^Method Cdn77\\Api\\Core\\Domain\\Entity\\Datacenter\\Location\:\:getLatitude\(\) throws checked exception Brick\\Math\\Exception\\NumberFormatException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Core/Domain/Entity/Datacenter/Location.php

		-
			message: '#^Method Cdn77\\Api\\Core\\Domain\\Entity\\Datacenter\\Location\:\:getLatitude\(\) throws checked exception Brick\\Math\\Exception\\RoundingNecessaryException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Core/Domain/Entity/Datacenter/Location.php

		-
			message: '#^Method Cdn77\\Api\\Core\\Domain\\Entity\\Datacenter\\Location\:\:getLongitude\(\) throws checked exception Brick\\Math\\Exception\\DivisionByZeroException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Core/Domain/Entity/Datacenter/Location.php

		-
			message: '#^Method Cdn77\\Api\\Core\\Domain\\Entity\\Datacenter\\Location\:\:getLongitude\(\) throws checked exception Brick\\Math\\Exception\\NumberFormatException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Core/Domain/Entity/Datacenter/Location.php

		-
			message: '#^Method Cdn77\\Api\\Core\\Domain\\Entity\\Datacenter\\Location\:\:getLongitude\(\) throws checked exception Brick\\Math\\Exception\\RoundingNecessaryException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Core/Domain/Entity/Datacenter/Location.php

		-
			message: '#^Method Cdn77\\Api\\Core\\Domain\\Entity\\Invoice\\Invoice\:\:total\(\) throws checked exception Brick\\Math\\Exception\\NumberFormatException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Core/Domain/Entity/Invoice/Invoice.php

		-
			message: '#^Method Cdn77\\Api\\Core\\Domain\\Entity\\Invoice\\Invoice\:\:total\(\) throws checked exception Brick\\Math\\Exception\\RoundingNecessaryException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Core/Domain/Entity/Invoice/Invoice.php

		-
			message: '#^Method Cdn77\\Api\\Core\\Domain\\Entity\\Invoice\\Invoice\:\:total\(\) throws checked exception Brick\\Money\\Exception\\UnknownCurrencyException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Core/Domain/Entity/Invoice/Invoice.php

		-
			message: '#^Method Cdn77\\Api\\Core\\Domain\\Entity\\Invoice\\InvoiceCustomer\:\:getCountry\(\) throws checked exception Cdn77\\Api\\Invoice\\Domain\\Exception\\NoCountryAssignedToCustomer but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Core/Domain/Entity/Invoice/InvoiceCustomer.php

		-
			message: '#^Method Cdn77\\Api\\Core\\Domain\\Entity\\Origin\\Origin\:\:updateLabel\(\) throws checked exception Cdn77\\Api\\Origin\\Domain\\Exception\\DuplicateOriginFound but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Core/Domain/Entity/Origin/Origin.php

		-
			message: '#^Method Cdn77\\Api\\Core\\Domain\\Entity\\Origin\\OriginId\:\:getSchemaSpec\(\) throws checked exception cebe\\openapi\\exceptions\\TypeErrorException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Core/Domain/Entity/Origin/OriginId.php

		-
			message: '#^Method Cdn77\\Api\\Core\\Domain\\Entity\\Origin\\OriginId\:\:getSchemaSpecWithSectionLink\(\) throws checked exception cebe\\openapi\\exceptions\\TypeErrorException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Core/Domain/Entity/Origin/OriginId.php

		-
			message: '#^Method Cdn77\\Api\\Core\\Domain\\Entity\\Payment\\PaymentRecipe\:\:updateLabel\(\) throws checked exception Cdn77\\Api\\Payment\\Domain\\Exception\\DuplicateCreditCardFound but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Core/Domain/Entity/Payment/PaymentRecipe.php

		-
			message: '#^Method Cdn77\\Api\\Core\\Domain\\Entity\\Plan\\Plan\:\:__construct\(\) throws checked exception Brick\\Math\\Exception\\MathException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Core/Domain/Entity/Plan/Plan.php

		-
			message: '#^Method Cdn77\\Api\\Core\\Domain\\Entity\\Plan\\Plan\:\:getPrice\(\) throws checked exception Brick\\Math\\Exception\\NumberFormatException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Core/Domain/Entity/Plan/Plan.php

		-
			message: '#^Method Cdn77\\Api\\Core\\Domain\\Entity\\Plan\\Plan\:\:getPrice\(\) throws checked exception Brick\\Math\\Exception\\RoundingNecessaryException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Core/Domain/Entity/Plan/Plan.php

		-
			message: '#^Method Cdn77\\Api\\Core\\Domain\\Entity\\Plan\\Plan\:\:getPrice\(\) throws checked exception Brick\\Money\\Exception\\UnknownCurrencyException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Core/Domain/Entity/Plan/Plan.php

		-
			message: '#^Method Cdn77\\Api\\Core\\Domain\\Entity\\RawLogs\\RawLog\:\:__construct\(\) throws checked exception Brick\\Math\\Exception\\MathException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Core/Domain/Entity/RawLogs/RawLog.php

		-
			message: '#^Method Cdn77\\Api\\Core\\Domain\\Entity\\Storage\\Storage\:\:markDeleted\(\) throws checked exception Cdn77\\Api\\Storage\\Domain\\Exception\\StorageAlreadyDeleted but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Core/Domain/Entity/Storage/Storage.php

		-
			message: '#^Method Cdn77\\Api\\Core\\Domain\\Entity\\Storage\\Storage\:\:rename\(\) throws checked exception Cdn77\\Api\\Storage\\Domain\\Exception\\CouldNotCreateStorage but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Core/Domain/Entity/Storage/Storage.php

		-
			message: '#^Method Cdn77\\Api\\Core\\Domain\\Entity\\Storage\\StorageAddOn\:\:__construct\(\) throws checked exception Brick\\Math\\Exception\\MathException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Core/Domain/Entity/Storage/StorageAddOn.php

		-
			message: '#^Method Cdn77\\Api\\Core\\Domain\\Entity\\Storage\\StorageAddOn\:\:getPrice\(\) throws checked exception Brick\\Math\\Exception\\NumberFormatException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Core/Domain/Entity/Storage/StorageAddOn.php

		-
			message: '#^Method Cdn77\\Api\\Core\\Domain\\Entity\\Storage\\StorageAddOn\:\:getPrice\(\) throws checked exception Brick\\Math\\Exception\\RoundingNecessaryException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Core/Domain/Entity/Storage/StorageAddOn.php

		-
			message: '#^Method Cdn77\\Api\\Core\\Domain\\Entity\\Storage\\StorageAddOn\:\:getPrice\(\) throws checked exception Brick\\Money\\Exception\\UnknownCurrencyException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Core/Domain/Entity/Storage/StorageAddOn.php

		-
			message: '#^Method Cdn77\\Api\\Core\\Domain\\Entity\\Storage\\StoragePlan\:\:__construct\(\) throws checked exception Brick\\Math\\Exception\\MathException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Core/Domain/Entity/Storage/StoragePlan.php

		-
			message: '#^Method Cdn77\\Api\\Core\\Domain\\Entity\\Storage\\StoragePlan\:\:getPrice\(\) throws checked exception Brick\\Math\\Exception\\NumberFormatException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Core/Domain/Entity/Storage/StoragePlan.php

		-
			message: '#^Method Cdn77\\Api\\Core\\Domain\\Entity\\Storage\\StoragePlan\:\:getPrice\(\) throws checked exception Brick\\Math\\Exception\\RoundingNecessaryException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Core/Domain/Entity/Storage/StoragePlan.php

		-
			message: '#^Method Cdn77\\Api\\Core\\Domain\\Entity\\Storage\\StoragePlan\:\:getPrice\(\) throws checked exception Brick\\Money\\Exception\\UnknownCurrencyException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Core/Domain/Entity/Storage/StoragePlan.php

		-
			message: '#^Method Cdn77\\Api\\Core\\Domain\\Entity\\Tariff\\Credit\:\:create\(\) throws checked exception Brick\\Math\\Exception\\MathException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Core/Domain/Entity/Tariff/Credit.php

		-
			message: '#^Method Cdn77\\Api\\Core\\Domain\\Entity\\Tariff\\Credit\:\:create\(\) throws checked exception Brick\\Money\\Exception\\MoneyMismatchException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Core/Domain/Entity/Tariff/Credit.php

		-
			message: '#^Method Cdn77\\Api\\Core\\Domain\\Entity\\Tariff\\Credit\:\:getCurrentCredit\(\) throws checked exception Brick\\Math\\Exception\\MathException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 2
			path: src/Core/Domain/Entity/Tariff/Credit.php

		-
			message: '#^Method Cdn77\\Api\\Core\\Domain\\Entity\\Tariff\\Credit\:\:getCurrentCredit\(\) throws checked exception Brick\\Money\\Exception\\MoneyMismatchException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 2
			path: src/Core/Domain/Entity/Tariff/Credit.php

		-
			message: '#^Method Cdn77\\Api\\Core\\Domain\\Entity\\Tariff\\Credit\:\:newCreditWithResidue\(\) throws checked exception Brick\\Math\\Exception\\MathException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 3
			path: src/Core/Domain/Entity/Tariff/Credit.php

		-
			message: '#^Method Cdn77\\Api\\Core\\Domain\\Entity\\Tariff\\Credit\:\:newCreditWithResidue\(\) throws checked exception Brick\\Money\\Exception\\MoneyMismatchException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 3
			path: src/Core/Domain/Entity/Tariff/Credit.php

		-
			message: '#^Method Cdn77\\Api\\Core\\Domain\\Entity\\Tariff\\Credit\:\:withdraw\(\) throws checked exception Brick\\Math\\Exception\\MathException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Core/Domain/Entity/Tariff/Credit.php

		-
			message: '#^Method Cdn77\\Api\\Core\\Domain\\Entity\\Tariff\\Credit\:\:withdraw\(\) throws checked exception Brick\\Money\\Exception\\MoneyMismatchException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Core/Domain/Entity/Tariff/Credit.php

		-
			message: '#^Method Cdn77\\Api\\Core\\Domain\\Entity\\Tariff\\CreditHistory\:\:creditMoney\(\) throws checked exception Brick\\Math\\Exception\\NumberFormatException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Core/Domain/Entity/Tariff/CreditHistory.php

		-
			message: '#^Method Cdn77\\Api\\Core\\Domain\\Entity\\Tariff\\CreditHistory\:\:creditMoney\(\) throws checked exception Brick\\Math\\Exception\\RoundingNecessaryException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Core/Domain/Entity/Tariff/CreditHistory.php

		-
			message: '#^Method Cdn77\\Api\\Core\\Domain\\Entity\\Tariff\\CreditHistory\:\:creditMoney\(\) throws checked exception Brick\\Money\\Exception\\UnknownCurrencyException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Core/Domain/Entity/Tariff/CreditHistory.php

		-
			message: '#^Method Cdn77\\Api\\Core\\Domain\\Entity\\Tariff\\TariffLog\:\:getCreditChange\(\) throws checked exception Brick\\Math\\Exception\\NumberFormatException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Core/Domain/Entity/Tariff/TariffLog.php

		-
			message: '#^Method Cdn77\\Api\\Core\\Domain\\Entity\\Tariff\\TariffLog\:\:getCreditChange\(\) throws checked exception Brick\\Math\\Exception\\RoundingNecessaryException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Core/Domain/Entity/Tariff/TariffLog.php

		-
			message: '#^Method Cdn77\\Api\\Core\\Domain\\Entity\\Tariff\\TariffLog\:\:getCreditChange\(\) throws checked exception Brick\\Money\\Exception\\UnknownCurrencyException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Core/Domain/Entity/Tariff/TariffLog.php

		-
			message: '#^Method Cdn77\\Api\\Core\\Domain\\Entity\\Tariff\\TariffLog\:\:getInitialCredit\(\) throws checked exception Brick\\Math\\Exception\\NumberFormatException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Core/Domain/Entity/Tariff/TariffLog.php

		-
			message: '#^Method Cdn77\\Api\\Core\\Domain\\Entity\\Tariff\\TariffLog\:\:getInitialCredit\(\) throws checked exception Brick\\Math\\Exception\\RoundingNecessaryException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Core/Domain/Entity/Tariff/TariffLog.php

		-
			message: '#^Method Cdn77\\Api\\Core\\Domain\\Entity\\Tariff\\TariffLog\:\:getInitialCredit\(\) throws checked exception Brick\\Money\\Exception\\UnknownCurrencyException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Core/Domain/Entity/Tariff/TariffLog.php

		-
			message: '#^Method Cdn77\\Api\\Core\\Domain\\Resolver\\AffectedCustomerResolver\:\:resolve\(\) throws checked exception Cdn77\\Api\\Core\\Domain\\Exception\\Customer\\CustomerNotFound but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Core/Domain/Resolver/AffectedCustomerResolver.php

		-
			message: '#^Method Cdn77\\Api\\Core\\Domain\\Resolver\\AffectedCustomerResolver\:\:resolveActive\(\) throws checked exception Cdn77\\Api\\Core\\Domain\\Exception\\Customer\\CustomerIsSuspended but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Core/Domain/Resolver/AffectedCustomerResolver.php

		-
			message: '#^Method Cdn77\\Api\\Core\\Domain\\Resolver\\AffectedCustomerResolver\:\:resolveSuspended\(\) throws checked exception Cdn77\\Api\\Core\\Domain\\Exception\\Customer\\CustomerIsTerminated but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Core/Domain/Resolver/AffectedCustomerResolver.php

		-
			message: '#^Method Cdn77\\Api\\Core\\Domain\\Stats\\StatsProviderBag\:\:get\(\) throws checked exception OutOfBoundsException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Core/Domain/Stats/StatsProviderBag.php

		-
			message: '#^Method Cdn77\\Api\\Core\\Infrastructure\\Ara\\HttpAra\:\:closePlan\(\) throws checked exception Brick\\Math\\Exception\\MathException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Core/Infrastructure/Ara/HttpAra.php

		-
			message: '#^Method Cdn77\\Api\\Core\\Infrastructure\\Ara\\HttpAra\:\:createPlan\(\) throws checked exception Brick\\Math\\Exception\\MathException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Core/Infrastructure/Ara/HttpAra.php

		-
			message: '#^Method Cdn77\\Api\\Core\\Infrastructure\\Ara\\HttpAra\:\:requestPriceSum\(\) throws checked exception Brick\\Math\\Exception\\NumberFormatException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Core/Infrastructure/Ara/HttpAra.php

		-
			message: '#^Method Cdn77\\Api\\Core\\Infrastructure\\Ara\\HttpAra\:\:requestPriceSum\(\) throws checked exception Brick\\Math\\Exception\\RoundingNecessaryException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Core/Infrastructure/Ara/HttpAra.php

		-
			message: '#^Method Cdn77\\Api\\Core\\Infrastructure\\Ara\\HttpAra\:\:requestPriceSum\(\) throws checked exception Brick\\Money\\Exception\\UnknownCurrencyException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Core/Infrastructure/Ara/HttpAra.php

		-
			message: '#^Method Cdn77\\Api\\Core\\Infrastructure\\Billing\\UsdXeroBiller\:\:addInvoice\(\) throws checked exception Cdn77\\Api\\Core\\Domain\\Billing\\Exception\\CouldNotCreateInvoice but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Core/Infrastructure/Billing/UsdXeroBiller.php

		-
			message: '#^Method Cdn77\\Api\\Core\\Infrastructure\\Billing\\UsdXeroBiller\:\:addInvoiceForPayment\(\) throws checked exception XeroPHP\\Exception but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Core/Infrastructure/Billing/UsdXeroBiller.php

		-
			message: '#^Method Cdn77\\Api\\Core\\Infrastructure\\Billing\\UsdXeroBiller\:\:addPayment\(\) throws checked exception XeroPHP\\Remote\\Exception but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Core/Infrastructure/Billing/UsdXeroBiller.php

		-
			message: '#^Method Cdn77\\Api\\Core\\Infrastructure\\Billing\\UsdXeroBiller\:\:authorizeInvoice\(\) throws checked exception XeroPHP\\Exception but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Core/Infrastructure/Billing/UsdXeroBiller.php

		-
			message: '#^Method Cdn77\\Api\\Core\\Infrastructure\\Billing\\UsdXeroBiller\:\:createBillingContact\(\) throws checked exception Cdn77\\Api\\Invoice\\Domain\\Exception\\XeroContactCouldNotBeCreated but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Core/Infrastructure/Billing/UsdXeroBiller.php

		-
			message: '#^Method Cdn77\\Api\\Core\\Infrastructure\\Billing\\UsdXeroBiller\:\:getAccount\(\) throws checked exception XeroPHP\\Exception but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Core/Infrastructure/Billing/UsdXeroBiller.php

		-
			message: '#^Method Cdn77\\Api\\Core\\Infrastructure\\Billing\\UsdXeroBiller\:\:getBillingContact\(\) throws checked exception XeroPHP\\Exception but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Core/Infrastructure/Billing/UsdXeroBiller.php

		-
			message: '#^Method Cdn77\\Api\\Core\\Infrastructure\\Billing\\UsdXeroBiller\:\:updateInvoice\(\) throws checked exception Cdn77\\Api\\Core\\Domain\\Exception\\CannotUpdatePaidInvoice but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Core/Infrastructure/Billing/UsdXeroBiller.php

		-
			message: '#^Method Cdn77\\Api\\Core\\Infrastructure\\Ceph\\S3ClientApi\:\:getBucketPolicy\(\) throws checked exception Cdn77\\Api\\ObjectStorage\\Domain\\Exception\\NoPolicyActionsSet but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Core/Infrastructure/Ceph/S3ClientApi.php

		-
			message: '#^Method Cdn77\\Api\\Core\\Infrastructure\\Ceph\\S3ClientApi\:\:getBucketPolicy\(\) throws checked exception Psl\\Json\\Exception\\EncodeException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Core/Infrastructure/Ceph/S3ClientApi.php

		-
			message: '#^Method Cdn77\\Api\\Core\\Infrastructure\\Ceph\\S3ClientFactory\:\:create\(\) throws checked exception InvalidArgumentException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Core/Infrastructure/Ceph/S3ClientFactory.php

		-
			message: '#^Method Cdn77\\Api\\Core\\Infrastructure\\ClickHouse\\Provider\\ClickHouseLiveStreamingStatsProvider\:\:requestStats\(\) throws checked exception Psr\\Http\\Client\\ClientExceptionInterface but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Core/Infrastructure/ClickHouse/Provider/ClickHouseLiveStreamingStatsProvider.php

		-
			message: '#^Method Cdn77\\Api\\Core\\Infrastructure\\ClickHouse\\Provider\\ClickHouseLiveStreamingStatsProvider\:\:requestStats\(\) throws checked exception SimPod\\ClickHouseClient\\Exception\\ServerError but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Core/Infrastructure/ClickHouse/Provider/ClickHouseLiveStreamingStatsProvider.php

		-
			message: '#^Method Cdn77\\Api\\Core\\Infrastructure\\ClickHouse\\Provider\\ClickHouseLiveStreamingStatsProvider\:\:requestSum\(\) throws checked exception Psr\\Http\\Client\\ClientExceptionInterface but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Core/Infrastructure/ClickHouse/Provider/ClickHouseLiveStreamingStatsProvider.php

		-
			message: '#^Method Cdn77\\Api\\Core\\Infrastructure\\ClickHouse\\Provider\\ClickHouseLiveStreamingStatsProvider\:\:requestSum\(\) throws checked exception SimPod\\ClickHouseClient\\Exception\\ServerError but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Core/Infrastructure/ClickHouse/Provider/ClickHouseLiveStreamingStatsProvider.php

		-
			message: '#^Method Cdn77\\Api\\Core\\Infrastructure\\ClickHouse\\Provider\\ClickHouseStatsProvider\:\:requestStats\(\) throws checked exception Psr\\Http\\Client\\ClientExceptionInterface but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Core/Infrastructure/ClickHouse/Provider/ClickHouseStatsProvider.php

		-
			message: '#^Method Cdn77\\Api\\Core\\Infrastructure\\ClickHouse\\Provider\\ClickHouseStatsProvider\:\:requestStats\(\) throws checked exception SimPod\\ClickHouseClient\\Exception\\ServerError but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Core/Infrastructure/ClickHouse/Provider/ClickHouseStatsProvider.php

		-
			message: '#^Method Cdn77\\Api\\Core\\Infrastructure\\ClickHouse\\Provider\\ClickHouseStatsProvider\:\:requestSum\(\) throws checked exception Brick\\Math\\Exception\\DivisionByZeroException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Core/Infrastructure/ClickHouse/Provider/ClickHouseStatsProvider.php

		-
			message: '#^Method Cdn77\\Api\\Core\\Infrastructure\\ClickHouse\\Provider\\ClickHouseStatsProvider\:\:requestSum\(\) throws checked exception Brick\\Math\\Exception\\NumberFormatException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Core/Infrastructure/ClickHouse/Provider/ClickHouseStatsProvider.php

		-
			message: '#^Method Cdn77\\Api\\Core\\Infrastructure\\ClickHouse\\Provider\\ClickHouseStatsProvider\:\:requestSum\(\) throws checked exception Brick\\Math\\Exception\\RoundingNecessaryException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Core/Infrastructure/ClickHouse/Provider/ClickHouseStatsProvider.php

		-
			message: '#^Method Cdn77\\Api\\Core\\Infrastructure\\ClickHouse\\Provider\\ClickHouseStatsProvider\:\:requestSum\(\) throws checked exception Psr\\Http\\Client\\ClientExceptionInterface but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Core/Infrastructure/ClickHouse/Provider/ClickHouseStatsProvider.php

		-
			message: '#^Method Cdn77\\Api\\Core\\Infrastructure\\ClickHouse\\Provider\\ClickHouseStatsProvider\:\:requestSum\(\) throws checked exception SimPod\\ClickHouseClient\\Exception\\ServerError but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Core/Infrastructure/ClickHouse/Provider/ClickHouseStatsProvider.php

		-
			message: '#^Method Cdn77\\Api\\Core\\Infrastructure\\ClickHouse\\QueryLogger\:\:stopQuery\(\) throws checked exception Safe\\Exceptions\\FilesystemException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Core/Infrastructure/ClickHouse/QueryLogger.php

		-
			message: '#^Method Cdn77\\Api\\Core\\Infrastructure\\Console\\NoLockAcquirer\:\:execute\(\) throws checked exception Cdn77\\Api\\Core\\Application\\Console\\Exception\\CommandAlreadyRunning but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Core/Infrastructure/Console/NoLockAcquirer.php

		-
			message: '#^Method Cdn77\\Api\\Core\\Infrastructure\\Console\\NoLockAcquirer\:\:execute\(\) throws checked exception Symfony\\Component\\Lock\\Exception\\LockAcquiringException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Core/Infrastructure/Console/NoLockAcquirer.php

		-
			message: '#^Method Cdn77\\Api\\Core\\Infrastructure\\Console\\NoLockAcquirer\:\:execute\(\) throws checked exception Symfony\\Component\\Lock\\Exception\\LockConflictedException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Core/Infrastructure/Console/NoLockAcquirer.php

		-
			message: '#^Method Cdn77\\Api\\Core\\Infrastructure\\Console\\SemaphoreLockAcquirer\:\:execute\(\) throws checked exception Cdn77\\Api\\Core\\Application\\Console\\Exception\\CommandAlreadyRunning but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Core/Infrastructure/Console/SemaphoreLockAcquirer.php

		-
			message: '#^Method Cdn77\\Api\\Core\\Infrastructure\\Console\\SemaphoreLockAcquirer\:\:execute\(\) throws checked exception Symfony\\Component\\Lock\\Exception\\LockAcquiringException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Core/Infrastructure/Console/SemaphoreLockAcquirer.php

		-
			message: '#^Method Cdn77\\Api\\Core\\Infrastructure\\Console\\SemaphoreLockAcquirer\:\:execute\(\) throws checked exception Symfony\\Component\\Lock\\Exception\\LockConflictedException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Core/Infrastructure/Console/SemaphoreLockAcquirer.php

		-
			message: '#^Method Cdn77\\Api\\Core\\Infrastructure\\Finder\\Cname\\DbalCnameCountFinder\:\:find\(\) throws checked exception Doctrine\\DBAL\\Exception but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Core/Infrastructure/Finder/Cname/DbalCnameCountFinder.php

		-
			message: '#^Method Cdn77\\Api\\Core\\Infrastructure\\Finder\\Cname\\DbalCnameCountFinder\:\:findForCustomer\(\) throws checked exception Doctrine\\DBAL\\Exception but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Core/Infrastructure/Finder/Cname/DbalCnameCountFinder.php

		-
			message: '#^Method Cdn77\\Api\\Core\\Infrastructure\\NxgApi\\HttpNxgApiProxy\:\:sendRequest\(\) throws checked exception Cdn77\\Api\\Core\\Domain\\NxgApi\\Exception\\NxgApiRequestFailed but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Core/Infrastructure/NxgApi/HttpNxgApiProxy.php

		-
			message: '#^Method Cdn77\\Api\\Core\\Infrastructure\\NxgApi\\HttpNxgApiProxy\:\:sendRequest\(\) throws checked exception InvalidArgumentException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 2
			path: src/Core/Infrastructure/NxgApi/HttpNxgApiProxy.php

		-
			message: '#^Method Cdn77\\Api\\Core\\Infrastructure\\NxgApi\\HttpNxgApiProxy\:\:sendRequest\(\) throws checked exception Psr\\Http\\Client\\ClientExceptionInterface but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Core/Infrastructure/NxgApi/HttpNxgApiProxy.php

		-
			message: '#^Method Cdn77\\Api\\Core\\Infrastructure\\Postmark\\DatabaseCustomerMailLimiter\:\:isInLimitForTemplate\(\) throws checked exception Doctrine\\DBAL\\Exception but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Core/Infrastructure/Postmark/DatabaseCustomerMailLimiter.php

		-
			message: '#^Method Cdn77\\Api\\Core\\Infrastructure\\Postmark\\SdkPostmark\:\:sendCardExpiringNotification\(\) throws checked exception Cdn77\\Api\\Core\\Domain\\Postmark\\Exception\\MailCouldNotBeSent but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Core/Infrastructure/Postmark/SdkPostmark.php

		-
			message: '#^Method Cdn77\\Api\\Core\\Infrastructure\\Postmark\\SdkPostmark\:\:sendCustomerSignUpWelcome\(\) throws checked exception Cdn77\\Api\\Core\\Domain\\Postmark\\Exception\\MailCouldNotBeSent but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Core/Infrastructure/Postmark/SdkPostmark.php

		-
			message: '#^Method Cdn77\\Api\\Core\\Infrastructure\\Postmark\\SdkPostmark\:\:sendEmailAddressChangeConfirmation\(\) throws checked exception Cdn77\\Api\\Core\\Domain\\Postmark\\Exception\\MailCouldNotBeSent but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Core/Infrastructure/Postmark/SdkPostmark.php

		-
			message: '#^Method Cdn77\\Api\\Core\\Infrastructure\\Postmark\\SdkPostmark\:\:sendEmailAddressChangeNotification\(\) throws checked exception Cdn77\\Api\\Core\\Domain\\Postmark\\Exception\\MailCouldNotBeSent but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Core/Infrastructure/Postmark/SdkPostmark.php

		-
			message: '#^Method Cdn77\\Api\\Core\\Infrastructure\\Postmark\\SdkPostmark\:\:sendEmailAddressConfirmation\(\) throws checked exception Cdn77\\Api\\Core\\Domain\\Postmark\\Exception\\MailCouldNotBeSent but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Core/Infrastructure/Postmark/SdkPostmark.php

		-
			message: '#^Method Cdn77\\Api\\Core\\Infrastructure\\Postmark\\SdkPostmark\:\:sendInvoice\(\) throws checked exception Cdn77\\Api\\Core\\Domain\\Postmark\\Exception\\MailCouldNotBeSent but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Core/Infrastructure/Postmark/SdkPostmark.php

		-
			message: '#^Method Cdn77\\Api\\Core\\Infrastructure\\Postmark\\SdkPostmark\:\:sendResetPassword\(\) throws checked exception Cdn77\\Api\\Core\\Domain\\Postmark\\Exception\\MailCouldNotBeSent but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Core/Infrastructure/Postmark/SdkPostmark.php

		-
			message: '#^Method Cdn77\\Api\\Core\\Infrastructure\\Postmark\\SdkPostmark\:\:sendTeamMemberSignUpInvite\(\) throws checked exception Cdn77\\Api\\Core\\Domain\\Postmark\\Exception\\MailCouldNotBeSent but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Core/Infrastructure/Postmark/SdkPostmark.php

		-
			message: '#^Method Cdn77\\Api\\Core\\Infrastructure\\Postmark\\SdkPostmark\:\:sendWelcomeToCdn77ObjectStorage\(\) throws checked exception Cdn77\\Api\\Core\\Domain\\Postmark\\Exception\\MailCouldNotBeSent but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Core/Infrastructure/Postmark/SdkPostmark.php

		-
			message: '#^Method Cdn77\\Api\\Core\\Infrastructure\\Pruner\\DbalTablePruner\:\:prune\(\) throws checked exception Brick\\Math\\Exception\\DivisionByZeroException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Core/Infrastructure/Pruner/DbalTablePruner.php

		-
			message: '#^Method Cdn77\\Api\\Core\\Infrastructure\\Pruner\\DbalTablePruner\:\:prune\(\) throws checked exception Brick\\Math\\Exception\\NumberFormatException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Core/Infrastructure/Pruner/DbalTablePruner.php

		-
			message: '#^Method Cdn77\\Api\\Core\\Infrastructure\\Pruner\\DbalTablePruner\:\:prune\(\) throws checked exception Brick\\Math\\Exception\\RoundingNecessaryException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Core/Infrastructure/Pruner/DbalTablePruner.php

		-
			message: '#^Method Cdn77\\Api\\Core\\Infrastructure\\PushZone\\HttpPushZoneApi\:\:updateServer\(\) throws checked exception Cdn77\\Api\\Storage\\Domain\\Exception\\ServerUpdateFailed but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Core/Infrastructure/PushZone/HttpPushZoneApi.php

		-
			message: '#^Method Cdn77\\Api\\Core\\Infrastructure\\Repository\\Cdn\\DoctrineCdnRepository\:\:find\(\) throws checked exception Doctrine\\ORM\\NonUniqueResultException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Core/Infrastructure/Repository/Cdn/DoctrineCdnRepository.php

		-
			message: '#^Method Cdn77\\Api\\Core\\Infrastructure\\Repository\\Cdn\\DoctrineCdnRepository\:\:get\(\) throws checked exception Cdn77\\Api\\Cdn\\Domain\\Exception\\CdnNotFound but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Core/Infrastructure/Repository/Cdn/DoctrineCdnRepository.php

		-
			message: '#^Method Cdn77\\Api\\Core\\Infrastructure\\Repository\\Customer\\DoctrineAccountSettingsRepository\:\:getForCustomer\(\) throws checked exception Cdn77\\Api\\Customer\\Domain\\Exception\\CustomerSettingsNotFound but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Core/Infrastructure/Repository/Customer/DoctrineAccountSettingsRepository.php

		-
			message: '#^Method Cdn77\\Api\\Core\\Infrastructure\\Repository\\Customer\\DoctrineCustomerCdnSettingsRepository\:\:getForCustomer\(\) throws checked exception Cdn77\\Api\\Cdn\\Domain\\Exception\\CdnSettingsNotFound but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Core/Infrastructure/Repository/Customer/DoctrineCustomerCdnSettingsRepository.php

		-
			message: '#^Method Cdn77\\Api\\Core\\Infrastructure\\Repository\\DoctrineCustomerRepository\:\:findForEmail\(\) throws checked exception Doctrine\\ORM\\NonUniqueResultException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Core/Infrastructure/Repository/DoctrineCustomerRepository.php

		-
			message: '#^Method Cdn77\\Api\\Core\\Infrastructure\\Repository\\DoctrineCustomerRepository\:\:findForId\(\) throws checked exception Doctrine\\ORM\\NonUniqueResultException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Core/Infrastructure/Repository/DoctrineCustomerRepository.php

		-
			message: '#^Method Cdn77\\Api\\Core\\Infrastructure\\Repository\\DoctrineCustomerRepository\:\:findForPublicId\(\) throws checked exception Doctrine\\ORM\\NonUniqueResultException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Core/Infrastructure/Repository/DoctrineCustomerRepository.php

		-
			message: '#^Method Cdn77\\Api\\Core\\Infrastructure\\Repository\\DoctrineExchangeRateRepository\:\:getLatest\(\) throws checked exception Doctrine\\ORM\\NonUniqueResultException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Core/Infrastructure/Repository/DoctrineExchangeRateRepository.php

		-
			message: '#^Method Cdn77\\Api\\Core\\Infrastructure\\Repository\\DoctrineOAuthTokenRepository\:\:getForService\(\) throws checked exception Cdn77\\Api\\Core\\Domain\\Exception\\OAuthTokenNotFound but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Core/Infrastructure/Repository/DoctrineOAuthTokenRepository.php

		-
			message: '#^Method Cdn77\\Api\\Core\\Infrastructure\\Repository\\DoctrineSslRepository\:\:getForType\(\) throws checked exception Doctrine\\ORM\\NonUniqueResultException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Core/Infrastructure/Repository/DoctrineSslRepository.php

		-
			message: '#^Method Cdn77\\Api\\Core\\Infrastructure\\Repository\\MonthlyTrafficPlan\\DoctrineMonthlyTrafficPlanRepository\:\:get\(\) throws checked exception Cdn77\\Api\\MonthlyTrafficPlan\\Domain\\Exception\\MonthlyTrafficPlanNotFound but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Core/Infrastructure/Repository/MonthlyTrafficPlan/DoctrineMonthlyTrafficPlanRepository.php

		-
			message: '#^Method Cdn77\\Api\\Core\\Infrastructure\\Repository\\Origin\\DoctrineOriginRepository\:\:findForCdn\(\) throws checked exception Doctrine\\ORM\\NonUniqueResultException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Core/Infrastructure/Repository/Origin/DoctrineOriginRepository.php

		-
			message: '#^Method Cdn77\\Api\\Core\\Infrastructure\\Repository\\Storage\\DoctrineStorageRepository\:\:findForCdn\(\) throws checked exception Doctrine\\ORM\\NonUniqueResultException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Core/Infrastructure/Repository/Storage/DoctrineStorageRepository.php

		-
			message: '#^Method Cdn77\\Api\\Core\\Infrastructure\\Repository\\Storage\\DoctrineStorageRepository\:\:get\(\) throws checked exception Cdn77\\Api\\Storage\\Domain\\Exception\\StorageNotFound but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Core/Infrastructure/Repository/Storage/DoctrineStorageRepository.php

		-
			message: '#^Method Cdn77\\Api\\Core\\Infrastructure\\Repository\\Tariff\\DoctrineTariffRepository\:\:findActiveForCustomer\(\) throws checked exception Doctrine\\ORM\\NonUniqueResultException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Core/Infrastructure/Repository/Tariff/DoctrineTariffRepository.php

		-
			message: '#^Method Cdn77\\Api\\Core\\Infrastructure\\Repository\\Tariff\\DoctrineTariffRepository\:\:getActiveForCustomer\(\) throws checked exception Cdn77\\Api\\Tariff\\Domain\\Exception\\NoActiveTariffFound but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Core/Infrastructure/Repository/Tariff/DoctrineTariffRepository.php

		-
			message: '#^Method Cdn77\\Api\\Core\\Infrastructure\\Slack\\HttpSlack\:\:sendMessage\(\) throws checked exception InvalidArgumentException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 3
			path: src/Core/Infrastructure/Slack/HttpSlack.php

		-
			message: '#^Method Cdn77\\Api\\Core\\Infrastructure\\Validation\\DatabaseCdnAccessValidator\:\:canCreate\(\) throws checked exception Cdn77\\Api\\Cdn\\Domain\\Exception\\CdnCouldNotBeCreated but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Core/Infrastructure/Validation/DatabaseCdnAccessValidator.php

		-
			message: '#^Method Cdn77\\Api\\Core\\Infrastructure\\Validation\\DatabaseCdnAccessValidator\:\:isAllAllowed\(\) throws checked exception Cdn77\\Api\\Core\\Domain\\Exception\\Customer\\CustomerNotFound but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Core/Infrastructure/Validation/DatabaseCdnAccessValidator.php

		-
			message: '#^Method Cdn77\\Api\\Core\\Infrastructure\\Validation\\DatabaseCdnAccessValidator\:\:isOwnerOrAdmin\(\) throws checked exception Cdn77\\Api\\Core\\Domain\\Exception\\Customer\\CustomerNotFound but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Core/Infrastructure/Validation/DatabaseCdnAccessValidator.php

		-
			message: '#^Method Cdn77\\Api\\Core\\Infrastructure\\Validation\\DbalTeamMemberAccessValidator\:\:hasAccessToRestrictedContext\(\) throws checked exception Doctrine\\DBAL\\Exception but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Core/Infrastructure/Validation/DbalTeamMemberAccessValidator.php

		-
			message: '#^Method Cdn77\\Api\\CoreLibrary\\GeoIp\\GeoIpDatabaseReader\:\:getCountryForIp\(\) throws checked exception MaxMind\\Db\\Reader\\InvalidDatabaseException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 2
			path: src/CoreLibrary/GeoIp/GeoIpDatabaseReader.php

		-
			message: '#^Method Cdn77\\Api\\CoreLibrary\\Money\\CurrencyCode\:\:toCurrency\(\) throws checked exception Brick\\Money\\Exception\\UnknownCurrencyException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/CoreLibrary/Money/CurrencyCode.php

		-
			message: '#^Method Cdn77\\Api\\CoreLibrary\\Money\\MoneyFactory\:\:ofDbValue\(\) throws checked exception Brick\\Math\\Exception\\NumberFormatException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/CoreLibrary/Money/MoneyFactory.php

		-
			message: '#^Method Cdn77\\Api\\CoreLibrary\\Money\\MoneyFactory\:\:ofDbValue\(\) throws checked exception Brick\\Math\\Exception\\RoundingNecessaryException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/CoreLibrary/Money/MoneyFactory.php

		-
			message: '#^Method Cdn77\\Api\\CoreLibrary\\Money\\MoneyFactory\:\:ofDbValue\(\) throws checked exception Brick\\Money\\Exception\\UnknownCurrencyException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/CoreLibrary/Money/MoneyFactory.php

		-
			message: '#^Method Cdn77\\Api\\CoreLibrary\\Reflection\\Reflection\:\:getProperty\(\) throws checked exception ReflectionException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/CoreLibrary/Reflection/Reflection.php

		-
			message: '#^Method Cdn77\\Api\\CoreLibrary\\Symfony\\KernelContainerConfigurator\:\:configure\(\) throws checked exception Symfony\\Component\\Finder\\Exception\\DirectoryNotFoundException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/CoreLibrary/Symfony/KernelContainerConfigurator.php

		-
			message: '#^Method Cdn77\\Api\\CoreLibrary\\Symfony\\KernelContainerConfigurator\:\:loadAllConfigsWithEnvironmentConfigs\(\) throws checked exception Exception but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/CoreLibrary/Symfony/KernelContainerConfigurator.php

		-
			message: '#^Method Cdn77\\Api\\CoreLibrary\\Symfony\\KernelContainerConfigurator\:\:loadAllConfigsWithoutEnvironmentConfigs\(\) throws checked exception Exception but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/CoreLibrary/Symfony/KernelContainerConfigurator.php

		-
			message: '#^Method Cdn77\\Api\\CoreLibrary\\Symfony\\KernelRoutesConfigurator\:\:configure\(\) throws checked exception LogicException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/CoreLibrary/Symfony/KernelRoutesConfigurator.php

		-
			message: '#^Method Cdn77\\Api\\CoreLibrary\\Symfony\\KernelRoutesConfigurator\:\:configure\(\) throws checked exception Symfony\\Component\\Finder\\Exception\\DirectoryNotFoundException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/CoreLibrary/Symfony/KernelRoutesConfigurator.php

		-
			message: '#^Method Cdn77\\Api\\Currency\\Application\\Console\\FetchExchangeRates\:\:execute\(\) throws checked exception Cdn77\\Api\\Currency\\Domain\\Exception\\FailedToFetchExchangeRate but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Currency/Application/Console/FetchExchangeRates.php

		-
			message: '#^Method Cdn77\\Api\\Currency\\Application\\Console\\FetchExchangeRates\:\:execute\(\) throws checked exception OutOfBoundsException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Currency/Application/Console/FetchExchangeRates.php

		-
			message: '#^Method Cdn77\\Api\\Currency\\Infrastructure\\Api\\HttpXECurrencyApi\:\:getExchangeRates\(\) throws checked exception Brick\\Math\\Exception\\DivisionByZeroException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Currency/Infrastructure/Api/HttpXECurrencyApi.php

		-
			message: '#^Method Cdn77\\Api\\Currency\\Infrastructure\\Api\\HttpXECurrencyApi\:\:getExchangeRates\(\) throws checked exception Brick\\Math\\Exception\\NumberFormatException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Currency/Infrastructure/Api/HttpXECurrencyApi.php

		-
			message: '#^Method Cdn77\\Api\\Currency\\Infrastructure\\Api\\HttpXECurrencyApi\:\:getExchangeRates\(\) throws checked exception Brick\\Math\\Exception\\RoundingNecessaryException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Currency/Infrastructure/Api/HttpXECurrencyApi.php

		-
			message: '#^Method Cdn77\\Api\\CustomPlan\\Application\\Payload\\InvoiceLinesSchema\:\:toDto\(\) throws checked exception Brick\\Math\\Exception\\MathException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/CustomPlan/Application/Payload/InvoiceLinesSchema.php

		-
			message: '#^Method Cdn77\\Api\\CustomPlan\\Application\\Payload\\InvoiceLinesSchema\:\:toDto\(\) throws checked exception Brick\\Money\\Exception\\MoneyException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/CustomPlan/Application/Payload/InvoiceLinesSchema.php

		-
			message: '#^Method Cdn77\\Api\\CustomPlan\\Domain\\Command\\DeleteContractHandler\:\:handle\(\) throws checked exception Cdn77\\Api\\CustomPlan\\Domain\\Exception\\CannotDeleteContractWithInvoice but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/CustomPlan/Domain/Command/DeleteContractHandler.php

		-
			message: '#^Method Cdn77\\Api\\CustomPlan\\Domain\\Command\\EditContractHandler\:\:handle\(\) throws checked exception Cdn77\\Api\\Core\\Domain\\Exception\\Customer\\CustomerNotFound but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/CustomPlan/Domain/Command/EditContractHandler.php

		-
			message: '#^Method Cdn77\\Api\\CustomPlan\\Domain\\Command\\EditCustomPlanHandler\:\:handle\(\) throws checked exception Cdn77\\Api\\Core\\Domain\\Exception\\Customer\\CustomerNotFound but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/CustomPlan/Domain/Command/EditCustomPlanHandler.php

		-
			message: '#^Method Cdn77\\Api\\CustomPlan\\Domain\\Dto\\InvoiceLine\:\:fromArray\(\) throws checked exception Brick\\Math\\Exception\\DivisionByZeroException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/CustomPlan/Domain/Dto/InvoiceLine.php

		-
			message: '#^Method Cdn77\\Api\\CustomPlan\\Domain\\Dto\\InvoiceLine\:\:fromArray\(\) throws checked exception Brick\\Math\\Exception\\NumberFormatException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 2
			path: src/CustomPlan/Domain/Dto/InvoiceLine.php

		-
			message: '#^Method Cdn77\\Api\\CustomPlan\\Domain\\Dto\\InvoiceLine\:\:fromArray\(\) throws checked exception Brick\\Math\\Exception\\RoundingNecessaryException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 2
			path: src/CustomPlan/Domain/Dto/InvoiceLine.php

		-
			message: '#^Method Cdn77\\Api\\CustomPlan\\Domain\\Dto\\InvoiceLine\:\:fromArray\(\) throws checked exception Brick\\Money\\Exception\\UnknownCurrencyException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/CustomPlan/Domain/Dto/InvoiceLine.php

		-
			message: '#^Method Cdn77\\Api\\CustomPlan\\Domain\\Dto\\PlanVolume\:\:toString\(\) throws checked exception Brick\\Math\\Exception\\MathException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/CustomPlan/Domain/Dto/PlanVolume.php

		-
			message: '#^Method Cdn77\\Api\\CustomPlan\\Domain\\EstimatedTrafficCalculator\:\:calculate\(\) throws checked exception Brick\\Math\\Exception\\DivisionByZeroException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 2
			path: src/CustomPlan/Domain/EstimatedTrafficCalculator.php

		-
			message: '#^Method Cdn77\\Api\\CustomPlan\\Domain\\EstimatedTrafficCalculator\:\:calculate\(\) throws checked exception Brick\\Math\\Exception\\MathException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 2
			path: src/CustomPlan/Domain/EstimatedTrafficCalculator.php

		-
			message: '#^Method Cdn77\\Api\\CustomPlan\\Domain\\EstimatedTrafficCalculator\:\:calculate\(\) throws checked exception Brick\\Math\\Exception\\NumberFormatException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 2
			path: src/CustomPlan/Domain/EstimatedTrafficCalculator.php

		-
			message: '#^Method Cdn77\\Api\\CustomPlan\\Domain\\EstimatedTrafficCalculator\:\:calculate\(\) throws checked exception Brick\\Math\\Exception\\RoundingNecessaryException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 2
			path: src/CustomPlan/Domain/EstimatedTrafficCalculator.php

		-
			message: '#^Method Cdn77\\Api\\CustomPlan\\Domain\\EstimatedTrafficCalculator\:\:calculate\(\) throws checked exception InvalidArgumentException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/CustomPlan/Domain/EstimatedTrafficCalculator.php

		-
			message: '#^Method Cdn77\\Api\\CustomPlan\\Domain\\Query\\GetContractPaymentDetailHandler\:\:handle\(\) throws checked exception Cdn77\\Api\\Core\\Domain\\Exception\\Customer\\CustomerNotFound but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/CustomPlan/Domain/Query/GetContractPaymentDetailHandler.php

		-
			message: '#^Method Cdn77\\Api\\CustomPlan\\Domain\\Query\\GetContractPaymentDetailHandler\:\:handle\(\) throws checked exception Cdn77\\Api\\CustomPlan\\Domain\\Exception\\ContractNotFound but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/CustomPlan/Domain/Query/GetContractPaymentDetailHandler.php

		-
			message: '#^Method Cdn77\\Api\\CustomPlan\\Domain\\Query\\GetCustomPlansOverviewHandler\:\:handle\(\) throws checked exception Cdn77\\Api\\Core\\Domain\\Exception\\Customer\\CustomerNotFound but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/CustomPlan/Domain/Query/GetCustomPlansOverviewHandler.php

		-
			message: '#^Method Cdn77\\Api\\CustomPlan\\Domain\\Query\\GetCustomerCustomPlanHistoryHandler\:\:handle\(\) throws checked exception Cdn77\\Api\\Core\\Domain\\Exception\\Customer\\CustomerNotFound but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/CustomPlan/Domain/Query/GetCustomerCustomPlanHistoryHandler.php

		-
			message: '#^Method Cdn77\\Api\\CustomPlan\\Domain\\Validation\\InvoiceConfigurationValidator\:\:validate\(\) throws checked exception Brick\\Math\\Exception\\MathException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 3
			path: src/CustomPlan/Domain/Validation/InvoiceConfigurationValidator.php

		-
			message: '#^Method Cdn77\\Api\\CustomPlan\\Domain\\Validation\\InvoiceConfigurationValidator\:\:validate\(\) throws checked exception Brick\\Money\\Exception\\MoneyMismatchException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 2
			path: src/CustomPlan/Domain/Validation/InvoiceConfigurationValidator.php

		-
			message: '#^Method Cdn77\\Api\\CustomPlan\\Domain\\Validation\\InvoiceConfigurationValidator\:\:validate\(\) throws checked exception Cdn77\\Api\\CustomPlan\\Domain\\Exception\\CannotAddContract but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 2
			path: src/CustomPlan/Domain/Validation/InvoiceConfigurationValidator.php

		-
			message: '#^Method Cdn77\\Api\\CustomPlan\\Infrastructure\\Repository\\DoctrineContractRepository\:\:findLastForCustomPlan\(\) throws checked exception Doctrine\\ORM\\NonUniqueResultException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/CustomPlan/Infrastructure/Repository/DoctrineContractRepository.php

		-
			message: '#^Method Cdn77\\Api\\CustomPlan\\Infrastructure\\Repository\\DoctrineContractRepository\:\:get\(\) throws checked exception Cdn77\\Api\\CustomPlan\\Domain\\Exception\\ContractNotFound but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/CustomPlan/Infrastructure/Repository/DoctrineContractRepository.php

		-
			message: '#^Method Cdn77\\Api\\CustomPlan\\Infrastructure\\Repository\\DoctrineCustomPlanRepository\:\:findLatestActiveForCustomer\(\) throws checked exception Doctrine\\ORM\\NonUniqueResultException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/CustomPlan/Infrastructure/Repository/DoctrineCustomPlanRepository.php

		-
			message: '#^Method Cdn77\\Api\\CustomPlan\\Infrastructure\\Repository\\DoctrineCustomPlanRepository\:\:findLatestForCustomerAndPlanPeriod\(\) throws checked exception Doctrine\\ORM\\NonUniqueResultException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/CustomPlan/Infrastructure/Repository/DoctrineCustomPlanRepository.php

		-
			message: '#^Method Cdn77\\Api\\CustomPlan\\Infrastructure\\Repository\\DoctrineCustomPlanRepository\:\:get\(\) throws checked exception Cdn77\\Api\\CustomPlan\\Domain\\Exception\\CustomPlanNotFound but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/CustomPlan/Infrastructure/Repository/DoctrineCustomPlanRepository.php

		-
			message: '#^Method Cdn77\\Api\\CustomPlan\\Infrastructure\\Repository\\DoctrineCustomPlanRepository\:\:getActiveForCustomer\(\) throws checked exception Cdn77\\Api\\CustomPlan\\Domain\\Exception\\CustomPlanNotFound but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/CustomPlan/Infrastructure/Repository/DoctrineCustomPlanRepository.php

		-
			message: '#^Method Cdn77\\Api\\Customer\\Application\\Console\\ProcessScheduledSuspension\:\:execute\(\) throws checked exception Cdn77\\Api\\Core\\Domain\\Exception\\Customer\\CustomerNotFound but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Customer/Application/Console/ProcessScheduledSuspension.php

		-
			message: '#^Method Cdn77\\Api\\Customer\\Application\\Console\\ProcessScheduledSuspension\:\:execute\(\) throws checked exception Cdn77\\Api\\MonthlyTrafficPlan\\Domain\\Exception\\CustomerMonthlyTrafficPlanNotFound but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Customer/Application/Console/ProcessScheduledSuspension.php

		-
			message: '#^Method Cdn77\\Api\\Customer\\Application\\Console\\SendFirstCustomerTrafficNotificationsCommand\:\:execute\(\) throws checked exception Cdn77\\Api\\Customer\\Domain\\Exception\\AccountFlagsNotFound but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Customer/Application/Console/SendFirstCustomerTrafficNotificationsCommand.php

		-
			message: '#^Method Cdn77\\Api\\Customer\\Application\\Payload\\TrialSchema\:\:__construct\(\) throws checked exception Brick\\Math\\Exception\\MathException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Customer/Application/Payload/TrialSchema.php

		-
			message: '#^Method Cdn77\\Api\\Customer\\Domain\\ApiPasswordEncryptor\:\:decrypt\(\) throws checked exception Safe\\Exceptions\\OpensslException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Customer/Domain/ApiPasswordEncryptor.php

		-
			message: '#^Method Cdn77\\Api\\Customer\\Domain\\ApiPasswordEncryptor\:\:decrypt\(\) throws checked exception Safe\\Exceptions\\UrlException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 2
			path: src/Customer/Domain/ApiPasswordEncryptor.php

		-
			message: '#^Method Cdn77\\Api\\Customer\\Domain\\ApiPasswordEncryptor\:\:encrypt\(\) throws checked exception Safe\\Exceptions\\OpensslException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 2
			path: src/Customer/Domain/ApiPasswordEncryptor.php

		-
			message: '#^Method Cdn77\\Api\\Customer\\Domain\\Command\\ConfirmEmailAddressHandler\:\:handle\(\) throws checked exception Cdn77\\Api\\Core\\Domain\\Exception\\Customer\\CustomerNotFound but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Customer/Domain/Command/ConfirmEmailAddressHandler.php

		-
			message: '#^Method Cdn77\\Api\\Customer\\Domain\\Command\\ConfirmEmailAddressHandler\:\:handle\(\) throws checked exception Cdn77\\Api\\Customer\\Domain\\Exception\\EmailAddressConfirmationFailed but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Customer/Domain/Command/ConfirmEmailAddressHandler.php

		-
			message: '#^Method Cdn77\\Api\\Customer\\Domain\\Command\\ConfirmEmailAddressHandler\:\:handle\(\) throws checked exception Cdn77\\Api\\Customer\\Domain\\Exception\\EmailAddressIsAlreadyConfirmed but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Customer/Domain/Command/ConfirmEmailAddressHandler.php

		-
			message: '#^Method Cdn77\\Api\\Customer\\Domain\\Command\\ResendVerificationEmailHandler\:\:handle\(\) throws checked exception Cdn77\\Api\\Core\\Domain\\Exception\\Customer\\CustomerNotFound but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Customer/Domain/Command/ResendVerificationEmailHandler.php

		-
			message: '#^Method Cdn77\\Api\\Customer\\Domain\\Command\\ResendVerificationEmailHandler\:\:handle\(\) throws checked exception Cdn77\\Api\\Customer\\Domain\\Exception\\EmailAddressIsAlreadyConfirmed but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Customer/Domain/Command/ResendVerificationEmailHandler.php

		-
			message: '#^Method Cdn77\\Api\\Customer\\Domain\\Command\\ResetPasswordHandler\:\:handle\(\) throws checked exception Cdn77\\Api\\Customer\\Domain\\Exception\\InvalidPasswordResetRequest but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 3
			path: src/Customer/Domain/Command/ResetPasswordHandler.php

		-
			message: '#^Method Cdn77\\Api\\Customer\\Domain\\Command\\SuspendCustomerHandler\:\:handle\(\) throws checked exception Cdn77\\Api\\MonthlyTrafficPlan\\Domain\\Exception\\CustomerMonthlyTrafficPlanNotFound but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Customer/Domain/Command/SuspendCustomerHandler.php

		-
			message: '#^Method Cdn77\\Api\\Customer\\Domain\\Command\\UnsuspendCustomerHandler\:\:handle\(\) throws checked exception Cdn77\\Api\\Core\\Domain\\Exception\\Customer\\CustomerNotFound but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Customer/Domain/Command/UnsuspendCustomerHandler.php

		-
			message: '#^Method Cdn77\\Api\\Customer\\Domain\\Command\\UnsuspendCustomerHandler\:\:handle\(\) throws checked exception Cdn77\\Api\\Customer\\Domain\\Exception\\CustomerUnsuspensionFailed but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Customer/Domain/Command/UnsuspendCustomerHandler.php

		-
			message: '#^Method Cdn77\\Api\\Customer\\Domain\\Command\\UpdateCustomerProfileInfoHandler\:\:handle\(\) throws checked exception Cdn77\\Api\\Core\\Domain\\Exception\\Customer\\CustomerNotFound but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Customer/Domain/Command/UpdateCustomerProfileInfoHandler.php

		-
			message: '#^Method Cdn77\\Api\\Customer\\Domain\\Command\\UpdateCustomerProfileInfoHandler\:\:handle\(\) throws checked exception Cdn77\\Api\\Customer\\Domain\\Exception\\CustomerProfileUpdateFailed but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Customer/Domain/Command/UpdateCustomerProfileInfoHandler.php

		-
			message: '#^Method Cdn77\\Api\\Customer\\Domain\\Command\\VerifyCustomerPasswordHandler\:\:handle\(\) throws checked exception Cdn77\\Api\\Authentication\\Domain\\Exception\\InvalidCredentials but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Customer/Domain/Command/VerifyCustomerPasswordHandler.php

		-
			message: '#^Method Cdn77\\Api\\Customer\\Domain\\Command\\VerifyCustomerPasswordHandler\:\:handle\(\) throws checked exception Cdn77\\Api\\Core\\Domain\\Exception\\Customer\\CustomerNotFound but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Customer/Domain/Command/VerifyCustomerPasswordHandler.php

		-
			message: '#^Method Cdn77\\Api\\Customer\\Domain\\EmailAddressConfirmationLinkSender\:\:resend\(\) throws checked exception Cdn77\\Api\\Customer\\Domain\\Exception\\SentMailRateLimitExceeded but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Customer/Domain/EmailAddressConfirmationLinkSender.php

		-
			message: '#^Method Cdn77\\Api\\Customer\\Domain\\Limit\\InviteLimiter\:\:checkForCustomerAndEmail\(\) throws checked exception Cdn77\\Api\\Customer\\Domain\\Exception\\InviteCountLimitExceeded but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 2
			path: src/Customer/Domain/Limit/InviteLimiter.php

		-
			message: '#^Method Cdn77\\Api\\Customer\\Domain\\Limit\\PasswordChangeRequestRateLimiter\:\:checkForCustomer\(\) throws checked exception Cdn77\\Api\\Customer\\Domain\\Exception\\ChangePasswordRateLimitExceeded but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Customer/Domain/Limit/PasswordChangeRequestRateLimiter.php

		-
			message: '#^Method Cdn77\\Api\\Customer\\Domain\\Limit\\PasswordVerifyRequestRateLimiter\:\:checkForCustomer\(\) throws checked exception Cdn77\\Api\\Customer\\Domain\\Exception\\VerifyPasswordRateLimitExceeded but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Customer/Domain/Limit/PasswordVerifyRequestRateLimiter.php

		-
			message: '#^Method Cdn77\\Api\\Customer\\Domain\\Query\\GetCreditHistoryForCustomerHandler\:\:handle\(\) throws checked exception Cdn77\\Api\\Core\\Domain\\Exception\\Customer\\CustomerNotFound but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Customer/Domain/Query/GetCreditHistoryForCustomerHandler.php

		-
			message: '#^Method Cdn77\\Api\\Customer\\Domain\\Query\\GetCreditOperationsHistoryHandler\:\:handle\(\) throws checked exception Cdn77\\Api\\Core\\Domain\\Exception\\Customer\\CustomerNotFound but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Customer/Domain/Query/GetCreditOperationsHistoryHandler.php

		-
			message: '#^Method Cdn77\\Api\\Customer\\Domain\\Query\\GetCustomerDetailByIdHandler\:\:handle\(\) throws checked exception Cdn77\\Api\\Core\\Domain\\Exception\\Customer\\CustomerNotFound but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Customer/Domain/Query/GetCustomerDetailByIdHandler.php

		-
			message: '#^Method Cdn77\\Api\\Customer\\Domain\\Query\\GetCustomerStreamingPanelLoginDataInfoHandler\:\:handle\(\) throws checked exception Cdn77\\Api\\Core\\Domain\\Exception\\Customer\\CustomerNotFound but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Customer/Domain/Query/GetCustomerStreamingPanelLoginDataInfoHandler.php

		-
			message: '#^Method Cdn77\\Api\\Customer\\Domain\\Query\\GetCustomerStreamingPanelLoginDataInfoHandler\:\:handle\(\) throws checked exception Cdn77\\Api\\Customer\\Domain\\Exception\\CannotAccessStreamingPanel but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Customer/Domain/Query/GetCustomerStreamingPanelLoginDataInfoHandler.php

		-
			message: '#^Method Cdn77\\Api\\Customer\\Infrastructure\\Factory\\DbalSequenceCustomerIdFactory\:\:get\(\) throws checked exception Doctrine\\DBAL\\Exception but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Customer/Infrastructure/Factory/DbalSequenceCustomerIdFactory.php

		-
			message: '#^Method Cdn77\\Api\\Customer\\Infrastructure\\Finder\\DbalCustomerIdFinder\:\:findForEmail\(\) throws checked exception Doctrine\\DBAL\\Exception but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Customer/Infrastructure/Finder/DbalCustomerIdFinder.php

		-
			message: '#^Method Cdn77\\Api\\Customer\\Infrastructure\\Finder\\DbalRequestCountFinder\:\:findForCustomerAndEndpoint\(\) throws checked exception Doctrine\\DBAL\\Exception but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Customer/Infrastructure/Finder/DbalRequestCountFinder.php

		-
			message: '#^Method Cdn77\\Api\\Customer\\Infrastructure\\Finder\\DbalRequestCountFinder\:\:findForEndpointAndCustomerInRequestBody\(\) throws checked exception Doctrine\\DBAL\\Exception but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Customer/Infrastructure/Finder/DbalRequestCountFinder.php

		-
			message: '#^Method Cdn77\\Api\\Customer\\Infrastructure\\Finder\\DbalSignUpInviteCountFinder\:\:findForCustomer\(\) throws checked exception Doctrine\\DBAL\\Exception but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Customer/Infrastructure/Finder/DbalSignUpInviteCountFinder.php

		-
			message: '#^Method Cdn77\\Api\\Customer\\Infrastructure\\Finder\\DbalSignUpInviteCountFinder\:\:findForCustomerAndEmail\(\) throws checked exception Doctrine\\DBAL\\Exception but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Customer/Infrastructure/Finder/DbalSignUpInviteCountFinder.php

		-
			message: '#^Method Cdn77\\Api\\Customer\\Infrastructure\\Limit\\DbalResetPasswordRequestLimiter\:\:checkForEmail\(\) throws checked exception Doctrine\\DBAL\\Exception but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Customer/Infrastructure/Limit/DbalResetPasswordRequestLimiter.php

		-
			message: '#^Method Cdn77\\Api\\Customer\\Infrastructure\\Repository\\DoctrineAccountSignUpInviteRepository\:\:find\(\) throws checked exception Doctrine\\ORM\\NonUniqueResultException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Customer/Infrastructure/Repository/DoctrineAccountSignUpInviteRepository.php

		-
			message: '#^Method Cdn77\\Api\\Customer\\Infrastructure\\Repository\\DoctrineAccountSignUpInviteRepository\:\:get\(\) throws checked exception Cdn77\\Api\\Customer\\Domain\\Exception\\AccountSignUpInviteNotFound but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Customer/Infrastructure/Repository/DoctrineAccountSignUpInviteRepository.php

		-
			message: '#^Method Cdn77\\Api\\Customer\\Infrastructure\\Repository\\DoctrineCustomerMailingUnsubscribeRepository\:\:findForCustomerAndType\(\) throws checked exception Doctrine\\ORM\\NonUniqueResultException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Customer/Infrastructure/Repository/DoctrineCustomerMailingUnsubscribeRepository.php

		-
			message: '#^Method Cdn77\\Api\\Customer\\Infrastructure\\Repository\\DoctrineCustomerTrafficRepository\:\:findForCustomer\(\) throws checked exception Doctrine\\ORM\\NonUniqueResultException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Customer/Infrastructure/Repository/DoctrineCustomerTrafficRepository.php

		-
			message: '#^Method Cdn77\\Api\\Customer\\Infrastructure\\Repository\\DoctrineCustomerTrafficRepository\:\:getForCustomer\(\) throws checked exception Cdn77\\Api\\Customer\\Domain\\Exception\\CustomerTrafficNotFound but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Customer/Infrastructure/Repository/DoctrineCustomerTrafficRepository.php

		-
			message: '#^Method Cdn77\\Api\\Customer\\Infrastructure\\Repository\\DoctrineEmailAddressConfirmationRepository\:\:findUnconfirmedForCustomer\(\) throws checked exception Doctrine\\ORM\\NonUniqueResultException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Customer/Infrastructure/Repository/DoctrineEmailAddressConfirmationRepository.php

		-
			message: '#^Method Cdn77\\Api\\Customer\\Infrastructure\\Repository\\DoctrineEmailAddressConfirmationRepository\:\:get\(\) throws checked exception Cdn77\\Api\\Customer\\Domain\\Exception\\EmailAddressConfirmationNotFound but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Customer/Infrastructure/Repository/DoctrineEmailAddressConfirmationRepository.php

		-
			message: '#^Method Cdn77\\Api\\Customer\\Infrastructure\\Repository\\DoctrineEmailAddressConfirmationRepository\:\:getUnconfirmedForCustomer\(\) throws checked exception Cdn77\\Api\\Customer\\Domain\\Exception\\EmailAddressConfirmationNotFound but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Customer/Infrastructure/Repository/DoctrineEmailAddressConfirmationRepository.php

		-
			message: '#^Method Cdn77\\Api\\Customer\\Infrastructure\\Repository\\DoctrineResetPasswordRequestRepository\:\:findForHash\(\) throws checked exception Doctrine\\ORM\\NonUniqueResultException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Customer/Infrastructure/Repository/DoctrineResetPasswordRequestRepository.php

		-
			message: '#^Method Cdn77\\Api\\Datacenter\\Domain\\Dto\\DatacenterRegions\:\:fromDatacenterDetails\(\) throws checked exception OutOfBoundsException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Datacenter/Domain/Dto/DatacenterRegions.php

		-
			message: '#^Method Cdn77\\Api\\Datacenter\\Domain\\Dto\\Location\:\:fromPayload\(\) throws checked exception Brick\\Math\\Exception\\DivisionByZeroException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 2
			path: src/Datacenter/Domain/Dto/Location.php

		-
			message: '#^Method Cdn77\\Api\\Datacenter\\Domain\\Dto\\Location\:\:fromPayload\(\) throws checked exception Brick\\Math\\Exception\\NumberFormatException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 2
			path: src/Datacenter/Domain/Dto/Location.php

		-
			message: '#^Method Cdn77\\Api\\Datacenter\\Domain\\Dto\\Location\:\:fromPayload\(\) throws checked exception Brick\\Math\\Exception\\RoundingNecessaryException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 2
			path: src/Datacenter/Domain/Dto/Location.php

		-
			message: '#^Method Cdn77\\Api\\Datacenter\\Infrastructure\\Finder\\DbalDatacenterDetailFinder\:\:findAllActiveForCustomer\(\) throws checked exception Brick\\Math\\Exception\\DivisionByZeroException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 2
			path: src/Datacenter/Infrastructure/Finder/DbalDatacenterDetailFinder.php

		-
			message: '#^Method Cdn77\\Api\\Datacenter\\Infrastructure\\Finder\\DbalDatacenterDetailFinder\:\:findAllActiveForCustomer\(\) throws checked exception Brick\\Math\\Exception\\NumberFormatException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 2
			path: src/Datacenter/Infrastructure/Finder/DbalDatacenterDetailFinder.php

		-
			message: '#^Method Cdn77\\Api\\Datacenter\\Infrastructure\\Finder\\DbalDatacenterDetailFinder\:\:findAllActiveForCustomer\(\) throws checked exception Brick\\Math\\Exception\\RoundingNecessaryException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 2
			path: src/Datacenter/Infrastructure/Finder/DbalDatacenterDetailFinder.php

		-
			message: '#^Method Cdn77\\Api\\Datacenter\\Infrastructure\\Finder\\DbalDatacenterDetailFinder\:\:findAllActiveForCustomer\(\) throws checked exception Doctrine\\DBAL\\Exception but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Datacenter/Infrastructure/Finder/DbalDatacenterDetailFinder.php

		-
			message: '#^Method Cdn77\\Api\\Datacenter\\Infrastructure\\Repository\\DoctrineLocationRepository\:\:findForId\(\) throws checked exception Doctrine\\ORM\\NonUniqueResultException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Datacenter/Infrastructure/Repository/DoctrineLocationRepository.php

		-
			message: '#^Method Cdn77\\Api\\Datacenter\\Infrastructure\\Repository\\DoctrineLocationRepository\:\:get\(\) throws checked exception Cdn77\\Api\\Datacenter\\Domain\\Exception\\LocationNotFound but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Datacenter/Infrastructure/Repository/DoctrineLocationRepository.php

		-
			message: '#^Method Cdn77\\Api\\GraphQL\\Application\\Container\\DataProviderCompilerPass\:\:process\(\) throws checked exception Symfony\\Component\\DependencyInjection\\Exception\\InvalidArgumentException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/GraphQL/Application/Container/DataProviderCompilerPass.php

		-
			message: '#^Method Cdn77\\Api\\GraphQL\\Application\\Container\\DataProviderCompilerPass\:\:process\(\) throws checked exception Symfony\\Component\\DependencyInjection\\Exception\\ServiceNotFoundException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/GraphQL/Application/Container/DataProviderCompilerPass.php

		-
			message: '#^Method Cdn77\\Api\\GraphQL\\Application\\Container\\FieldFactoryCompilerPass\:\:process\(\) throws checked exception Cdn77\\Api\\Core\\Domain\\Exception\\NotImplemented but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/GraphQL/Application/Container/FieldFactoryCompilerPass.php

		-
			message: '#^Method Cdn77\\Api\\GraphQL\\Application\\Container\\FieldFactoryCompilerPass\:\:process\(\) throws checked exception Symfony\\Component\\DependencyInjection\\Exception\\InvalidArgumentException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/GraphQL/Application/Container/FieldFactoryCompilerPass.php

		-
			message: '#^Method Cdn77\\Api\\GraphQL\\Application\\Container\\FieldFactoryCompilerPass\:\:process\(\) throws checked exception Symfony\\Component\\DependencyInjection\\Exception\\ServiceNotFoundException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/GraphQL/Application/Container/FieldFactoryCompilerPass.php

		-
			message: '#^Method Cdn77\\Api\\GraphQL\\Application\\Container\\TypeCompilerPass\:\:process\(\) throws checked exception Symfony\\Component\\DependencyInjection\\Exception\\InvalidArgumentException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/GraphQL/Application/Container/TypeCompilerPass.php

		-
			message: '#^Method Cdn77\\Api\\GraphQL\\Application\\Container\\TypeCompilerPass\:\:process\(\) throws checked exception Symfony\\Component\\DependencyInjection\\Exception\\ServiceNotFoundException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/GraphQL/Application/Container/TypeCompilerPass.php

		-
			message: '#^Method Cdn77\\Api\\GraphQL\\Application\\Controller\\GraphQLController\:\:createPsr7Request\(\) throws checked exception InvalidArgumentException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 2
			path: src/GraphQL/Application/Controller/GraphQLController.php

		-
			message: '#^Method Cdn77\\Api\\GraphQL\\Application\\Controller\\GraphQLController\:\:execute\(\) throws checked exception Exception but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/GraphQL/Application/Controller/GraphQLController.php

		-
			message: '#^Method Cdn77\\Api\\GraphQL\\Application\\Controller\\GraphQLController\:\:execute\(\) throws checked exception InvalidArgumentException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 2
			path: src/GraphQL/Application/Controller/GraphQLController.php

		-
			message: '#^Method Cdn77\\Api\\GraphQL\\Application\\Controller\\SchemaController\:\:execute\(\) throws checked exception GraphQL\\Error\\Error but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/GraphQL/Application/Controller/SchemaController.php

		-
			message: '#^Method Cdn77\\Api\\GraphQL\\Application\\Controller\\SchemaController\:\:execute\(\) throws checked exception GraphQL\\Error\\InvariantViolation but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/GraphQL/Application/Controller/SchemaController.php

		-
			message: '#^Method Cdn77\\Api\\GraphQL\\Application\\Controller\\SchemaController\:\:execute\(\) throws checked exception GraphQL\\Error\\SerializationError but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/GraphQL/Application/Controller/SchemaController.php

		-
			message: '#^Method Cdn77\\Api\\GraphQL\\Application\\Controller\\SchemaController\:\:execute\(\) throws checked exception InvalidArgumentException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/GraphQL/Application/Controller/SchemaController.php

		-
			message: '#^Method Cdn77\\Api\\GraphQL\\Application\\Controller\\SchemaController\:\:execute\(\) throws checked exception JsonException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/GraphQL/Application/Controller/SchemaController.php

		-
			message: '#^Method Cdn77\\Api\\GraphQL\\Domain\\Operation\\FieldSelectionExtractor\:\:resolveObjectType\(\) throws checked exception GraphQL\\Error\\InvariantViolation but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/GraphQL/Domain/Operation/FieldSelectionExtractor.php

		-
			message: '#^Method Cdn77\\Api\\Invoice\\Application\\Controller\\DownloadController\:\:execute\(\) throws checked exception Cdn77\\Api\\Core\\Domain\\Billing\\Exception\\XeroInvoiceNotFound but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Invoice/Application/Controller/DownloadController.php

		-
			message: '#^Method Cdn77\\Api\\Invoice\\Application\\Controller\\DownloadController\:\:execute\(\) throws checked exception Cdn77\\Api\\Core\\Domain\\Exception\\Customer\\CustomerNotFound but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Invoice/Application/Controller/DownloadController.php

		-
			message: '#^Method Cdn77\\Api\\Invoice\\Application\\Controller\\DownloadController\:\:execute\(\) throws checked exception Cdn77\\Api\\Invoice\\Domain\\Exception\\InvoiceNotFound but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Invoice/Application/Controller/DownloadController.php

		-
			message: '#^Method Cdn77\\Api\\Invoice\\Application\\Controller\\DownloadController\:\:execute\(\) throws checked exception InvalidArgumentException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 2
			path: src/Invoice/Application/Controller/DownloadController.php

		-
			message: '#^Method Cdn77\\Api\\Invoice\\Application\\Payload\\InvoiceItemSchema\:\:getInvoiceItemDetails\(\) throws checked exception Brick\\Math\\Exception\\DivisionByZeroException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Invoice/Application/Payload/InvoiceItemSchema.php

		-
			message: '#^Method Cdn77\\Api\\Invoice\\Application\\Payload\\InvoiceItemSchema\:\:getInvoiceItemDetails\(\) throws checked exception Brick\\Math\\Exception\\NumberFormatException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 2
			path: src/Invoice/Application/Payload/InvoiceItemSchema.php

		-
			message: '#^Method Cdn77\\Api\\Invoice\\Application\\Payload\\InvoiceItemSchema\:\:getInvoiceItemDetails\(\) throws checked exception Brick\\Math\\Exception\\RoundingNecessaryException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 2
			path: src/Invoice/Application/Payload/InvoiceItemSchema.php

		-
			message: '#^Method Cdn77\\Api\\Invoice\\Application\\Payload\\InvoiceItemSchema\:\:getInvoiceItemDetails\(\) throws checked exception Brick\\Money\\Exception\\UnknownCurrencyException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Invoice/Application/Payload/InvoiceItemSchema.php

		-
			message: '#^Method Cdn77\\Api\\Invoice\\Application\\Payload\\UnpaidInvoiceDetailSchema\:\:fromInvoice\(\) throws checked exception Brick\\Math\\Exception\\NumberFormatException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Invoice/Application/Payload/UnpaidInvoiceDetailSchema.php

		-
			message: '#^Method Cdn77\\Api\\Invoice\\Application\\Payload\\UnpaidInvoiceDetailSchema\:\:fromInvoice\(\) throws checked exception Brick\\Math\\Exception\\RoundingNecessaryException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Invoice/Application/Payload/UnpaidInvoiceDetailSchema.php

		-
			message: '#^Method Cdn77\\Api\\Invoice\\Application\\Payload\\UnpaidInvoiceDetailSchema\:\:fromInvoice\(\) throws checked exception Brick\\Money\\Exception\\UnknownCurrencyException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Invoice/Application/Payload/UnpaidInvoiceDetailSchema.php

		-
			message: '#^Method Cdn77\\Api\\Invoice\\Domain\\Dto\\DownloadableInvoice\:\:fromXeroInvoice\(\) throws checked exception XeroPHP\\Exception but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Invoice/Domain/Dto/DownloadableInvoice.php

		-
			message: '#^Method Cdn77\\Api\\Invoice\\Domain\\Dto\\DownloadableInvoice\:\:writeContent\(\) throws checked exception Safe\\Exceptions\\FilesystemException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 3
			path: src/Invoice/Domain/Dto/DownloadableInvoice.php

		-
			message: '#^Method Cdn77\\Api\\Invoice\\Domain\\Dto\\InvoiceDetails\:\:total\(\) throws checked exception Brick\\Math\\Exception\\MathException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 2
			path: src/Invoice/Domain/Dto/InvoiceDetails.php

		-
			message: '#^Method Cdn77\\Api\\Invoice\\Domain\\Dto\\InvoiceDetails\:\:total\(\) throws checked exception Brick\\Money\\Exception\\MoneyMismatchException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Invoice/Domain/Dto/InvoiceDetails.php

		-
			message: '#^Method Cdn77\\Api\\Invoice\\Domain\\InvoiceFactory\:\:createForPayment\(\) throws checked exception Brick\\Math\\Exception\\NumberFormatException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Invoice/Domain/InvoiceFactory.php

		-
			message: '#^Method Cdn77\\Api\\Invoice\\Domain\\InvoiceFactory\:\:createForPayment\(\) throws checked exception Brick\\Math\\Exception\\RoundingNecessaryException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Invoice/Domain/InvoiceFactory.php

		-
			message: '#^Method Cdn77\\Api\\Invoice\\Domain\\InvoiceFactory\:\:createForPayment\(\) throws checked exception Brick\\Money\\Exception\\UnknownCurrencyException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Invoice/Domain/InvoiceFactory.php

		-
			message: '#^Method Cdn77\\Api\\Invoice\\Domain\\Query\\FindInvoicesForCustomerHandler\:\:handle\(\) throws checked exception Cdn77\\Api\\Core\\Domain\\Exception\\Customer\\CustomerNotFound but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Invoice/Domain/Query/FindInvoicesForCustomerHandler.php

		-
			message: '#^Method Cdn77\\Api\\Invoice\\Domain\\Query\\GetContactDetailHandler\:\:handle\(\) throws checked exception Cdn77\\Api\\Core\\Domain\\Exception\\Customer\\CustomerNotFound but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Invoice/Domain/Query/GetContactDetailHandler.php

		-
			message: '#^Method Cdn77\\Api\\Invoice\\Infrastructure\\Finder\\DbalInvoiceCountFinder\:\:findForCustomerAndNumber\(\) throws checked exception Doctrine\\DBAL\\Exception but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Invoice/Infrastructure/Finder/DbalInvoiceCountFinder.php

		-
			message: '#^Method Cdn77\\Api\\Invoice\\Infrastructure\\Finder\\DbalInvoiceNumberFinder\:\:getNext\(\) throws checked exception Doctrine\\DBAL\\Exception but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 3
			path: src/Invoice/Infrastructure/Finder/DbalInvoiceNumberFinder.php

		-
			message: '#^Method Cdn77\\Api\\Invoice\\Infrastructure\\Finder\\DoctrineXeroContactCountFinder\:\:findForCustomer\(\) throws checked exception Doctrine\\DBAL\\Exception but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Invoice/Infrastructure/Finder/DoctrineXeroContactCountFinder.php

		-
			message: '#^Method Cdn77\\Api\\Invoice\\Infrastructure\\Repository\\DoctrineCountryRepository\:\:find\(\) throws checked exception Doctrine\\ORM\\NonUniqueResultException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Invoice/Infrastructure/Repository/DoctrineCountryRepository.php

		-
			message: '#^Method Cdn77\\Api\\Invoice\\Infrastructure\\Repository\\DoctrineCountryRepository\:\:findForIso\(\) throws checked exception Doctrine\\ORM\\NonUniqueResultException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Invoice/Infrastructure/Repository/DoctrineCountryRepository.php

		-
			message: '#^Method Cdn77\\Api\\Invoice\\Infrastructure\\Repository\\DoctrineCountryRepository\:\:get\(\) throws checked exception Cdn77\\Api\\Invoice\\Domain\\Exception\\CountryNotFound but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Invoice/Infrastructure/Repository/DoctrineCountryRepository.php

		-
			message: '#^Method Cdn77\\Api\\Invoice\\Infrastructure\\Repository\\DoctrineCountryRepository\:\:getForIso\(\) throws checked exception Cdn77\\Api\\Invoice\\Domain\\Exception\\CountryNotFound but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Invoice/Infrastructure/Repository/DoctrineCountryRepository.php

		-
			message: '#^Method Cdn77\\Api\\Invoice\\Infrastructure\\Repository\\DoctrineInvoiceCustomerRepository\:\:findForCustomer\(\) throws checked exception Doctrine\\ORM\\NonUniqueResultException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Invoice/Infrastructure/Repository/DoctrineInvoiceCustomerRepository.php

		-
			message: '#^Method Cdn77\\Api\\Invoice\\Infrastructure\\Repository\\DoctrineInvoiceCustomerRepository\:\:getForCustomer\(\) throws checked exception Cdn77\\Api\\Invoice\\Domain\\Exception\\InvoiceCustomerNotFound but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Invoice/Infrastructure/Repository/DoctrineInvoiceCustomerRepository.php

		-
			message: '#^Method Cdn77\\Api\\Invoice\\Infrastructure\\Repository\\DoctrineInvoiceCustomerRepository\:\:vatIdExists\(\) throws checked exception Doctrine\\DBAL\\Exception but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Invoice/Infrastructure/Repository/DoctrineInvoiceCustomerRepository.php

		-
			message: '#^Method Cdn77\\Api\\Invoice\\Infrastructure\\Repository\\DoctrineInvoiceRepository\:\:findForInvoiceNumber\(\) throws checked exception Doctrine\\ORM\\NonUniqueResultException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Invoice/Infrastructure/Repository/DoctrineInvoiceRepository.php

		-
			message: '#^Method Cdn77\\Api\\Invoice\\Infrastructure\\Repository\\DoctrineXeroContactRepository\:\:findForXeroUuid\(\) throws checked exception Doctrine\\ORM\\NonUniqueResultException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Invoice/Infrastructure/Repository/DoctrineXeroContactRepository.php

		-
			message: '#^Method Cdn77\\Api\\Invoice\\Infrastructure\\Repository\\DoctrineXeroContactRepository\:\:getForXeroUuid\(\) throws checked exception Cdn77\\Api\\Invoice\\Domain\\Exception\\XeroContactNotFound but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Invoice/Infrastructure/Repository/DoctrineXeroContactRepository.php

		-
			message: '#^Method Cdn77\\Api\\Job\\Domain\\Command\\PurgeAllHandler\:\:handle\(\) throws checked exception Cdn77\\Api\\Cdn\\Domain\\Exception\\CdnAccessNotAllowed but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Job/Domain/Command/PurgeAllHandler.php

		-
			message: '#^Method Cdn77\\Api\\Job\\Domain\\Command\\PurgeAllHandler\:\:handle\(\) throws checked exception Cdn77\\Api\\Core\\Domain\\NxgApi\\Exception\\NxgRequestFailed but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Job/Domain/Command/PurgeAllHandler.php

		-
			message: '#^Method Cdn77\\Api\\Job\\Domain\\Command\\PurgeAllHandler\:\:handle\(\) throws checked exception Cdn77\\Api\\Job\\Domain\\Exception\\PurgeAllDisabled but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Job/Domain/Command/PurgeAllHandler.php

		-
			message: '#^Method Cdn77\\Api\\Job\\Domain\\Query\\GetJobsForCdnHandler\:\:handle\(\) throws checked exception Cdn77\\Api\\Cdn\\Domain\\Exception\\CdnAccessNotAllowed but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Job/Domain/Query/GetJobsForCdnHandler.php

		-
			message: '#^Method Cdn77\\Api\\Job\\Domain\\Value\\JobType\:\:getSchemaSpec\(\) throws checked exception cebe\\openapi\\exceptions\\TypeErrorException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Job/Domain/Value/JobType.php

		-
			message: '#^Method Cdn77\\Api\\Job\\Domain\\Value\\Paths\:\:getPathItemSchemaSpec\(\) throws checked exception cebe\\openapi\\exceptions\\TypeErrorException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Job/Domain/Value/Paths.php

		-
			message: '#^Method Cdn77\\Api\\Job\\Domain\\Value\\Paths\:\:getSchemaSpec\(\) throws checked exception cebe\\openapi\\exceptions\\TypeErrorException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Job/Domain/Value/Paths.php

		-
			message: '#^Method Cdn77\\Api\\Job\\Infrastructure\\Finder\\DbalCustomerScheduledJobCountFinder\:\:getSince\(\) throws checked exception Doctrine\\DBAL\\Exception but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Job/Infrastructure/Finder/DbalCustomerScheduledJobCountFinder.php

		-
			message: '#^Method Cdn77\\Api\\Job\\Infrastructure\\Notify\\DbalJobChannelNotifier\:\:notify\(\) throws checked exception Doctrine\\DBAL\\Exception but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Job/Infrastructure/Notify/DbalJobChannelNotifier.php

		-
			message: '#^Method Cdn77\\Api\\Job\\Infrastructure\\Repository\\DoctrineJobRepository\:\:get\(\) throws checked exception Cdn77\\Api\\Job\\Domain\\Exception\\JobNotFound but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Job/Infrastructure/Repository/DoctrineJobRepository.php

		-
			message: '#^Method Cdn77\\Api\\Monitoring\\Overkill\\Application\\HttpFoundation\\MetricResponseCache\:\:get\(\) throws checked exception InvalidArgumentException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Monitoring/Overkill/Application/HttpFoundation/MetricResponseCache.php

		-
			message: '#^Method Cdn77\\Api\\Monitoring\\Overkill\\Domain\\Metric\\Registry\:\:registerCounter\(\) throws checked exception Prometheus\\Exception\\MetricsRegistrationException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Monitoring/Overkill/Domain/Metric/Registry.php

		-
			message: '#^Method Cdn77\\Api\\Monitoring\\Overkill\\Domain\\Metric\\Registry\:\:registerGauge\(\) throws checked exception Prometheus\\Exception\\MetricsRegistrationException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Monitoring/Overkill/Domain/Metric/Registry.php

		-
			message: '#^Method Cdn77\\Api\\Monitoring\\Overkill\\Infrastructure\\Finder\\ClickHouseCdnStatusCodeCountFinder\:\:all\(\) throws checked exception Psr\\Http\\Client\\ClientExceptionInterface but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Monitoring/Overkill/Infrastructure/Finder/ClickHouseCdnStatusCodeCountFinder.php

		-
			message: '#^Method Cdn77\\Api\\Monitoring\\Overkill\\Infrastructure\\Finder\\ClickHouseCdnStatusCodeCountFinder\:\:all\(\) throws checked exception SimPod\\ClickHouseClient\\Exception\\ServerError but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Monitoring/Overkill/Infrastructure/Finder/ClickHouseCdnStatusCodeCountFinder.php

		-
			message: '#^Method Cdn77\\Api\\Monitoring\\Overkill\\Infrastructure\\Finder\\DbalCdnIdCustomerFinder\:\:findForCustomerRatings\(\) throws checked exception Doctrine\\DBAL\\Exception but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Monitoring/Overkill/Infrastructure/Finder/DbalCdnIdCustomerFinder.php

		-
			message: '#^Method Cdn77\\Api\\MonthlyTrafficPlan\\Domain\\AvailableTrafficVolumeCalculator\:\:calculate\(\) throws checked exception Brick\\Math\\Exception\\DivisionByZeroException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 2
			path: src/MonthlyTrafficPlan/Domain/AvailableTrafficVolumeCalculator.php

		-
			message: '#^Method Cdn77\\Api\\MonthlyTrafficPlan\\Domain\\AvailableTrafficVolumeCalculator\:\:calculate\(\) throws checked exception Brick\\Math\\Exception\\NumberFormatException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 2
			path: src/MonthlyTrafficPlan/Domain/AvailableTrafficVolumeCalculator.php

		-
			message: '#^Method Cdn77\\Api\\MonthlyTrafficPlan\\Domain\\AvailableTrafficVolumeCalculator\:\:calculate\(\) throws checked exception Brick\\Math\\Exception\\RoundingNecessaryException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 2
			path: src/MonthlyTrafficPlan/Domain/AvailableTrafficVolumeCalculator.php

		-
			message: '#^Method Cdn77\\Api\\MonthlyTrafficPlan\\Domain\\AvailableTrafficVolumeCalculator\:\:calculate\(\) throws checked exception InvalidArgumentException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/MonthlyTrafficPlan/Domain/AvailableTrafficVolumeCalculator.php

		-
			message: '#^Method Cdn77\\Api\\MonthlyTrafficPlan\\Domain\\MonthlyTrafficPlanLogger\:\:storeActivePeriod\(\) throws checked exception Cdn77\\Api\\Core\\Domain\\Exception\\Customer\\CustomerNotFound but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/MonthlyTrafficPlan/Domain/MonthlyTrafficPlanLogger.php

		-
			message: '#^Method Cdn77\\Api\\MonthlyTrafficPlan\\Infrastructure\\Repository\\DoctrineCustomerMonthlyTrafficPlanRepository\:\:findLatestForCustomer\(\) throws checked exception Doctrine\\ORM\\NonUniqueResultException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/MonthlyTrafficPlan/Infrastructure/Repository/DoctrineCustomerMonthlyTrafficPlanRepository.php

		-
			message: '#^Method Cdn77\\Api\\MonthlyTrafficPlan\\Infrastructure\\Repository\\DoctrineMonthlyPlanGroupRepository\:\:findCurrent\(\) throws checked exception Doctrine\\ORM\\NoResultException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/MonthlyTrafficPlan/Infrastructure/Repository/DoctrineMonthlyPlanGroupRepository.php

		-
			message: '#^Method Cdn77\\Api\\MonthlyTrafficPlan\\Infrastructure\\Repository\\DoctrineMonthlyPlanGroupRepository\:\:findCurrent\(\) throws checked exception Doctrine\\ORM\\NonUniqueResultException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/MonthlyTrafficPlan/Infrastructure/Repository/DoctrineMonthlyPlanGroupRepository.php

		-
			message: '#^Method Cdn77\\Api\\MonthlyTrafficPlan\\Infrastructure\\Repository\\DoctrineMonthlyTrafficPlanRepository\:\:findForGroupAndTraffic\(\) throws checked exception Brick\\Math\\Exception\\MathException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/MonthlyTrafficPlan/Infrastructure/Repository/DoctrineMonthlyTrafficPlanRepository.php

		-
			message: '#^Method Cdn77\\Api\\ObjectStorage\\Application\\Console\\ChargeCustomers\:\:execute\(\) throws checked exception Doctrine\\DBAL\\Exception but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/ObjectStorage/Application/Console/ChargeCustomers.php

		-
			message: '#^Method Cdn77\\Api\\ObjectStorage\\Application\\Payload\\Request\\GetStatsSchema\:\:toDto\(\) throws checked exception Cdn77\\Api\\Core\\Domain\\Exception\\ObjectStorage\\InvalidBucketName but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/ObjectStorage/Application/Payload/Request/GetStatsSchema.php

		-
			message: '#^Method Cdn77\\Api\\ObjectStorage\\Infrastructure\\Provider\\ClickHouseCephStatsProvider\:\:getStats\(\) throws checked exception Psr\\Http\\Client\\ClientExceptionInterface but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/ObjectStorage/Infrastructure/Provider/ClickHouseCephStatsProvider.php

		-
			message: '#^Method Cdn77\\Api\\ObjectStorage\\Infrastructure\\Provider\\ClickHouseCephStatsProvider\:\:getStats\(\) throws checked exception SimPod\\ClickHouseClient\\Exception\\ServerError but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/ObjectStorage/Infrastructure/Provider/ClickHouseCephStatsProvider.php

		-
			message: '#^Method Cdn77\\Api\\ObjectStorage\\Infrastructure\\Provider\\ClickHouseCephStatsProvider\:\:getSum\(\) throws checked exception Psr\\Http\\Client\\ClientExceptionInterface but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/ObjectStorage/Infrastructure/Provider/ClickHouseCephStatsProvider.php

		-
			message: '#^Method Cdn77\\Api\\ObjectStorage\\Infrastructure\\Provider\\ClickHouseCephStatsProvider\:\:getSum\(\) throws checked exception SimPod\\ClickHouseClient\\Exception\\ServerError but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/ObjectStorage/Infrastructure/Provider/ClickHouseCephStatsProvider.php

		-
			message: '#^Method Cdn77\\Api\\Origin\\Application\\Console\\PruneSuspendedObjectStorageOrigins\:\:execute\(\) throws checked exception Cdn77\\Api\\Origin\\Domain\\Exception\\DeleteObjectStorageFailed but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Origin/Application/Console/PruneSuspendedObjectStorageOrigins.php

		-
			message: '#^Method Cdn77\\Api\\Origin\\Application\\Console\\SynchronizeObjectStorageClusters\:\:execute\(\) throws checked exception Cdn77\\Api\\Core\\Domain\\Exception\\ObjectStorage\\InvalidBucketName but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Origin/Application/Console/SynchronizeObjectStorageClusters.php

		-
			message: '#^Method Cdn77\\Api\\Origin\\Application\\Console\\SynchronizeObjectStorageClusters\:\:execute\(\) throws checked exception Cdn77\\Api\\Core\\Domain\\Exception\\ObjectStorage\\ObjectStorageUserNotFound but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Origin/Application/Console/SynchronizeObjectStorageClusters.php

		-
			message: '#^Method Cdn77\\Api\\Origin\\Application\\Console\\SynchronizeObjectStorageClusters\:\:execute\(\) throws checked exception Cdn77\\Api\\Core\\Domain\\Origin\\Exception\\OriginNotFound but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Origin/Application/Console/SynchronizeObjectStorageClusters.php

		-
			message: '#^Method Cdn77\\Api\\Origin\\Application\\Payload\\OriginDetailSchema\:\:getNoteSchemaSpec\(\) throws checked exception cebe\\openapi\\exceptions\\TypeErrorException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Origin/Application/Payload/OriginDetailSchema.php

		-
			message: '#^Method Cdn77\\Api\\Origin\\Domain\\Command\\CreateObjectStorageOriginHandler\:\:handle\(\) throws checked exception Cdn77\\Api\\Core\\Domain\\Exception\\Customer\\CustomerNotFound but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Origin/Domain/Command/CreateObjectStorageOriginHandler.php

		-
			message: '#^Method Cdn77\\Api\\Origin\\Domain\\Command\\SetTimeoutToOriginHandler\:\:handle\(\) throws checked exception Cdn77\\Api\\Core\\Domain\\NxgApi\\Exception\\NxgRequestFailed but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Origin/Domain/Command/SetTimeoutToOriginHandler.php

		-
			message: '#^Method Cdn77\\Api\\Origin\\Domain\\CredentialsFactory\:\:create\(\) throws checked exception Cdn77\\Api\\Core\\Domain\\Exception\\Customer\\CustomerNotFound but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Origin/Domain/CredentialsFactory.php

		-
			message: '#^Method Cdn77\\Api\\Origin\\Infrastructure\\Ceph\\S3ClientApi\:\:createBucket\(\) throws checked exception Exception but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Origin/Infrastructure/Ceph/S3ClientApi.php

		-
			message: '#^Method Cdn77\\Api\\Origin\\Infrastructure\\Finder\\DoctrineOriginConnectionCountFinder\:\:find\(\) throws checked exception Doctrine\\DBAL\\Exception but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Origin/Infrastructure/Finder/DoctrineOriginConnectionCountFinder.php

		-
			message: '#^Method Cdn77\\Api\\Payment\\Application\\Controller\\CompletePaymentController\:\:execute\(\) throws checked exception Doctrine\\DBAL\\Exception\\UniqueConstraintViolationException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Payment/Application/Controller/CompletePaymentController.php

		-
			message: '#^Method Cdn77\\Api\\Payment\\Application\\Controller\\CompletePaymentController\:\:execute\(\) throws checked exception Doctrine\\ORM\\Exception\\ORMException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Payment/Application/Controller/CompletePaymentController.php

		-
			message: '#^Method Cdn77\\Api\\Payment\\Application\\Controller\\CompletePaymentController\:\:execute\(\) throws checked exception InvalidArgumentException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 2
			path: src/Payment/Application/Controller/CompletePaymentController.php

		-
			message: '#^Method Cdn77\\Api\\Payment\\Application\\Payload\\Dto\\NewPayment\:\:fromSchema\(\) throws checked exception Brick\\Math\\Exception\\NumberFormatException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 2
			path: src/Payment/Application/Payload/Dto/NewPayment.php

		-
			message: '#^Method Cdn77\\Api\\Payment\\Application\\Payload\\Dto\\NewPayment\:\:fromSchema\(\) throws checked exception Brick\\Math\\Exception\\RoundingNecessaryException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 2
			path: src/Payment/Application/Payload/Dto/NewPayment.php

		-
			message: '#^Method Cdn77\\Api\\Payment\\Application\\Payload\\Dto\\NewPayment\:\:fromSchema\(\) throws checked exception Brick\\Money\\Exception\\UnknownCurrencyException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 3
			path: src/Payment/Application/Payload/Dto/NewPayment.php

		-
			message: '#^Method Cdn77\\Api\\Payment\\Application\\Payload\\PaymentSettings\\DetailSchema\:\:fromPaymentSettings\(\) throws checked exception Brick\\Math\\Exception\\MathException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Payment/Application/Payload/PaymentSettings/DetailSchema.php

		-
			message: '#^Method Cdn77\\Api\\Payment\\Console\\NotifyExpiringPaymentRecipes\:\:execute\(\) throws checked exception Cdn77\\Api\\Customer\\Domain\\Exception\\AccountFlagsNotFound but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Payment/Console/NotifyExpiringPaymentRecipes.php

		-
			message: '#^Method Cdn77\\Api\\Payment\\Domain\\Command\\AddCreditCardHandler\:\:handle\(\) throws checked exception Cdn77\\Api\\Payment\\Domain\\Exception\\DuplicateCreditCardFound but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Payment/Domain/Command/AddCreditCardHandler.php

		-
			message: '#^Method Cdn77\\Api\\Payment\\Domain\\Command\\AddCustomPlanPaymentHandler\:\:handle\(\) throws checked exception Cdn77\\Api\\Core\\Domain\\Exception\\Customer\\CustomerNotFound but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Payment/Domain/Command/AddCustomPlanPaymentHandler.php

		-
			message: '#^Method Cdn77\\Api\\Payment\\Domain\\Command\\AddMonthlyPlanPayment\:\:fromPaymentData\(\) throws checked exception Brick\\Math\\Exception\\NumberFormatException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Payment/Domain/Command/AddMonthlyPlanPayment.php

		-
			message: '#^Method Cdn77\\Api\\Payment\\Domain\\Command\\AddMonthlyPlanPayment\:\:fromPaymentData\(\) throws checked exception Brick\\Math\\Exception\\RoundingNecessaryException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Payment/Domain/Command/AddMonthlyPlanPayment.php

		-
			message: '#^Method Cdn77\\Api\\Payment\\Domain\\Command\\AddMonthlyPlanPayment\:\:fromPaymentData\(\) throws checked exception Brick\\Money\\Exception\\UnknownCurrencyException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Payment/Domain/Command/AddMonthlyPlanPayment.php

		-
			message: '#^Method Cdn77\\Api\\Payment\\Domain\\Command\\AddPAYGPayment\:\:fromPaymentData\(\) throws checked exception Brick\\Math\\Exception\\NumberFormatException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Payment/Domain/Command/AddPAYGPayment.php

		-
			message: '#^Method Cdn77\\Api\\Payment\\Domain\\Command\\AddPAYGPayment\:\:fromPaymentData\(\) throws checked exception Brick\\Math\\Exception\\RoundingNecessaryException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Payment/Domain/Command/AddPAYGPayment.php

		-
			message: '#^Method Cdn77\\Api\\Payment\\Domain\\Command\\AddPAYGPayment\:\:fromPaymentData\(\) throws checked exception Brick\\Money\\Exception\\UnknownCurrencyException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Payment/Domain/Command/AddPAYGPayment.php

		-
			message: '#^Method Cdn77\\Api\\Payment\\Domain\\Command\\AddPaymentHandler\:\:handle\(\) throws checked exception Cdn77\\Api\\Core\\Domain\\Exception\\Customer\\CustomerNotFound but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 2
			path: src/Payment/Domain/Command/AddPaymentHandler.php

		-
			message: '#^Method Cdn77\\Api\\Payment\\Domain\\Command\\AddWireTransferPayment\:\:fromNotification\(\) throws checked exception Cdn77\\Api\\Payment\\Domain\\Exception\\PaymentNotAuthorized but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Payment/Domain/Command/AddWireTransferPayment.php

		-
			message: '#^Method Cdn77\\Api\\Payment\\Domain\\Command\\AddWireTransferPaymentHandler\:\:handle\(\) throws checked exception Brick\\Math\\Exception\\NumberFormatException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Payment/Domain/Command/AddWireTransferPaymentHandler.php

		-
			message: '#^Method Cdn77\\Api\\Payment\\Domain\\Command\\AddWireTransferPaymentHandler\:\:handle\(\) throws checked exception Brick\\Math\\Exception\\RoundingNecessaryException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Payment/Domain/Command/AddWireTransferPaymentHandler.php

		-
			message: '#^Method Cdn77\\Api\\Payment\\Domain\\Command\\AddWireTransferPaymentHandler\:\:handle\(\) throws checked exception Brick\\Money\\Exception\\UnknownCurrencyException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Payment/Domain/Command/AddWireTransferPaymentHandler.php

		-
			message: '#^Method Cdn77\\Api\\Payment\\Domain\\Command\\AddWireTransferPaymentHandler\:\:handle\(\) throws checked exception Cdn77\\Api\\Payment\\Domain\\Exception\\UnsupportedPaymentMethod but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Payment/Domain/Command/AddWireTransferPaymentHandler.php

		-
			message: '#^Method Cdn77\\Api\\Payment\\Domain\\Command\\DeclareWireTransferPayment\:\:fromNotification\(\) throws checked exception Brick\\Math\\Exception\\NumberFormatException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Payment/Domain/Command/DeclareWireTransferPayment.php

		-
			message: '#^Method Cdn77\\Api\\Payment\\Domain\\Command\\DeclareWireTransferPayment\:\:fromNotification\(\) throws checked exception Brick\\Math\\Exception\\RoundingNecessaryException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Payment/Domain/Command/DeclareWireTransferPayment.php

		-
			message: '#^Method Cdn77\\Api\\Payment\\Domain\\Command\\DeclareWireTransferPayment\:\:fromNotification\(\) throws checked exception Brick\\Money\\Exception\\UnknownCurrencyException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Payment/Domain/Command/DeclareWireTransferPayment.php

		-
			message: '#^Method Cdn77\\Api\\Payment\\Domain\\Command\\DeclareWireTransferPayment\:\:fromNotification\(\) throws checked exception Cdn77\\Api\\Payment\\Domain\\Exception\\WireTransferNotCompleted but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Payment/Domain/Command/DeclareWireTransferPayment.php

		-
			message: '#^Method Cdn77\\Api\\Payment\\Domain\\Command\\DeclareWireTransferPaymentHandler\:\:executeWireTransferPayment\(\) throws checked exception Brick\\Math\\Exception\\NumberFormatException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Payment/Domain/Command/DeclareWireTransferPaymentHandler.php

		-
			message: '#^Method Cdn77\\Api\\Payment\\Domain\\Command\\DeclareWireTransferPaymentHandler\:\:executeWireTransferPayment\(\) throws checked exception Brick\\Math\\Exception\\RoundingNecessaryException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Payment/Domain/Command/DeclareWireTransferPaymentHandler.php

		-
			message: '#^Method Cdn77\\Api\\Payment\\Domain\\Command\\DeclareWireTransferPaymentHandler\:\:executeWireTransferPayment\(\) throws checked exception Brick\\Money\\Exception\\UnknownCurrencyException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Payment/Domain/Command/DeclareWireTransferPaymentHandler.php

		-
			message: '#^Method Cdn77\\Api\\Payment\\Domain\\Command\\EditCreditCardHandler\:\:handle\(\) throws checked exception Cdn77\\Api\\Payment\\Domain\\Exception\\PaymentRecipeNotFound but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Payment/Domain/Command/EditCreditCardHandler.php

		-
			message: '#^Method Cdn77\\Api\\Payment\\Domain\\Command\\EditPaymentSettingsHandler\:\:handle\(\) throws checked exception Cdn77\\Api\\Core\\Domain\\Exception\\Customer\\CustomerNotFound but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Payment/Domain/Command/EditPaymentSettingsHandler.php

		-
			message: '#^Method Cdn77\\Api\\Payment\\Domain\\Command\\RemoveCardInfoHandler\:\:handle\(\) throws checked exception Cdn77\\Api\\Core\\Domain\\Exception\\Customer\\CustomerNotFound but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Payment/Domain/Command/RemoveCardInfoHandler.php

		-
			message: '#^Method Cdn77\\Api\\Payment\\Domain\\Command\\RemoveCardInfoHandler\:\:handle\(\) throws checked exception Cdn77\\Api\\Payment\\Domain\\Exception\\PaymentRecipeNotFound but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Payment/Domain/Command/RemoveCardInfoHandler.php

		-
			message: '#^Method Cdn77\\Api\\Payment\\Domain\\Dto\\UpdatedPaymentSettings\:\:fromSchema\(\) throws checked exception Brick\\Math\\Exception\\NumberFormatException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Payment/Domain/Dto/UpdatedPaymentSettings.php

		-
			message: '#^Method Cdn77\\Api\\Payment\\Domain\\Dto\\UpdatedPaymentSettings\:\:fromSchema\(\) throws checked exception Brick\\Math\\Exception\\RoundingNecessaryException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Payment/Domain/Dto/UpdatedPaymentSettings.php

		-
			message: '#^Method Cdn77\\Api\\Payment\\Domain\\Dto\\UpdatedPaymentSettings\:\:fromSchema\(\) throws checked exception Brick\\Money\\Exception\\UnknownCurrencyException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Payment/Domain/Dto/UpdatedPaymentSettings.php

		-
			message: '#^Method Cdn77\\Api\\Payment\\Domain\\Dto\\WireTransferDTO\:\:fromPaygateClientData\(\) throws checked exception Cdn77\\Api\\Payment\\Domain\\Exception\\UnsupportedPaymentMethod but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Payment/Domain/Dto/WireTransferDTO.php

		-
			message: '#^Method Cdn77\\Api\\Payment\\Domain\\Exception\\InvalidRechargeLimit\:\:amountTooSmall\(\) throws checked exception Brick\\Math\\Exception\\MathException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Payment/Domain/Exception/InvalidRechargeLimit.php

		-
			message: '#^Method Cdn77\\Api\\Payment\\Domain\\MinimumPriceResolver\:\:execute\(\) throws checked exception Brick\\Math\\Exception\\NumberFormatException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 4
			path: src/Payment/Domain/MinimumPriceResolver.php

		-
			message: '#^Method Cdn77\\Api\\Payment\\Domain\\MinimumPriceResolver\:\:execute\(\) throws checked exception Brick\\Math\\Exception\\RoundingNecessaryException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 4
			path: src/Payment/Domain/MinimumPriceResolver.php

		-
			message: '#^Method Cdn77\\Api\\Payment\\Domain\\MinimumPriceResolver\:\:execute\(\) throws checked exception Brick\\Money\\Exception\\UnknownCurrencyException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 4
			path: src/Payment/Domain/MinimumPriceResolver.php

		-
			message: '#^Method Cdn77\\Api\\Payment\\Domain\\PaymentCustomerResolver\:\:get\(\) throws checked exception Cdn77\\Api\\Core\\Domain\\Exception\\Customer\\CustomerNotFound but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 2
			path: src/Payment/Domain/PaymentCustomerResolver.php

		-
			message: '#^Method Cdn77\\Api\\Payment\\Domain\\PaymentCustomerResolver\:\:get\(\) throws checked exception Cdn77\\Api\\Payment\\Domain\\Exception\\PaymentFailed but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Payment/Domain/PaymentCustomerResolver.php

		-
			message: '#^Method Cdn77\\Api\\Payment\\Domain\\PromoCodeBonusCalculator\:\:getPromoCodeBonus\(\) throws checked exception Brick\\Math\\Exception\\MathException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 4
			path: src/Payment/Domain/PromoCodeBonusCalculator.php

		-
			message: '#^Method Cdn77\\Api\\Payment\\Domain\\PromoCodeBonusCalculator\:\:getPromoCodeBonus\(\) throws checked exception Brick\\Math\\Exception\\NumberFormatException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Payment/Domain/PromoCodeBonusCalculator.php

		-
			message: '#^Method Cdn77\\Api\\Payment\\Domain\\PromoCodeBonusCalculator\:\:getPromoCodeBonus\(\) throws checked exception Brick\\Math\\Exception\\RoundingNecessaryException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Payment/Domain/PromoCodeBonusCalculator.php

		-
			message: '#^Method Cdn77\\Api\\Payment\\Domain\\PromoCodeBonusCalculator\:\:getPromoCodeBonus\(\) throws checked exception Brick\\Money\\Exception\\MoneyMismatchException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 2
			path: src/Payment/Domain/PromoCodeBonusCalculator.php

		-
			message: '#^Method Cdn77\\Api\\Payment\\Domain\\PromoCodeBonusCalculator\:\:getPromoCodeBonus\(\) throws checked exception Brick\\Money\\Exception\\UnknownCurrencyException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Payment/Domain/PromoCodeBonusCalculator.php

		-
			message: '#^Method Cdn77\\Api\\Payment\\Domain\\PromoCodeBonusCalculator\:\:getPromoCodeBonus\(\) throws checked exception InvalidArgumentException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Payment/Domain/PromoCodeBonusCalculator.php

		-
			message: '#^Method Cdn77\\Api\\Payment\\Infrastructure\\Finder\\DbalPaymentSumFinder\:\:findDailyForRange\(\) throws checked exception Brick\\Math\\Exception\\NumberFormatException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Payment/Infrastructure/Finder/DbalPaymentSumFinder.php

		-
			message: '#^Method Cdn77\\Api\\Payment\\Infrastructure\\Finder\\DbalPaymentSumFinder\:\:findDailyForRange\(\) throws checked exception Brick\\Math\\Exception\\RoundingNecessaryException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Payment/Infrastructure/Finder/DbalPaymentSumFinder.php

		-
			message: '#^Method Cdn77\\Api\\Payment\\Infrastructure\\Finder\\DbalPaymentSumFinder\:\:findDailyForRange\(\) throws checked exception Brick\\Money\\Exception\\UnknownCurrencyException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Payment/Infrastructure/Finder/DbalPaymentSumFinder.php

		-
			message: '#^Method Cdn77\\Api\\Payment\\Infrastructure\\Finder\\DoctrinePaymentCountFinder\:\:getForCustomer\(\) throws checked exception Doctrine\\DBAL\\Exception but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Payment/Infrastructure/Finder/DoctrinePaymentCountFinder.php

		-
			message: '#^Method Cdn77\\Api\\Payment\\Infrastructure\\Finder\\DoctrinePaymentRecipeCountFinder\:\:getForCustomer\(\) throws checked exception Doctrine\\DBAL\\Exception but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Payment/Infrastructure/Finder/DoctrinePaymentRecipeCountFinder.php

		-
			message: '#^Method Cdn77\\Api\\Payment\\Infrastructure\\Finder\\DoctrinePaymentRecipeCountFinder\:\:getForRecipeId\(\) throws checked exception Doctrine\\DBAL\\Exception but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Payment/Infrastructure/Finder/DoctrinePaymentRecipeCountFinder.php

		-
			message: '#^Method Cdn77\\Api\\Payment\\Infrastructure\\Repository\\DoctrineCustomPlanContractRepository\:\:findForInvoiceAndCustomer\(\) throws checked exception Doctrine\\ORM\\NonUniqueResultException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Payment/Infrastructure/Repository/DoctrineCustomPlanContractRepository.php

		-
			message: '#^Method Cdn77\\Api\\Payment\\Infrastructure\\Repository\\DoctrineInvoiceRepository\:\:findForPayment\(\) throws checked exception Doctrine\\ORM\\NonUniqueResultException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Payment/Infrastructure/Repository/DoctrineInvoiceRepository.php

		-
			message: '#^Method Cdn77\\Api\\Payment\\Infrastructure\\Repository\\DoctrinePaymentRecipeRepository\:\:findByRecipeId\(\) throws checked exception Doctrine\\ORM\\NonUniqueResultException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Payment/Infrastructure/Repository/DoctrinePaymentRecipeRepository.php

		-
			message: '#^Method Cdn77\\Api\\Payment\\Infrastructure\\Repository\\DoctrinePaymentRecipeRepository\:\:findDefaultForCustomer\(\) throws checked exception Doctrine\\ORM\\NonUniqueResultException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Payment/Infrastructure/Repository/DoctrinePaymentRecipeRepository.php

		-
			message: '#^Method Cdn77\\Api\\Payment\\Infrastructure\\Repository\\DoctrinePaymentRecipeRepository\:\:findForCustomerAndLabel\(\) throws checked exception Doctrine\\ORM\\NonUniqueResultException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Payment/Infrastructure/Repository/DoctrinePaymentRecipeRepository.php

		-
			message: '#^Method Cdn77\\Api\\Payment\\Infrastructure\\Repository\\DoctrinePaymentRecipeRepository\:\:get\(\) throws checked exception Cdn77\\Api\\Payment\\Domain\\Exception\\PaymentRecipeNotFound but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Payment/Infrastructure/Repository/DoctrinePaymentRecipeRepository.php

		-
			message: '#^Method Cdn77\\Api\\Payment\\Infrastructure\\Repository\\DoctrinePaymentRecipeRepository\:\:get\(\) throws checked exception Doctrine\\ORM\\NonUniqueResultException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Payment/Infrastructure/Repository/DoctrinePaymentRecipeRepository.php

		-
			message: '#^Method Cdn77\\Api\\Payment\\Infrastructure\\Repository\\DoctrinePaymentRecipeRepository\:\:getByRecipeId\(\) throws checked exception Cdn77\\Api\\Payment\\Domain\\Exception\\PaymentRecipeNotFound but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Payment/Infrastructure/Repository/DoctrinePaymentRecipeRepository.php

		-
			message: '#^Method Cdn77\\Api\\Payment\\Infrastructure\\Repository\\DoctrinePaymentRepository\:\:findLastForCustomer\(\) throws checked exception Doctrine\\ORM\\NonUniqueResultException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Payment/Infrastructure/Repository/DoctrinePaymentRepository.php

		-
			message: '#^Method Cdn77\\Api\\Payment\\Infrastructure\\Repository\\DoctrinePaymentRepository\:\:getForPaygateId\(\) throws checked exception Cdn77\\Api\\Payment\\Domain\\Exception\\PaymentNotFound but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Payment/Infrastructure/Repository/DoctrinePaymentRepository.php

		-
			message: '#^Method Cdn77\\Api\\Payment\\Infrastructure\\Repository\\DoctrinePaymentRepository\:\:getForPaygateId\(\) throws checked exception Doctrine\\ORM\\NonUniqueResultException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Payment/Infrastructure/Repository/DoctrinePaymentRepository.php

		-
			message: '#^Method Cdn77\\Api\\Payment\\Infrastructure\\Repository\\DoctrinePromoCodeRepository\:\:getUnusedForCustomer\(\) throws checked exception Doctrine\\ORM\\NonUniqueResultException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Payment/Infrastructure/Repository/DoctrinePromoCodeRepository.php

		-
			message: '#^Method Cdn77\\Api\\Plan\\Domain\\Command\\CloseMonthlyPlanHandler\:\:handle\(\) throws checked exception Cdn77\\Api\\MonthlyTrafficPlan\\Domain\\Exception\\CustomerMonthlyTrafficPlanNotFound but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Plan/Domain/Command/CloseMonthlyPlanHandler.php

		-
			message: '#^Method Cdn77\\Api\\Plan\\Infrastructure\\Repository\\DoctrinePlanRepository\:\:findForNextPeriod\(\) throws checked exception Doctrine\\ORM\\NonUniqueResultException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Plan/Infrastructure/Repository/DoctrinePlanRepository.php

		-
			message: '#^Method Cdn77\\Api\\Plan\\Infrastructure\\Repository\\DoctrinePlanRepository\:\:findLastForCustomer\(\) throws checked exception Doctrine\\ORM\\NonUniqueResultException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Plan/Infrastructure/Repository/DoctrinePlanRepository.php

		-
			message: '#^Method Cdn77\\Api\\Plan\\Infrastructure\\Repository\\DoctrinePlanRepository\:\:findLastMonthlyForCustomer\(\) throws checked exception Doctrine\\ORM\\NonUniqueResultException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Plan/Infrastructure/Repository/DoctrinePlanRepository.php

		-
			message: '#^Method Cdn77\\Api\\Plan\\Infrastructure\\Repository\\DoctrinePlanRepository\:\:get\(\) throws checked exception Cdn77\\Api\\Plan\\Domain\\Exception\\PlanNotFound but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Plan/Infrastructure/Repository/DoctrinePlanRepository.php

		-
			message: '#^Method Cdn77\\Api\\Rate\\Infrastructure\\Finder\\DoctrineRatesCountFinder\:\:findForCustomer\(\) throws checked exception Doctrine\\DBAL\\Exception but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Rate/Infrastructure/Finder/DoctrineRatesCountFinder.php

		-
			message: '#^Method Cdn77\\Api\\RawLog\\Application\\Controller\\DownloadController\:\:execute\(\) throws checked exception InvalidArgumentException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 2
			path: src/RawLog/Application/Controller/DownloadController.php

		-
			message: '#^Method Cdn77\\Api\\RawLog\\Domain\\Command\\ActivateRawLogHandler\:\:handle\(\) throws checked exception Brick\\Math\\Exception\\NumberFormatException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/RawLog/Domain/Command/ActivateRawLogHandler.php

		-
			message: '#^Method Cdn77\\Api\\RawLog\\Domain\\Command\\ActivateRawLogHandler\:\:handle\(\) throws checked exception Brick\\Math\\Exception\\RoundingNecessaryException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/RawLog/Domain/Command/ActivateRawLogHandler.php

		-
			message: '#^Method Cdn77\\Api\\RawLog\\Domain\\Command\\ActivateRawLogHandler\:\:handle\(\) throws checked exception Brick\\Money\\Exception\\UnknownCurrencyException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/RawLog/Domain/Command/ActivateRawLogHandler.php

		-
			message: '#^Method Cdn77\\Api\\RawLog\\Domain\\Command\\ActivateRawLogHandler\:\:handle\(\) throws checked exception Cdn77\\Api\\Cdn\\Domain\\Exception\\CdnNotFound but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/RawLog/Domain/Command/ActivateRawLogHandler.php

		-
			message: '#^Method Cdn77\\Api\\RawLog\\Domain\\Command\\ActivateRawLogHandler\:\:handle\(\) throws checked exception Cdn77\\Api\\RawLog\\Domain\\Exception\\ActivationStatusChangeFailed but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/RawLog/Domain/Command/ActivateRawLogHandler.php

		-
			message: '#^Method Cdn77\\Api\\RawLog\\Domain\\Dto\\LogLine\:\:fromArray\(\) throws checked exception OutOfBoundsException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 16
			path: src/RawLog/Domain/Dto/LogLine.php

		-
			message: '#^Method Cdn77\\Api\\RawLog\\Domain\\Dto\\RawLogFile\:\:getContent\(\) throws checked exception Safe\\Exceptions\\FilesystemException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 3
			path: src/RawLog/Domain/Dto/RawLogFile.php

		-
			message: '#^Method Cdn77\\Api\\RawLog\\Domain\\Dto\\RawLogFile\:\:getContent\(\) throws checked exception Safe\\Exceptions\\OutcontrolException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/RawLog/Domain/Dto/RawLogFile.php

		-
			message: '#^Method Cdn77\\Api\\RawLog\\Domain\\Query\\FindSampleLogsHandler\:\:getLogLines\(\) throws checked exception DivisionByZeroError but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/RawLog/Domain/Query/FindSampleLogsHandler.php

		-
			message: '#^Method Cdn77\\Api\\RawLog\\Domain\\Query\\FindSampleLogsHandler\:\:handle\(\) throws checked exception Cdn77\\Api\\Cdn\\Domain\\Exception\\CdnAccessNotAllowed but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/RawLog/Domain/Query/FindSampleLogsHandler.php

		-
			message: '#^Method Cdn77\\Api\\RawLog\\Domain\\Query\\FindSampleLogsHandler\:\:handle\(\) throws checked exception Cdn77\\Api\\Core\\Domain\\Exception\\Customer\\CustomerNotFound but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/RawLog/Domain/Query/FindSampleLogsHandler.php

		-
			message: '#^Method Cdn77\\Api\\RawLog\\Infrastructure\\FileSystem\\LocalRawLogFileSystem\:\:getFileSize\(\) throws checked exception Safe\\Exceptions\\FilesystemException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/RawLog/Infrastructure/FileSystem/LocalRawLogFileSystem.php

		-
			message: '#^Method Cdn77\\Api\\RawLog\\Infrastructure\\FileSystem\\LocalSampleLogFileSystem\:\:filesize\(\) throws checked exception Safe\\Exceptions\\FilesystemException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/RawLog/Infrastructure/FileSystem/LocalSampleLogFileSystem.php

		-
			message: '#^Method Cdn77\\Api\\RawLog\\Infrastructure\\FileSystem\\LocalSampleLogFileSystem\:\:parseSample\(\) throws checked exception DivisionByZeroError but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 2
			path: src/RawLog/Infrastructure/FileSystem/LocalSampleLogFileSystem.php

		-
			message: '#^Method Cdn77\\Api\\RawLog\\Infrastructure\\FileSystem\\LocalSampleLogFileSystem\:\:readSample\(\) throws checked exception DivisionByZeroError but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/RawLog/Infrastructure/FileSystem/LocalSampleLogFileSystem.php

		-
			message: '#^Method Cdn77\\Api\\RawLog\\Infrastructure\\FileSystem\\LocalSampleLogFileSystem\:\:readSample\(\) throws checked exception Safe\\Exceptions\\FilesystemException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/RawLog/Infrastructure/FileSystem/LocalSampleLogFileSystem.php

		-
			message: '#^Method Cdn77\\Api\\RawLog\\Infrastructure\\Finder\\DbalLocationIdFinder\:\:findAll\(\) throws checked exception Doctrine\\DBAL\\Exception but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/RawLog/Infrastructure/Finder/DbalLocationIdFinder.php

		-
			message: '#^Method Cdn77\\Api\\RawLog\\Infrastructure\\Repository\\DoctrineRawLogRepository\:\:findActiveForCdn\(\) throws checked exception Doctrine\\ORM\\NonUniqueResultException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/RawLog/Infrastructure/Repository/DoctrineRawLogRepository.php

		-
			message: '#^Method Cdn77\\Api\\RawLog\\Infrastructure\\Repository\\DoctrineRawLogRepository\:\:findForCdn\(\) throws checked exception Doctrine\\ORM\\NonUniqueResultException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/RawLog/Infrastructure/Repository/DoctrineRawLogRepository.php

		-
			message: '#^Method Cdn77\\Api\\RawLog\\Infrastructure\\Repository\\DoctrineRawLogRepository\:\:getActiveForCdn\(\) throws checked exception Cdn77\\Api\\RawLog\\Domain\\Exception\\ActiveRawLogNotFound but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/RawLog/Infrastructure/Repository/DoctrineRawLogRepository.php

		-
			message: '#^Method Cdn77\\Api\\Service\\PushZone\\Api\\ApiClient\:\:formatRequestPayload\(\) throws checked exception Cdn77\\Api\\Service\\PushZone\\Api\\Exception\\RequestFailed but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Service/PushZone/Api/ApiClient.php

		-
			message: '#^Method Cdn77\\Api\\Service\\PushZone\\Api\\ApiClient\:\:processResponse\(\) throws checked exception Cdn77\\Api\\Service\\PushZone\\Api\\Exception\\ResponseFailed but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 3
			path: src/Service/PushZone/Api/ApiClient.php

		-
			message: '#^Method Cdn77\\Api\\Service\\PushZone\\Api\\ApiClient\:\:processResponse\(\) throws checked exception RuntimeException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Service/PushZone/Api/ApiClient.php

		-
			message: '#^Method Cdn77\\Api\\Service\\PushZone\\Api\\ApiClient\:\:sendRequest\(\) throws checked exception Cdn77\\Api\\Service\\PushZone\\Api\\Exception\\RequestFailed but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Service/PushZone/Api/ApiClient.php

		-
			message: '#^Method Cdn77\\Api\\Service\\PushZone\\Api\\ApiClient\:\:sendRequest\(\) throws checked exception InvalidArgumentException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Service/PushZone/Api/ApiClient.php

		-
			message: '#^Method Cdn77\\Api\\Ssl\\Domain\\Command\\CreateSslHandler\:\:handle\(\) throws checked exception Cdn77\\Api\\Core\\Domain\\Exception\\Customer\\CustomerNotFound but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Ssl/Domain/Command/CreateSslHandler.php

		-
			message: '#^Method Cdn77\\Api\\Ssl\\Domain\\Command\\CreateSslHandler\:\:handle\(\) throws checked exception Cdn77\\Api\\Ssl\\Domain\\Exception\\SslAlreadyExists but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Ssl/Domain/Command/CreateSslHandler.php

		-
			message: '#^Method Cdn77\\Api\\Ssl\\Domain\\Query\\GetSslListHandler\:\:handle\(\) throws checked exception Cdn77\\Api\\Core\\Domain\\Exception\\Customer\\CustomerNotFound but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Ssl/Domain/Query/GetSslListHandler.php

		-
			message: '#^Method Cdn77\\Api\\Ssl\\Domain\\Validation\\CertificateValidator\:\:validate\(\) throws checked exception Cdn77\\Api\\Ssl\\Domain\\Exception\\CertificateExpired but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Ssl/Domain/Validation/CertificateValidator.php

		-
			message: '#^Method Cdn77\\Api\\Ssl\\Domain\\Validation\\CertificateValidator\:\:validate\(\) throws checked exception Cdn77\\Api\\Ssl\\Domain\\Exception\\CertificatePairDoesNotMatch but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Ssl/Domain/Validation/CertificateValidator.php

		-
			message: '#^Method Cdn77\\Api\\Ssl\\Domain\\Validation\\CertificateValidator\:\:validate\(\) throws checked exception Cdn77\\Api\\Ssl\\Domain\\Exception\\InvalidPrivateKey but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Ssl/Domain/Validation/CertificateValidator.php

		-
			message: '#^Method Cdn77\\Api\\Ssl\\Infrastructure\\Finder\\DbalSslAssignedCdnIdsFinder\:\:findForSslId\(\) throws checked exception Doctrine\\DBAL\\Exception but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Ssl/Infrastructure/Finder/DbalSslAssignedCdnIdsFinder.php

		-
			message: '#^Method Cdn77\\Api\\Ssl\\Infrastructure\\Validation\\DatabaseSslAccessValidator\:\:isAllowed\(\) throws checked exception Cdn77\\Api\\Core\\Domain\\Exception\\Customer\\CustomerNotFound but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Ssl/Infrastructure/Validation/DatabaseSslAccessValidator.php

		-
			message: '#^Method Cdn77\\Api\\Statistics\\Domain\\Query\\FindHistoricalUsageStatisticsForPeriodHandler\:\:handle\(\) throws checked exception Cdn77\\Api\\Core\\Domain\\Exception\\Customer\\CustomerNotFound but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Statistics/Domain/Query/FindHistoricalUsageStatisticsForPeriodHandler.php

		-
			message: '#^Method Cdn77\\Api\\Statistics\\Domain\\Query\\FindHistoricalUsageStatisticsForPeriodHandler\:\:handle\(\) throws checked exception Cdn77\\Api\\Storage\\Domain\\Exception\\StorageNotFound but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Statistics/Domain/Query/FindHistoricalUsageStatisticsForPeriodHandler.php

		-
			message: '#^Method Cdn77\\Api\\Statistics\\Domain\\StatisticsCollector\:\:collectServer\(\) throws checked exception Cdn77\\Api\\Statistics\\Domain\\Exception\\CollectingServerStatisticsFailed but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Statistics/Domain/StatisticsCollector.php

		-
			message: '#^Method Cdn77\\Api\\Statistics\\Infrastructure\\Repository\\DoctrineActualStatisticsRepository\:\:findForStorage\(\) throws checked exception Doctrine\\ORM\\NonUniqueResultException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Statistics/Infrastructure/Repository/DoctrineActualStatisticsRepository.php

		-
			message: '#^Method Cdn77\\Api\\Statistics\\Infrastructure\\Repository\\DoctrineActualStatisticsRepository\:\:getForStorage\(\) throws checked exception Cdn77\\Api\\Statistics\\Domain\\Exception\\StatisticsNotFound but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Statistics/Infrastructure/Repository/DoctrineActualStatisticsRepository.php

		-
			message: '#^Method Cdn77\\Api\\Stats\\Application\\Payload\\ChartDataSchema\:\:fromChartData\(\) throws checked exception Brick\\Math\\Exception\\MathException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Stats/Application/Payload/ChartDataSchema.php

		-
			message: '#^Method Cdn77\\Api\\Stats\\Application\\Payload\\ChartSeriesSchema\:\:fromChartSeries\(\) throws checked exception Brick\\Math\\Exception\\MathException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 2
			path: src/Stats/Application/Payload/ChartSeriesSchema.php

		-
			message: '#^Method Cdn77\\Api\\Stats\\Application\\Payload\\ContinentPercentileSchema\:\:fromContinentData\(\) throws checked exception Brick\\Math\\Exception\\MathException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Stats/Application/Payload/ContinentPercentileSchema.php

		-
			message: '#^Method Cdn77\\Api\\Stats\\Application\\Payload\\DatacenterPercentileSchema\:\:fromDatacenterData\(\) throws checked exception Brick\\Math\\Exception\\MathException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 2
			path: src/Stats/Application/Payload/DatacenterPercentileSchema.php

		-
			message: '#^Method Cdn77\\Api\\Stats\\Domain\\Aggregation\\AggregationResolver\:\:resolve\(\) throws checked exception Brick\\Math\\Exception\\MathException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 9
			path: src/Stats/Domain/Aggregation/AggregationResolver.php

		-
			message: '#^Method Cdn77\\Api\\Stats\\Domain\\BandwidthPercentileCalculator\:\:fromTotals\(\) throws checked exception Brick\\Math\\Exception\\DivisionByZeroException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 2
			path: src/Stats/Domain/BandwidthPercentileCalculator.php

		-
			message: '#^Method Cdn77\\Api\\Stats\\Domain\\BandwidthPercentileCalculator\:\:fromTotals\(\) throws checked exception Brick\\Math\\Exception\\MathException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 3
			path: src/Stats/Domain/BandwidthPercentileCalculator.php

		-
			message: '#^Method Cdn77\\Api\\Stats\\Domain\\BandwidthPercentileCalculator\:\:fromTotals\(\) throws checked exception Brick\\Math\\Exception\\NumberFormatException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 2
			path: src/Stats/Domain/BandwidthPercentileCalculator.php

		-
			message: '#^Method Cdn77\\Api\\Stats\\Domain\\BandwidthPercentileCalculator\:\:fromTotals\(\) throws checked exception Brick\\Math\\Exception\\RoundingNecessaryException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 3
			path: src/Stats/Domain/BandwidthPercentileCalculator.php

		-
			message: '#^Method Cdn77\\Api\\Stats\\Domain\\CacheStatsResolver\:\:resolve\(\) throws checked exception Cdn77\\Api\\Cdn\\Domain\\Exception\\CdnAccessNotAllowed but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Stats/Domain/CacheStatsResolver.php

		-
			message: '#^Method Cdn77\\Api\\Stats\\Domain\\CacheStatsResolver\:\:resolve\(\) throws checked exception Cdn77\\Api\\Core\\Domain\\Exception\\Customer\\CustomerNotFound but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Stats/Domain/CacheStatsResolver.php

		-
			message: '#^Method Cdn77\\Api\\Stats\\Domain\\CacheStatsResolver\:\:resolve\(\) throws checked exception Cdn77\\Api\\Stats\\Domain\\Exception\\CannotAggregateValuesForInterval but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Stats/Domain/CacheStatsResolver.php

		-
			message: '#^Method Cdn77\\Api\\Stats\\Domain\\Chart\\ChartDataConverter\:\:convertStats\(\) throws checked exception Cdn77\\Api\\Stats\\Domain\\Exception\\ChartDataConversionFailed but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Stats/Domain/Chart/ChartDataConverter.php

		-
			message: '#^Method Cdn77\\Api\\Stats\\Domain\\Chart\\EmptyTimestampsResolver\:\:resolve\(\) throws checked exception InvalidArgumentException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Stats/Domain/Chart/EmptyTimestampsResolver.php

		-
			message: '#^Method Cdn77\\Api\\Stats\\Domain\\DataTable\\TableDataConverter\:\:sumTotals\(\) throws checked exception Brick\\Math\\Exception\\DivisionByZeroException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Stats/Domain/DataTable/TableDataConverter.php

		-
			message: '#^Method Cdn77\\Api\\Stats\\Domain\\DataTable\\TableDataConverter\:\:sumTotals\(\) throws checked exception Brick\\Math\\Exception\\MathException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Stats/Domain/DataTable/TableDataConverter.php

		-
			message: '#^Method Cdn77\\Api\\Stats\\Domain\\DataTable\\TableDataConverter\:\:sumTotals\(\) throws checked exception Brick\\Math\\Exception\\NumberFormatException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Stats/Domain/DataTable/TableDataConverter.php

		-
			message: '#^Method Cdn77\\Api\\Stats\\Domain\\DataTable\\TableDataConverter\:\:sumTotals\(\) throws checked exception Brick\\Math\\Exception\\RoundingNecessaryException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Stats/Domain/DataTable/TableDataConverter.php

		-
			message: '#^Method Cdn77\\Api\\Stats\\Domain\\DataTable\\TimestampTotalsResolver\:\:resolve\(\) throws checked exception Brick\\Math\\Exception\\DivisionByZeroException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Stats/Domain/DataTable/TimestampTotalsResolver.php

		-
			message: '#^Method Cdn77\\Api\\Stats\\Domain\\DataTable\\TimestampTotalsResolver\:\:resolve\(\) throws checked exception Brick\\Math\\Exception\\MathException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 2
			path: src/Stats/Domain/DataTable/TimestampTotalsResolver.php

		-
			message: '#^Method Cdn77\\Api\\Stats\\Domain\\DataTable\\TimestampTotalsResolver\:\:resolve\(\) throws checked exception Brick\\Math\\Exception\\NumberFormatException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Stats/Domain/DataTable/TimestampTotalsResolver.php

		-
			message: '#^Method Cdn77\\Api\\Stats\\Domain\\DataTable\\TimestampTotalsResolver\:\:resolve\(\) throws checked exception Brick\\Math\\Exception\\RoundingNecessaryException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Stats/Domain/DataTable/TimestampTotalsResolver.php

		-
			message: '#^Method Cdn77\\Api\\Stats\\Domain\\Dto\\CdnSums\:\:fromCdnStats\(\) throws checked exception Brick\\Math\\Exception\\MathException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Stats/Domain/Dto/CdnSums.php

		-
			message: '#^Method Cdn77\\Api\\Stats\\Domain\\Dto\\DatacenterSums\:\:add\(\) throws checked exception Brick\\Math\\Exception\\MathException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Stats/Domain/Dto/DatacenterSums.php

		-
			message: '#^Method Cdn77\\Api\\Stats\\Domain\\Dto\\Stats\:\:fromBigDecimalData\(\) throws checked exception Brick\\Math\\Exception\\MathException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Stats/Domain/Dto/Stats.php

		-
			message: '#^Method Cdn77\\Api\\Stats\\Domain\\Factory\\ContinentStatsFactory\:\:create\(\) throws checked exception Brick\\Math\\Exception\\DivisionByZeroException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Stats/Domain/Factory/ContinentStatsFactory.php

		-
			message: '#^Method Cdn77\\Api\\Stats\\Domain\\Factory\\ContinentStatsFactory\:\:create\(\) throws checked exception Brick\\Math\\Exception\\MathException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Stats/Domain/Factory/ContinentStatsFactory.php

		-
			message: '#^Method Cdn77\\Api\\Stats\\Domain\\Factory\\ContinentStatsFactory\:\:create\(\) throws checked exception Brick\\Math\\Exception\\NumberFormatException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Stats/Domain/Factory/ContinentStatsFactory.php

		-
			message: '#^Method Cdn77\\Api\\Stats\\Domain\\Factory\\ContinentStatsFactory\:\:create\(\) throws checked exception Brick\\Math\\Exception\\RoundingNecessaryException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Stats/Domain/Factory/ContinentStatsFactory.php

		-
			message: '#^Method Cdn77\\Api\\Stats\\Domain\\Factory\\ContinentStatsFactory\:\:createSums\(\) throws checked exception Brick\\Math\\Exception\\MathException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Stats/Domain/Factory/ContinentStatsFactory.php

		-
			message: '#^Method Cdn77\\Api\\Stats\\Domain\\Resolver\\CommonStatsResolver\:\:resolve\(\) throws checked exception Cdn77\\Api\\Cdn\\Domain\\Exception\\CdnAccessNotAllowed but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Stats/Domain/Resolver/CommonStatsResolver.php

		-
			message: '#^Method Cdn77\\Api\\Stats\\Domain\\Resolver\\CommonStatsResolver\:\:resolve\(\) throws checked exception Cdn77\\Api\\Core\\Domain\\Exception\\Customer\\CustomerNotFound but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Stats/Domain/Resolver/CommonStatsResolver.php

		-
			message: '#^Method Cdn77\\Api\\Stats\\Domain\\Resolver\\CommonStatsResolver\:\:resolve\(\) throws checked exception Cdn77\\Api\\Stats\\Domain\\Exception\\CannotAggregateValuesForInterval but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Stats/Domain/Resolver/CommonStatsResolver.php

		-
			message: '#^Method Cdn77\\Api\\Stats\\Domain\\Resolver\\CommonStatsResolver\:\:resolve\(\) throws checked exception Cdn77\\Api\\Stats\\Domain\\Exception\\InvalidAggregationUnit but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Stats/Domain/Resolver/CommonStatsResolver.php

		-
			message: '#^Method Cdn77\\Api\\Stats\\Domain\\Validation\\CacheStatsAccessValidator\:\:validate\(\) throws checked exception Cdn77\\Api\\Core\\Domain\\Exception\\Customer\\CustomerNotFound but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Stats/Domain/Validation/CacheStatsAccessValidator.php

		-
			message: '#^Method Cdn77\\Api\\Stats\\Domain\\Validation\\CacheStatsAccessValidator\:\:validate\(\) throws checked exception Cdn77\\Api\\Stats\\Domain\\Exception\\StatsAccessNotAllowed but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Stats/Domain/Validation/CacheStatsAccessValidator.php

		-
			message: '#^Method Cdn77\\Api\\Stats\\Domain\\Value\\Aggregation\:\:getSchemaSpec\(\) throws checked exception cebe\\openapi\\exceptions\\TypeErrorException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Stats/Domain/Value/Aggregation.php

		-
			message: '#^Method Cdn77\\Api\\Stats\\Domain\\Value\\StatsType\:\:expectedSeriesNames\(\) throws checked exception Cdn77\\Api\\Core\\Domain\\Exception\\NotImplemented but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Stats/Domain/Value/StatsType.php

		-
			message: '#^Method Cdn77\\Api\\Stats\\Infrastructure\\Finder\\DbalCustomPlanCustomersIdFinder\:\:findWithBandwidth\(\) throws checked exception Doctrine\\DBAL\\Exception but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Stats/Infrastructure/Finder/DbalCustomPlanCustomersIdFinder.php

		-
			message: '#^Method Cdn77\\Api\\Stats\\Infrastructure\\Finder\\DbalTikTokCountryTrafficFinder\:\:findDailyRatiosForCountries\(\) throws checked exception Brick\\Math\\Exception\\DivisionByZeroException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Stats/Infrastructure/Finder/DbalTikTokCountryTrafficFinder.php

		-
			message: '#^Method Cdn77\\Api\\Stats\\Infrastructure\\Finder\\DbalTikTokCountryTrafficFinder\:\:findDailyRatiosForCountries\(\) throws checked exception Brick\\Math\\Exception\\NumberFormatException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Stats/Infrastructure/Finder/DbalTikTokCountryTrafficFinder.php

		-
			message: '#^Method Cdn77\\Api\\Stats\\Infrastructure\\Finder\\DbalTikTokCountryTrafficFinder\:\:findDailyRatiosForCountries\(\) throws checked exception Brick\\Math\\Exception\\RoundingNecessaryException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Stats/Infrastructure/Finder/DbalTikTokCountryTrafficFinder.php

		-
			message: '#^Method Cdn77\\Api\\Stats\\Infrastructure\\Finder\\DbalTikTokCountryTrafficFinder\:\:findDailyRatiosForCountries\(\) throws checked exception Doctrine\\DBAL\\Exception but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Stats/Infrastructure/Finder/DbalTikTokCountryTrafficFinder.php

		-
			message: '#^Method Cdn77\\Api\\Storage\\Domain\\Command\\CreateStorageHandler\:\:handle\(\) throws checked exception Cdn77\\Api\\Core\\Domain\\Exception\\Customer\\CustomerNotFound but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Storage/Domain/Command/CreateStorageHandler.php

		-
			message: '#^Method Cdn77\\Api\\Storage\\Domain\\Command\\CreateStorageHandler\:\:handle\(\) throws checked exception Cdn77\\Api\\Storage\\Domain\\Exception\\CouldNotCreateStorage but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 3
			path: src/Storage/Domain/Command/CreateStorageHandler.php

		-
			message: '#^Method Cdn77\\Api\\Storage\\Domain\\Command\\DeleteStorageHandler\:\:handle\(\) throws checked exception Cdn77\\Api\\Core\\Domain\\Exception\\Customer\\CustomerNotFound but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Storage/Domain/Command/DeleteStorageHandler.php

		-
			message: '#^Method Cdn77\\Api\\Storage\\Domain\\Command\\DeleteStorageHandler\:\:handle\(\) throws checked exception Cdn77\\Api\\Storage\\Domain\\Exception\\CouldNotDeleteStorage but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Storage/Domain/Command/DeleteStorageHandler.php

		-
			message: '#^Method Cdn77\\Api\\Storage\\Domain\\Command\\DeleteStorageHandler\:\:handle\(\) throws checked exception Cdn77\\Api\\Storage\\Domain\\Exception\\StorageNotFound but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Storage/Domain/Command/DeleteStorageHandler.php

		-
			message: '#^Method Cdn77\\Api\\Storage\\Domain\\Command\\EditStorageHandler\:\:handle\(\) throws checked exception Cdn77\\Api\\Core\\Domain\\Exception\\Customer\\CustomerNotFound but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Storage/Domain/Command/EditStorageHandler.php

		-
			message: '#^Method Cdn77\\Api\\Storage\\Domain\\Command\\EditStorageHandler\:\:handle\(\) throws checked exception Cdn77\\Api\\Storage\\Domain\\Exception\\StorageNotFound but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Storage/Domain/Command/EditStorageHandler.php

		-
			message: '#^Method Cdn77\\Api\\Storage\\Domain\\Query\\FindAccountStoragesHandler\:\:handle\(\) throws checked exception Cdn77\\Api\\Core\\Domain\\Exception\\Customer\\CustomerNotFound but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Storage/Domain/Query/FindAccountStoragesHandler.php

		-
			message: '#^Method Cdn77\\Api\\Storage\\Domain\\Query\\GetStorageByIdForCustomerHandler\:\:handle\(\) throws checked exception Cdn77\\Api\\Core\\Domain\\Exception\\Customer\\CustomerNotFound but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Storage/Domain/Query/GetStorageByIdForCustomerHandler.php

		-
			message: '#^Method Cdn77\\Api\\Storage\\Domain\\Query\\GetStorageByIdForCustomerHandler\:\:handle\(\) throws checked exception Cdn77\\Api\\Storage\\Domain\\Exception\\StorageNotFound but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Storage/Domain/Query/GetStorageByIdForCustomerHandler.php

		-
			message: '#^Method Cdn77\\Api\\Storage\\Domain\\Query\\UpdateStorageOnAllRemoteServersHandler\:\:handle\(\) throws checked exception Cdn77\\Api\\Storage\\Domain\\Exception\\ServersUpdateFailed but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Storage/Domain/Query/UpdateStorageOnAllRemoteServersHandler.php

		-
			message: '#^Method Cdn77\\Api\\Storage\\Domain\\SecretAllocator\:\:allocate\(\) throws checked exception Cdn77\\Api\\Storage\\Domain\\Exception\\SecretAllocationFailed but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Storage/Domain/SecretAllocator.php

		-
			message: '#^Method Cdn77\\Api\\Storage\\Domain\\UserNameAllocator\:\:allocate\(\) throws checked exception Cdn77\\Api\\Storage\\Domain\\Exception\\UserNameAllocationFailed but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Storage/Domain/UserNameAllocator.php

		-
			message: '#^Method Cdn77\\Api\\Storage\\Infrastructure\\Finder\\DoctrineStorageIdFinder\:\:findForCustomer\(\) throws checked exception Doctrine\\DBAL\\Exception but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Storage/Infrastructure/Finder/DoctrineStorageIdFinder.php

		-
			message: '#^Method Cdn77\\Api\\Storage\\Infrastructure\\Finder\\DoctrineStorageSecretFinder\:\:findForServer\(\) throws checked exception Doctrine\\DBAL\\Exception but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Storage/Infrastructure/Finder/DoctrineStorageSecretFinder.php

		-
			message: '#^Method Cdn77\\Api\\Storage\\Infrastructure\\Repository\\DoctrineStorageRepository\:\:findForCustomerAndLabel\(\) throws checked exception Doctrine\\ORM\\NonUniqueResultException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Storage/Infrastructure/Repository/DoctrineStorageRepository.php

		-
			message: '#^Method Cdn77\\Api\\Storage\\Infrastructure\\Repository\\DoctrineStorageRepository\:\:findForSecret\(\) throws checked exception Doctrine\\ORM\\NonUniqueResultException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Storage/Infrastructure/Repository/DoctrineStorageRepository.php

		-
			message: '#^Method Cdn77\\Api\\Storage\\Infrastructure\\Repository\\DoctrineStorageRepository\:\:findForStorageUsername\(\) throws checked exception Doctrine\\ORM\\NonUniqueResultException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Storage/Infrastructure/Repository/DoctrineStorageRepository.php

		-
			message: '#^Method Cdn77\\Api\\StorageLocation\\Infrastructure\\Repository\\DoctrineStorageServerRepository\:\:findPublic\(\) throws checked exception Cdn77\\Api\\StorageLocation\\Domain\\Exception\\InvalidStorageServer but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/StorageLocation/Infrastructure/Repository/DoctrineStorageServerRepository.php

		-
			message: '#^Method Cdn77\\Api\\StorageLocation\\Infrastructure\\Repository\\DoctrineStorageServerRepository\:\:get\(\) throws checked exception Cdn77\\Api\\StorageLocation\\Domain\\Exception\\CannotFindStorageServer but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/StorageLocation/Infrastructure/Repository/DoctrineStorageServerRepository.php

		-
			message: '#^Method Cdn77\\Api\\StorageLocation\\Infrastructure\\Repository\\DoctrineStorageServerRepository\:\:getPublic\(\) throws checked exception Cdn77\\Api\\StorageLocation\\Domain\\Exception\\CannotFindStorageServer but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/StorageLocation/Infrastructure/Repository/DoctrineStorageServerRepository.php

		-
			message: '#^Method Cdn77\\Api\\StoragePlan\\Application\\Payload\\StoragePlanDetailSchema\:\:__construct\(\) throws checked exception Brick\\Math\\Exception\\MathException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/StoragePlan/Application/Payload/StoragePlanDetailSchema.php

		-
			message: '#^Method Cdn77\\Api\\StoragePlan\\Application\\Payload\\StoragePlanSchema\:\:__construct\(\) throws checked exception Brick\\Math\\Exception\\MathException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/StoragePlan/Application/Payload/StoragePlanSchema.php

		-
			message: '#^Method Cdn77\\Api\\StoragePlan\\Application\\Payload\\UsageSchema\:\:fromTotalStorageUsage\(\) throws checked exception Brick\\Math\\Exception\\MathException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 2
			path: src/StoragePlan/Application/Payload/UsageSchema.php

		-
			message: '#^Method Cdn77\\Api\\StoragePlan\\Domain\\Query\\GetStorageUsageHandler\:\:handle\(\) throws checked exception Brick\\Math\\Exception\\MathException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 2
			path: src/StoragePlan/Domain/Query/GetStorageUsageHandler.php

		-
			message: '#^Method Cdn77\\Api\\StoragePlan\\Infrastructure\\Repository\\DoctrineStorageAddOnRepository\:\:findActiveForCustomer\(\) throws checked exception Doctrine\\ORM\\NonUniqueResultException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/StoragePlan/Infrastructure/Repository/DoctrineStorageAddOnRepository.php

		-
			message: '#^Method Cdn77\\Api\\StoragePlan\\Infrastructure\\Repository\\DoctrineStoragePlanRepository\:\:getFree\(\) throws checked exception Cdn77\\Api\\StoragePlan\\Domain\\Exception\\StoragePlanNotFound but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/StoragePlan/Infrastructure/Repository/DoctrineStoragePlanRepository.php

		-
			message: '#^Method Cdn77\\Api\\StoragePlan\\Infrastructure\\Repository\\DoctrineStoragePlanRepository\:\:getFree\(\) throws checked exception Doctrine\\ORM\\NonUniqueResultException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/StoragePlan/Infrastructure/Repository/DoctrineStoragePlanRepository.php

		-
			message: '#^Method Cdn77\\Api\\Tariff\\Domain\\CreditAvailabilityEstimateCalculator\:\:calculate\(\) throws checked exception Brick\\Math\\Exception\\MathException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 3
			path: src/Tariff/Domain/CreditAvailabilityEstimateCalculator.php

		-
			message: '#^Method Cdn77\\Api\\Tariff\\Domain\\CreditAvailabilityEstimateCalculator\:\:calculate\(\) throws checked exception Brick\\Money\\Exception\\MoneyMismatchException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Tariff/Domain/CreditAvailabilityEstimateCalculator.php

		-
			message: '#^Method Cdn77\\Api\\Tariff\\Domain\\CreditAvailabilityEstimateCalculator\:\:calculate\(\) throws checked exception InvalidArgumentException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Tariff/Domain/CreditAvailabilityEstimateCalculator.php

		-
			message: '#^Method Cdn77\\Api\\Tariff\\Domain\\Query\\GetAvailableCreditHandler\:\:handle\(\) throws checked exception Cdn77\\Api\\Core\\Domain\\Exception\\Customer\\CustomerNotFound but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Tariff/Domain/Query/GetAvailableCreditHandler.php

		-
			message: '#^Method Cdn77\\Api\\Tariff\\Domain\\Query\\GetAvailableCreditHandler\:\:handle\(\) throws checked exception Cdn77\\Api\\Tariff\\Domain\\Exception\\CreditBalanceNotAvailable but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Tariff/Domain/Query/GetAvailableCreditHandler.php

		-
			message: '#^Method Cdn77\\Api\\Tariff\\Infrastructure\\Finder\\DbalTariffStartDateFinder\:\:getForCustomer\(\) throws checked exception Cdn77\\Api\\Tariff\\Domain\\Exception\\NoActiveTariffFound but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Tariff/Infrastructure/Finder/DbalTariffStartDateFinder.php

		-
			message: '#^Method Cdn77\\Api\\Tariff\\Infrastructure\\Finder\\DbalTariffStartDateFinder\:\:getForCustomer\(\) throws checked exception Doctrine\\DBAL\\Exception but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Tariff/Infrastructure/Finder/DbalTariffStartDateFinder.php

		-
			message: '#^Method Cdn77\\Api\\Tariff\\Infrastructure\\Finder\\DoctrineTariffCountFinder\:\:getForCustomer\(\) throws checked exception Doctrine\\DBAL\\Exception but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: src/Tariff/Infrastructure/Finder/DoctrineTariffCountFinder.php

		-
			message: '#^Method Cdn77\\Api\\Tests\\Cdn\\Application\\Controller\\EditControllerTest\:\:getCurrentlyAssignedSslForCdn\(\) throws checked exception Doctrine\\ORM\\NonUniqueResultException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: tests/Cdn/Application/Controller/EditControllerTest.php

		-
			message: '#^Method Cdn77\\Api\\Tests\\Core\\Domain\\Billing\\Value\\VatRateTest\:\:fromFractionalProvider\(\) throws checked exception Brick\\Math\\Exception\\DivisionByZeroException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 2
			path: tests/Core/Domain/Billing/Value/VatRateTest.php

		-
			message: '#^Method Cdn77\\Api\\Tests\\Core\\Domain\\Billing\\Value\\VatRateTest\:\:fromFractionalProvider\(\) throws checked exception Brick\\Math\\Exception\\NumberFormatException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 2
			path: tests/Core/Domain/Billing/Value/VatRateTest.php

		-
			message: '#^Method Cdn77\\Api\\Tests\\Core\\Domain\\Billing\\Value\\VatRateTest\:\:fromFractionalProvider\(\) throws checked exception Brick\\Math\\Exception\\RoundingNecessaryException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 2
			path: tests/Core/Domain/Billing/Value/VatRateTest.php

		-
			message: '#^Method Cdn77\\Api\\Tests\\Core\\Domain\\Billing\\Value\\VatRateTest\:\:fromXeroInvoiceProvider\(\) throws checked exception Brick\\Math\\Exception\\DivisionByZeroException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 4
			path: tests/Core/Domain/Billing/Value/VatRateTest.php

		-
			message: '#^Method Cdn77\\Api\\Tests\\Core\\Domain\\Billing\\Value\\VatRateTest\:\:fromXeroInvoiceProvider\(\) throws checked exception Brick\\Math\\Exception\\NumberFormatException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 4
			path: tests/Core/Domain/Billing/Value/VatRateTest.php

		-
			message: '#^Method Cdn77\\Api\\Tests\\Core\\Domain\\Billing\\Value\\VatRateTest\:\:fromXeroInvoiceProvider\(\) throws checked exception Brick\\Math\\Exception\\RoundingNecessaryException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 4
			path: tests/Core/Domain/Billing/Value/VatRateTest.php

		-
			message: '#^Method Cdn77\\Api\\Tests\\CustomPlan\\Domain\\EstimatedTrafficCalculatorTest\:\:timeAndTrafficProvider\(\) throws checked exception Brick\\Math\\Exception\\DivisionByZeroException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 8
			path: tests/CustomPlan/Domain/EstimatedTrafficCalculatorTest.php

		-
			message: '#^Method Cdn77\\Api\\Tests\\CustomPlan\\Domain\\EstimatedTrafficCalculatorTest\:\:timeAndTrafficProvider\(\) throws checked exception Brick\\Math\\Exception\\NumberFormatException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 8
			path: tests/CustomPlan/Domain/EstimatedTrafficCalculatorTest.php

		-
			message: '#^Method Cdn77\\Api\\Tests\\CustomPlan\\Domain\\EstimatedTrafficCalculatorTest\:\:timeAndTrafficProvider\(\) throws checked exception Brick\\Math\\Exception\\RoundingNecessaryException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 8
			path: tests/CustomPlan/Domain/EstimatedTrafficCalculatorTest.php

		-
			message: '#^Method Cdn77\\Api\\Tests\\CustomPlan\\Domain\\Validation\\InvoiceConfigurationValidatorTest\:\:providerInvalidInvoiceConfiguration\(\) throws checked exception Brick\\Math\\Exception\\NumberFormatException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: tests/CustomPlan/Domain/Validation/InvoiceConfigurationValidatorTest.php

		-
			message: '#^Method Cdn77\\Api\\Tests\\CustomPlan\\Domain\\Validation\\InvoiceConfigurationValidatorTest\:\:providerInvalidInvoiceConfiguration\(\) throws checked exception Brick\\Math\\Exception\\RoundingNecessaryException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: tests/CustomPlan/Domain/Validation/InvoiceConfigurationValidatorTest.php

		-
			message: '#^Method Cdn77\\Api\\Tests\\CustomPlan\\Domain\\Validation\\InvoiceConfigurationValidatorTest\:\:providerInvalidInvoiceConfiguration\(\) throws checked exception Brick\\Money\\Exception\\UnknownCurrencyException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: tests/CustomPlan/Domain/Validation/InvoiceConfigurationValidatorTest.php

		-
			message: '#^Method Cdn77\\Api\\Tests\\CustomPlan\\Domain\\Validation\\InvoiceConfigurationValidatorTest\:\:providerValidInvoiceConfiguration\(\) throws checked exception Brick\\Math\\Exception\\NumberFormatException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 3
			path: tests/CustomPlan/Domain/Validation/InvoiceConfigurationValidatorTest.php

		-
			message: '#^Method Cdn77\\Api\\Tests\\CustomPlan\\Domain\\Validation\\InvoiceConfigurationValidatorTest\:\:providerValidInvoiceConfiguration\(\) throws checked exception Brick\\Math\\Exception\\RoundingNecessaryException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 3
			path: tests/CustomPlan/Domain/Validation/InvoiceConfigurationValidatorTest.php

		-
			message: '#^Method Cdn77\\Api\\Tests\\CustomPlan\\Domain\\Validation\\InvoiceConfigurationValidatorTest\:\:providerValidInvoiceConfiguration\(\) throws checked exception Brick\\Money\\Exception\\UnknownCurrencyException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 3
			path: tests/CustomPlan/Domain/Validation/InvoiceConfigurationValidatorTest.php

		-
			message: '#^Method Cdn77\\Api\\Tests\\GraphQL\\Application\\Runtime\\DefaultFieldResolverTest\:\:getResolveInfo\(\) throws checked exception GraphQL\\Error\\InvariantViolation but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: tests/GraphQL/Application/Runtime/DefaultFieldResolverTest.php

		-
			message: '#^Method Cdn77\\Api\\Tests\\ORMFixtures\\Payment\\PaymentFactory\:\:create\(\) throws checked exception Brick\\Math\\Exception\\NumberFormatException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: tests/ORMFixtures/Payment/PaymentFactory.php

		-
			message: '#^Method Cdn77\\Api\\Tests\\ORMFixtures\\Payment\\PaymentFactory\:\:create\(\) throws checked exception Brick\\Math\\Exception\\RoundingNecessaryException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: tests/ORMFixtures/Payment/PaymentFactory.php

		-
			message: '#^Method Cdn77\\Api\\Tests\\ORMFixtures\\Payment\\PaymentFactory\:\:create\(\) throws checked exception Brick\\Money\\Exception\\UnknownCurrencyException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: tests/ORMFixtures/Payment/PaymentFactory.php

		-
			message: '#^Method Cdn77\\Api\\Tests\\Statistics\\Console\\CollectRemoteServerStatisticsEndpointTest\:\:assertGetNewStatsDeleteRequest\(\) throws checked exception RuntimeException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: tests/Statistics/Console/CollectRemoteServerStatisticsEndpointTest.php

		-
			message: '#^Method Cdn77\\Api\\Tests\\Statistics\\Console\\CollectRemoteServerStatisticsEndpointTest\:\:assertGetNewStatsDetailRequest\(\) throws checked exception RuntimeException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: tests/Statistics/Console/CollectRemoteServerStatisticsEndpointTest.php

		-
			message: '#^Method Cdn77\\Api\\Tests\\Statistics\\Console\\CollectRemoteServerStatisticsEndpointTest\:\:assertGetNewStatsListRequest\(\) throws checked exception RuntimeException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: tests/Statistics/Console/CollectRemoteServerStatisticsEndpointTest.php

		-
			message: '#^Method Cdn77\\Api\\Tests\\Stats\\Domain\\BandwidthPercentileCalculatorTest\:\:providerTotals\(\) throws checked exception Brick\\Math\\Exception\\DivisionByZeroException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 5
			path: tests/Stats/Domain/BandwidthPercentileCalculatorTest.php

		-
			message: '#^Method Cdn77\\Api\\Tests\\Stats\\Domain\\BandwidthPercentileCalculatorTest\:\:providerTotals\(\) throws checked exception Brick\\Math\\Exception\\NumberFormatException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 5
			path: tests/Stats/Domain/BandwidthPercentileCalculatorTest.php

		-
			message: '#^Method Cdn77\\Api\\Tests\\Stats\\Domain\\BandwidthPercentileCalculatorTest\:\:providerTotals\(\) throws checked exception Brick\\Math\\Exception\\RoundingNecessaryException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 5
			path: tests/Stats/Domain/BandwidthPercentileCalculatorTest.php

		-
			message: '#^Method Cdn77\\Api\\Tests\\Storage\\Console\\UpdateRemoteServerEndpointTest\:\:assertRequest\(\) throws checked exception RuntimeException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: tests/Storage/Console/UpdateRemoteServerEndpointTest.php

		-
			message: '#^Method Cdn77\\Api\\Tests\\Tariff\\Domain\\CreditAvailabilityEstimateCalculatorTest\:\:providerCreditAndDateTime\(\) throws checked exception Brick\\Math\\Exception\\NumberFormatException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 5
			path: tests/Tariff/Domain/CreditAvailabilityEstimateCalculatorTest.php

		-
			message: '#^Method Cdn77\\Api\\Tests\\Tariff\\Domain\\CreditAvailabilityEstimateCalculatorTest\:\:providerCreditAndDateTime\(\) throws checked exception Brick\\Math\\Exception\\RoundingNecessaryException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 5
			path: tests/Tariff/Domain/CreditAvailabilityEstimateCalculatorTest.php

		-
			message: '#^Method Cdn77\\Api\\Tests\\Tariff\\Domain\\CreditAvailabilityEstimateCalculatorTest\:\:providerCreditAndDateTime\(\) throws checked exception Brick\\Money\\Exception\\UnknownCurrencyException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 5
			path: tests/Tariff/Domain/CreditAvailabilityEstimateCalculatorTest.php

		-
			message: '#^Method Cdn77\\Api\\Tests\\TestSuiteComplianceTest\:\:providerChecks\(\) throws checked exception Symfony\\Component\\Finder\\Exception\\DirectoryNotFoundException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: tests/TestSuiteComplianceTest.php

		-
			message: '#^Method Cdn77\\Api\\Tests\\Utils\\FakerFactory\:\:create\(\) throws checked exception OverflowException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: tests/Utils/FakerFactory.php

		-
			message: '#^Method Cdn77\\Api\\Tests\\Utils\\GraphQLAssertion\:\:noErrorsOccurred\(\) throws checked exception Psl\\Json\\Exception\\EncodeException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: tests/Utils/GraphQLAssertion.php

		-
			message: '#^Method Cdn77\\Api\\Tests\\Utils\\NoopPropertyAccessor\:\:isReadable\(\) throws checked exception Symfony\\Component\\PropertyAccess\\Exception\\NoSuchPropertyException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: tests/Utils/NoopPropertyAccessor.php

		-
			message: '#^Method Cdn77\\Api\\Tests\\Utils\\NoopPropertyAccessor\:\:isWritable\(\) throws checked exception Symfony\\Component\\PropertyAccess\\Exception\\NoSuchPropertyException but it''s missing from the PHPDoc @throws tag\.$#'
			identifier: missingType.checkedException
			count: 1
			path: tests/Utils/NoopPropertyAccessor.php
